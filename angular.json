{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"fuse": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@ngx-env/builder:application", "options": {"outputPath": "dist/fuse", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["apexcharts", "crypto-js/enc-utf8", "crypto-js/hmac-sha256", "crypto-js/enc-base64", "quill-delta"], "assets": ["src/assets", {"glob": "**/*", "input": "public"}, {"glob": "_redirects", "input": "src", "output": "/"}, {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "stylePreprocessorOptions": {"includePaths": ["src/@fuse/styles"]}, "styles": ["src/@fuse/styles/tailwind.scss", "src/@fuse/styles/themes.scss", "src/styles/vendors.scss", "src/@fuse/styles/main.scss", "src/styles/styles.scss", "src/styles/tailwind.scss", "node_modules/datatables.net-dt/css/jquery.dataTables.min.css", "node_modules/datatables.net-select-dt/css/select.dataTables.css", "node_modules/ngx-toastr/toastr.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/datatables.net/js/jquery.dataTables.min.js", "node_modules/datatables.net-select/js/dataTables.select.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "75kb", "maximumError": "90kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@ngx-env/builder:dev-server", "configurations": {"production": {"buildTarget": "fuse:build:production"}, "development": {"buildTarget": "fuse:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@ngx-env/builder:extract-i18n"}, "test": {"builder": "@ngx-env/builder:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "5176f5c9-1219-4442-ad5f-0a8b5784e4d8"}}