/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "compileOnSave": false,
    "compilerOptions": {
        "baseUrl": "./src",
        "outDir": "./dist/out-tsc",
        "esModuleInterop": true,
        "sourceMap": true,
        "declaration": false,
        "experimentalDecorators": true,
        "moduleResolution": "bundler",
        "importHelpers": true,
        "target": "ES2022",
        "module": "ES2022",
        "useDefineForClassFields": false,
        "lib": ["ES2022", "dom"]
    },
    "angularCompilerOptions": {
        "enableI18nLegacyMessageIdFormat": false
    }
}
