name: Build SCT Web

on:
  push:
    branches: [ "developer" ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: 20
        cache: 'npm'

    - name: Install dependencies
      run: npm install

    - name: Make envfile
      uses: SpicyPizza/create-envfile@v2.0
      with:
        envkey_NG_APP_BASE_URL: ${{ vars.BASE_URL }}
        envkey_NG_APP_BASE_VERSION: ASHA

    - name: Build Web
      run: npm run build

    - name: rsync deployments
      uses: burnett01/rsync-deployments@7.0.1
      with:
        switches: -rltvz --delete --omit-dir-times
        path: dist/fuse/browser/
        remote_path: /var/www/hrm-md/
        remote_host: ${{ secrets.DEPLOY_HOST }}
        remote_user: ${{ secrets.DEPLOY_USER }}
        remote_key: ${{ secrets.DEPLOY_KEY }}
