import { DateTime } from "luxon";

export function DateTimeToSQL(data: string, format: string) {
  const dateTime = DateTime.fromSQL(data).toFormat(format);
  return dateTime;
}

export function DateTimeToJSDate(data: Date, format: string) {
  const dateTime = DateTime.fromJSDate(data).toFormat(format);
  return dateTime;
}

export function DateTimeToISO(data: string, format: string) {
  const dateTime = DateTime.fromISO(data).toFormat(format);
  return dateTime;
}

export function removeEmpty(obj: any) {
  Object.keys(obj).forEach(key => {
    if (obj[key] && typeof obj[key] === 'object') {
      removeEmpty(obj[key]);
    } else if (obj[key] === null || obj[key] === '') {
      delete obj[key];
    }
  });
  return obj;
}

export function createFileFromBlob(blob: Blob) {
  const url = window.URL.createObjectURL(blob);
  window.open(url);
}

export function calculateHoursFromMinutes(minutes) {
  const hours = Math.floor(minutes / 60); // Whole hours
  const remainingMinutes = minutes % 60; // Remaining minutes
  return { hours, remainingMinutes };
}

export function mapDatasetToFieldList(fields: any[], dataset: any[]) {
  return dataset.map(entry => {
    const mappedEntry = {};
    fields.forEach(({ field }) => {
      mappedEntry[field] = entry[field] !== undefined ? entry[field] : null;
    });
    mappedEntry['raw'] = entry
    return mappedEntry;
  });
}
