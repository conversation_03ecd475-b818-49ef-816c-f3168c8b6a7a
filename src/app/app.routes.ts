import { Route } from '@angular/router';
import { initialDataResolver } from 'app/app.resolvers';
import { AuthGuard } from 'app/core/auth/guards/auth.guard';
import { NoAuthGuard } from 'app/core/auth/guards/noAuth.guard';
import { LayoutComponent } from 'app/layout/layout.component';
import { LandingHomeComponent } from './modules/landing/home/<USER>';

// @formatter:off
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
export const appRoutes: Route[] = [

    // Redirect empty path to '/example'
    {path: '', pathMatch : 'full', redirectTo: 'home'},

    // Redirect signed-in user to the '/example'
    //
    // After the user signs in, the sign-in page will redirect the user to the 'signed-in-redirect'
    // path. Below is another redirection for that path to redirect the user to the desired
    // location. This is a small convenience to keep all main routes together here on this file.
    {path: 'signed-in-redirect' , pathMatch : 'full', redirectTo: 'leave/leave-remainning'},


    // Auth routes for guests
    {
        path: '',
        canActivate: [NoAuthGuard],
        canActivateChild: [NoAuthGuard],
        component: LayoutComponent,
        data: {
            layout: 'empty'
        },
        children: [
            {path: 'confirmation-required', loadChildren: () => import('app/modules/auth/confirmation-required/confirmation-required.routes')},
            {path: 'forgot-password', loadChildren: () => import('app/modules/auth/forgot-password/forgot-password.routes')},
            {path: 'reset-password', loadChildren: () => import('app/modules/auth/reset-password/reset-password.routes')},
            {path: 'sign-in', loadChildren: () => import('app/modules/auth/sign-in/sign-in.routes')},
            {path: 'sign-up', loadChildren: () => import('app/modules/auth/sign-up/sign-up.routes')},
        ]
    },

    // Auth routes for authenticated users
    {
        path: '',
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        component: LayoutComponent,
        data: {
            layout: 'empty'
        },
        children: [
            {path: 'sign-out', loadChildren: () => import('app/modules/auth/sign-out/sign-out.routes')},
            {path: 'unlock-session', loadChildren: () => import('app/modules/auth/unlock-session/unlock-session.routes')}
        ]
    },

    // Landing routes
    {
        path: '',
        component: LayoutComponent,
        data: {
            layout: 'empty'
        },
        children: [
            {path: 'home', loadChildren: () => import('app/modules/landing/home/<USER>')},
        ]
    },

    // Admin routes
    {
        path: '',
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        component: LayoutComponent,
        resolve: {
            initialData: initialDataResolver
        },
        children: [
            {path: 'example', loadChildren: () => import('app/modules/admin/example/example.routes')},
            {path: 'employee', loadChildren: () => import('app/modules/hr/employee/page.routes')},
            {path: 'employee-type', loadChildren: () => import('app/modules/hr/employee-type/page.routes')},
            {path: 'leave-type', loadChildren: () => import('app/modules/hr/leave-type/page.routes')},
            {path: 'com-for-emp', loadChildren: () => import('app/modules/hr/com-for-emp/page.routes')},
            {path: 'company-holiday', loadChildren: () => import('app/modules/hr/company-holiday/page.routes')},
            {path: 'leave', loadChildren: () => import('app/modules/leave/leave.routes')},
            {path: 'ot', loadChildren: () => import('app/modules/ot/ot.routes')},
            {path: 'ot-air', loadChildren: () => import('app/modules/ot-air/ot-air.routes')},
            // {path: 'personnel-request', loadChildren: () => import('app/modules/personnel-request/personnel-request.routes')},
            {path: 'title', loadChildren: () => import('app/modules/hr/position/page.routes')},
            {path: 'group', loadChildren: () => import('app/modules/hr/group/page.routes')},
            {path: 'department', loadChildren: () => import('app/modules/hr/department/page.routes')},
            {path: 'level', loadChildren: () => import('app/modules/hr/level/page.routes')},
            {path: 'work-shift', loadChildren: () => import('app/modules/hr/work-shift/page.routes')},
            {path: 'level-type', loadChildren: () => import('app/modules/hr/level-type/page.routes')},
            {path: 'approval-emp', loadChildren: () => import('app/modules/hr/approve/page.routes')},
            {path: 'user', loadChildren: () => import('app/modules/admin/user/page.routes')},
            {path: 'checkbox', loadChildren: () => import('app/modules/hr/checkbox/page.routes')},
            {path: 'report', loadChildren: () => import('app/modules/report/page.routes')},
            {path: 'banner', loadChildren: () => import('app/modules/admin/banner/page.routes')},
            {path: 'admin-leave', loadChildren: () => import('app/modules/admin/leave/page.routes')},
            {path: 'admin-ot', loadChildren: () => import('app/modules/admin/ot/page.routes')},
            {path: 'admin-ot-air', loadChildren: () => import('app/modules/admin/ot-air/page.routes')},
            {path: 'time-attendance', loadChildren: () => import('app/modules/time-attendance/page.routes')},
            {path: 'zone', loadChildren: () => import('app/modules/hr/zone/page.routes')},
            {path: 'floor', loadChildren: () => import('app/modules/hr/floor/page.routes')},
            {path: 'project', loadChildren: () => import('app/modules/hr/project/page.routes')},
            {path: 'client-list', loadChildren: () => import('app/modules/admin/client-list/page.routes')},
            {path: 'recruitment', loadChildren: () => import('app/modules/hr/recruitment/page.routes')},
            {path: 'applicant', loadChildren: () => import('app/modules/hr/applicant/page.routes')},
            {path: 'personnel-request', loadChildren: () => import('app/modules/hr/staffing-request/page.routes')},
            {path: 'personnel-request-emp', loadChildren: () => import('app/modules/hr/staffing-request/page.routes')},
            {path: 'job-applicants', loadChildren: () => import('app/modules/hr/job-applicants/page.routes')},
            {path: 'check-in-area', loadChildren: () => import('app/modules/admin/site/page.routes')},
        ]
    }
];
