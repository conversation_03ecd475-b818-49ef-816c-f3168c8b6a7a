import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { DateTimeToSQL } from 'app/helper';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { ReportService } from '../../page.service';

@Component({
  selector: 'app-dialog-view-ot-air',
  standalone: true,
  imports: [
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './dialog-view.component.html',
  styleUrls: ['./dialog-view.component.scss']
})
export class DialogViewLeaveComponent implements OnInit {
  form: FormGroup;
  flashErrorMessage: string;
  flashMessage: 'success' | 'error' | null = null;
  isLoading: boolean = false;
  searchInputControl: FormControl = new FormControl();
  selectedProduct: any | null = null;
  filterForm: FormGroup;
  tagsEditMode: boolean = false;
  totalPrice: number;
  itemData: any[] = [
    {
      id: 1,
      leaveNo: "LV241101",
      fullName: 'Narongkorn Mankatalyou',
      leaveType: {
        name: 'Anual'
      },
      date: '2024-11-05',
      StartDate: '2024-11-10',
      EndDate: '2024-11-10',
      qtyDay: 1,
      status: 'open'
    },
    {
      id: 1,
      leaveNo: "LV241101",
      fullName: 'Narongkorn Mankatalyou',
      leaveType: {
        name: 'Anual'
      },
      date: '2024-11-05',
      StartDate: '2024-11-10',
      EndDate: '2024-11-10',
      qtyDay: 1,
      status: 'open'
    },
    {
      id: 1,
      leaveNo: "LV241101",
      fullName: 'Narongkorn Mankatalyou',
      leaveType: {
        name: 'Anual'
      },
      date: '2024-11-05',
      StartDate: '2024-11-10',
      EndDate: '2024-11-10',
      qtyDay: 1,
      status: 'open'
    },
  ];
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  constructor(
    public dialogRef: MatDialogRef<DialogViewLeaveComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _changeDetectorRef: ChangeDetectorRef,
    private _service: ReportService,
    private toastr: ToastrService,
    private fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder,
    private _matDialog: MatDialog
  ) {
    console.log(this.data.form);
    this.itemData = this.data.form

  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

  onClose() {
    this.dialogRef.close();
  }

  showFlashMessage(type: 'success' | 'error'): void {
    // Show the message
    this.flashMessage = type;

    // Mark for check
    this._changeDetectorRef.markForCheck();

    // Hide it after 3 seconds
    setTimeout(() => {
      this.flashMessage = null;
      // Mark for check
      this._changeDetectorRef.markForCheck();
    }, 3000);
  }

  formatDate(data: any) {
    const dateFormat = DateTimeToSQL(data, 'dd/MM/yyyy')
    return dateFormat
  }


  changeType(data: any) {
    switch (data) {
      case 'half_day_morning':
        return 'Half day morning';
      case 'half_day_afternoon':
        return 'Half day afternoon';
      case 'full_day':
        return 'Full day';
      case 'consecutive_full_day_and_morning':
        return 'Consecutive Full Day and Half Day Morning';
      case 'half_afternoon_consecutive':
        return 'Half Day Afternoon and Consecutive Full Day';
      case 'consecutive_full_day_and_both_half':
        return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
      default:
        return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
    }
  }

  changeStatus(status: any) {
    if (!status) {
      return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
    }
    switch (status) {
      case 'open':
        return 'On process';;
      case 'reject':
        return 'Rejected';
      case 'approved':
        return 'Approved';
      case 'cancel':
        return 'Canceled';
      default:
        return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
    }
  }
}
