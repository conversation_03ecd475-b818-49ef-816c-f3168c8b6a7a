<div class="max-h-240">
    <div class="flex flex-row justify-between border-b-2 border-gray-300">
        <div class="my-2 text-lg text-black font-bold">
            <h4>Leave Detail</h4>
        </div>
    </div>
    <div class="my-2 border-b-2 border-gray-300 overflow-auto">
        <table
            class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black my-5 ">
            <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                <tr class="border-[1px] border-black">
                    <th scope="col" class="px-2 py-2">
                        Leave No
                    </th>
                    <th scope="col" class="px-2 py-2">
                        Fullname
                    </th>
                    <th scope="col" class="px-2 py-2">
                        Leave Type
                    </th>
                    <th scope="col" class="px-2 py-2">
                        Request Date
                    </th>
                    <th scope="col" class="px-2 py-2 ">
                        Start Date
                    </th>
                    <th scope="col" class="px-2 py-2 ">
                        End Date
                    </th>
                    <th scope="col" class="px-2 py-2 ">
                        Leave Total
                    </th>
                    <th scope="col" class="px-2 py-2 ">
                        Status
                    </th>
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let item of itemData; let i = index">
                    <tr class="hover:bg-slate-100">
                        <td class="px-2 py-2 text-md text-center">{{item.code ?? '-'}}</td>
                        <td class="px-2 py-2 text-md text-left">{{item.employee?.firstname}} {{item.employee?.lastname}}</td>
                        <td class="px-2 py-2 text-md text-center">{{item.leaveType?.name}}</td>
                        <td class="px-2 py-2 text-md text-center">{{item.date | date : 'dd/MM/yyyy'}}</td>
                        <td class="px-2 py-2 text-md text-center">{{item.dateStart | date : 'dd/MM/yyyy'}}</td>
                        <td class="px-2 py-2 text-md text-center">{{item.dateEnd | date : 'dd/MM/yyyy'}}</td>
                        <td class="px-2 py-2 text-md text-center">{{item.qtyDay ?? 0}}</td>
                        <td class="px-2 py-2 text-md text-center">{{changeStatus(item.status)}}</td>
                    </tr>
                </ng-container>
                <ng-container *ngIf="itemData.length === 0">
                    <tr class="hover:bg-slate-100">
                        <td class="px-2 py-2 text-md text-center" colspan="8"> No data</td>

                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
 
    <div class="flex flex-row mt-2 justify-center">
        <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3 mb-2" mat-flat-button [color]="'accent'" (click)="onClose()">
                Close
            </button>
        </div>
    </div>
</div>