import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { DialogViewLeaveComponent } from './dialog-view-leave/dialog-view.component';
import { ReportService } from '../page.service';
import { LeaveTypeService } from 'app/modules/hr/leave-type/page.service';

@Component({
    selector: 'app-report-list-of-approver',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        MatIcon,
        RouterOutlet,
        MatButtonModule,
        MatFormFieldModule,
        MatSelectModule,
        ReactiveFormsModule,
    ],
    templateUrl: './list-of-approver.component.html'
})
export class ListOfApproverComponent implements OnInit {
    items: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    years: number[] = [];
    form: FormGroup;
    department; any;
    months: { value: number; name: string }[] = [
        { value: 1, name: 'January' },
        { value: 2, name: 'February' },
        { value: 3, name: 'March' },
        { value: 4, name: 'April' },
        { value: 5, name: 'May' },
        { value: 6, name: 'June' },
        { value: 7, name: 'July' },
        { value: 8, name: 'August' },
        { value: 9, name: 'September' },
        { value: 10, name: 'October' },
        { value: 11, name: 'November' },
        { value: 12, name: 'December' },
    ];
    leaveTypes :any[] = []
    constructor(
        private _fb: FormBuilder,
        private _activated: ActivatedRoute,
        private _matDialog: MatDialog,
        private _service: ReportService,
    ) {
        this.department = this._activated.snapshot.data.department
        this.leaveTypes = this._activated.snapshot.data.leaveType
        this.leaveTypes = this._activated.snapshot.data.leaveType
        this.items = this._activated.snapshot.data.listOfApprover
        this.years = this.getPastAndFutureYears()

    }

    ngOnInit(): void {

    }

    getPastYears(): number[] {
        const currentYear = new Date().getFullYear();
        const years = [];

        for (let i = 0; i <= 10; i++) {
            years.push(currentYear - i);
        }

        return years;
    }
    getPastAndFutureYears(): number[] {
      const currentYear = new Date().getFullYear();
      const years = [];

      for (let i = 1; i >= -2; i--) {
        years.push(currentYear + i);
      }

      return years;
    }

    GetReport() {
        let formValue = this.form.value
        console.log(formValue);

    }

    viewData(data: any) {
        console.log(data , 'id');

        this._service.getLeaveHistoryEmployee(data).subscribe((resp: any)=>{
            const dialogRef = this._matDialog.open(DialogViewLeaveComponent, {
                width: 'auto',
                maxHeight: '90vh',
                data: {
                    form: resp,
                }
            });
            dialogRef.afterClosed().subscribe(item => {
                if (item) {
                    // this.toastr.success('Successed')
                    // this.dialogRef.close(true)
                }
            });
        })

    }


}
