<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-col md:flex-row justify-between pb-2 my-5 gap-2 border-b-2 border-gray-300">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                    Summary of member leave request
                </h2>
            </div>
        </div>
        <div class="overflow-auto">
            <table
                class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
                <thead class="text-md text-gray-700 uppercase bg-blue-200 dark:bg-gray-700 dark:text-gray-400">
                    <tr class="border-[1px] border-black">
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Dep.</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Code</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Fullname</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900 ">Period</th>
                        <ng-container *ngFor="let leaveType of items[0]?.staff[0]?.leaveType">
                            <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">
                                <p>{{ leaveType?.name }} </p>
                                <p class="text-slate-500 font-normal text-xs">(Rem/Ent)</p>
                            </th>
                        </ng-container>

                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of items; let i = index">
                        <ng-container *ngFor="let staff of item?.staff">
                            <tr class="font-normal hover:bg-slate-100 cursor-pointer">
                                <td class="px-2 py-2 text-sm border-[1px] border-gray-900">{{ item.department}}</td>
                                <td class="px-2 py-2 text-sm border-[1px] border-gray-900">{{ staff?.code }}</td>
                                <td class="px-2 py-2 text-sm border-[1px] border-gray-900 text-nowrap cursor-pointer"
                                    (click)="viewData(staff.id)"><span class="underline text-blue-600 ">
                                        {{staff?.fullName }}</span> </td>
                                <td class="px-2 py-2 text-sm border-[1px] border-gray-900 text-nowrap">{{ staff?.start |
                                    date :
                                    'dd/MM/yyyy' }} - {{staff.end | date : 'dd/MM/yyyy'}}</td>
                                <ng-container *ngFor="let leave of staff.leaveType">
                                    <td class="px-2 py-2 text-sm border-[1px] border-gray-900">{{ leave.remain
                                        }}/{{leave.entitlement}}</td>
                                </ng-container>
                            </tr>
                        </ng-container>
                    </ng-container>
                    <ng-container *ngIf="this.items.length <= 0">
                        <!-- Row for Department -->
                        <tr class="font-bold">
                            <td class="px-2 py-2 text-sm border-[1px] border-gray-900 bg-blue-100" colspan="4">
                                No data
                            </td>
                        </tr>

                    </ng-container>
                </tbody>
            </table>
            <div class="flex flex-col justify-start gap-2">
                <p class="text-slate-900 font-normal text-xs">DEP. = Department</p>
                <p class="text-slate-900 font-normal text-xs">REM = Remain</p>
                <p class="text-slate-900 font-normal text-xs">ENT = Entitlement</p>
            </div>
        </div>
    </div>
</div>