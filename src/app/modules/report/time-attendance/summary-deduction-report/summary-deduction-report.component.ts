import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { StaffAutocompleteComponent } from 'app/modules/share/staff-autocomplete/staff-autocomplete.component';
import { ReportService } from '../../page.service';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import * as XLSX from 'xlsx';
import { createFileFromBlob } from 'app/modules/share/helper';
import { ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-report-summary-deduction-report',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterOutlet,
    MatIcon,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    StaffAutocompleteComponent,
    MatRadioModule,
    MatAutocompleteModule,
    MatInputModule
  ],
  templateUrl: './summary-deduction-report.component.html'
})
export class SummaryDeductionReport implements OnInit {
  items: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  years: number[] = [];
  form: FormGroup;
  employee: any;
  approver: any;
  department: any;
  months: { value: string; name: string }[] = [
    { value: '01', name: 'January' },
    { value: '02', name: 'February' },
    { value: '03', name: 'March' },
    { value: '04', name: 'April' },
    { value: '05', name: 'May' },
    { value: '06', name: 'June' },
    { value: '07', name: 'July' },
    { value: '08', name: 'August' },
    { value: '09', name: 'September' },
    { value: '10', name: 'October' },
    { value: '11', name: 'November' },
    { value: '12', name: 'December' },
  ];
  employeeFilter = new FormControl('');
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  approverFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);

  constructor(
    private _fb: FormBuilder,
    private _activated: ActivatedRoute,
    private _service: ReportService,
    private _toastr: ToastrService
  ) {
    this.department = this._activated.snapshot.data.department
    this.employee = this._activated.snapshot.data.employee
    this.approver = this._activated.snapshot.data.approver
    this.years = this.getPastAndFutureYears();

    this.filterEmployee.next(this.employee.slice());
    this.filterApprover.next(this.approver.slice());
  }

  ngOnInit(): void {
    const currentDate = new Date();
    const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // เพิ่ม 0 นำหน้า
    const currentYear = currentDate.getFullYear();
    this.form = this._fb.group({
      month: currentMonth,
      year: currentYear,
      departmentId: [[]],
      employeeId: null,
      headId: null,
      type: 'STD'
    })

    this.employeeFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => {
        this._filterBrand();
      });

    this.approverFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => {
        this._filterApprover();
      });

  }
  protected _onDestroy = new Subject<void>();
  protected _filterBrand() {
    if (!this.employee) {
      return;
    }
    let search = this.employeeFilter.value;

    if (!search) {
      this.filterEmployee.next(this.employee.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employee.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }

  protected _filterApprover() {
    if (!this.approver) {
      return;
    }
    let search = this.approverFilter.value;

    if (!search) {
      this.filterApprover.next(this.approver.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterApprover.next(
      this.approver.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }
  onSelect(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        employeeId: selectedData.id,
      });
      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }

  onSelectaApprver(event: any, type: any) {
    if (!event) {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        headId: selectedData.id,
      });
      this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }

  getPastYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i <= 10; i++) {
      years.push(currentYear - i);
    }
    return years;
  }
  getPastAndFutureYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let i = 1; i >= -2; i--) {
      years.push(currentYear + i);
    }

    return years;
  }
  GetReport() {
    let formValue = this.form.value
    console.log(this.form.value)
    this._service.getSummaryDeduction(this.form.value)
      .subscribe({
        next: (resp: any) => {
          this.items = resp.sort((a, b) => a.name.localeCompare(b.name))
        },
        error: (err: any) => {

        }
      })
  }

  handleValue(value: any) {
    console.log('value', value);
    this.employee = value
  }
  clearData() {
    this.employeeFilter.reset()
    this.form.reset();
    this.form.patchValue({
      departmentId: []
    })
    this.items = [];
  }

  convertTimeToHoursAndMinutes(time: number): string {
    const hours = Math.floor(time / 100);
    const minutes = time % 100;
    return `${hours}.${minutes} `;
  }
  export() {
    /* ดึงข้อมูลจากตาราง */
    let table = document.getElementById('excel-table') as HTMLTableElement;
    let rows = Array.from(table.rows).map(row =>
      Array.from(row.cells).map(cell => cell.innerText)
    );

    /* สร้าง WorkSheet */
    const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(rows);

    /* กำหนดช่วงของข้อมูล */
    const range = XLSX.utils.decode_range(ws['!ref']!);

    /* กำหนดสีให้ Header (แถวแรก) */
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C });
      if (ws[cellAddress]) {
        ws[cellAddress].s = {
          fill: { fgColor: { rgb: "B0C4DE" } }, // สีฟ้า
          font: { bold: true },
          alignment: { horizontal: "center" },
        };
      }
    }

    /* กำหนดสีให้แถวข้อมูล (สีเหลือง) */
    for (let R = 1; R <= range.e.r; ++R) {
      for (let C = 0; C <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        if (ws[cellAddress]) {
          ws[cellAddress].s = {
            fill: { fgColor: { rgb: "FFFFCC" } }, // สีเหลือง
          };
        }
      }
    }

    /* สร้าง Workbook */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    /* บันทึกเป็นไฟล์ */
    XLSX.writeFile(wb, 'Summary_Deduction_Report.xlsx');
  }

  hasValidEmployees(item: any): boolean {
    return item.employee.length > 0 && item.employee.some(staff => staff.total_deduct_min > 0);
  }

  exportExcel() {
    const path = 'excel/summary-deduction-report-excel'
    let formValue = this.form.value
    this._service.exportExcel(formValue, path).subscribe({
      next: (resp) => {
        createFileFromBlob(resp, `Summary Deduction Report.xlsx`)
        this._toastr.success('Successed')
      },
      error: (err) => {
        console.error(err)
        this._toastr.error('Please contact admin')
      }
    })
  }

}
