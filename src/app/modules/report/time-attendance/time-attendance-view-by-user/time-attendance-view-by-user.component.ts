import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute } from '@angular/router';
import { StaffAutocompleteComponent } from 'app/modules/share/staff-autocomplete/staff-autocomplete.component';
import { ReportService } from '../../page.service';
import { calculateHoursFromMinutes, mapDatasetToFieldList } from 'app/helper';
import { Min2HrsPipe } from 'app/min2-hrs.pipe';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { takeUntil, ReplaySubject, Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import * as XLSX from 'xlsx';
import { createFileFromBlob } from 'app/modules/share/helper';

@Component({
  selector: 'app-report-time-attendance-view-by-user',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIcon,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    StaffAutocompleteComponent,
         MatRadioModule,
            MatAutocompleteModule,
             MatInputModule,
  ],
  providers: [
    Min2HrsPipe
  ],
  templateUrl: './time-attendance-view-by-user.component.html'
})
export class TimeAttendanceViewByUserComponent implements OnInit {

  formFieldHelpers: string[] = ['fuse-mat-dense'];
  years: number[] = [];
  form: FormGroup;
  department: any;
  employee: any;
  months: { value: number; name: string }[] = [
    { value: 1, name: 'January' },
    { value: 2, name: 'February' },
    { value: 3, name: 'March' },
    { value: 4, name: 'April' },
    { value: 5, name: 'May' },
    { value: 6, name: 'June' },
    { value: 7, name: 'July' },
    { value: 8, name: 'August' },
    { value: 9, name: 'September' },
    { value: 10, name: 'October' },
    { value: 11, name: 'November' },
    { value: 12, name: 'December' },
  ];

  fields = []
  items = []
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  employeeFilter = new FormControl('');
  type: any;
  private reportService = inject(ReportService);
  userdata = JSON.parse(localStorage.getItem('user'))
  constructor(
    private _fb: FormBuilder,
    private _activated: ActivatedRoute,
    private _min2HrsPipe: Min2HrsPipe,
    private toastr: ToastrService,
    private _service: ReportService,
  ) {
    this.type = this._activated.snapshot.data.type
    this.department = this._activated.snapshot.data.department
    this.employee = this._activated.snapshot.data.employee
    this.years = this.getPastAndFutureYears();


  }

  ngOnInit(): void {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() เริ่มนับจาก 0 (0 = มกราคม)
    const currentYear = currentDate.getFullYear();
    this.form = this._fb.group({
      month: currentMonth,
      year: currentYear,
      departmentId: [0],
      employeeId: null,
    })

    if(this.type === 'staff') {
      this.form.patchValue({
        employeeId: this.userdata.id,
      })

      this.GetReport()
    }



    this.employeeFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => {
        this._filterBrand();
      });

  }

  getPastYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i <= 10; i++) {
      years.push(currentYear - i);
    }
    return years;
  }
  getPastAndFutureYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let i = 2; i >= -2; i--) {
      years.push(currentYear + i);
    }

    return years;
  }
  handleValue(value: any) {
    this.form.patchValue({
      employeeId: value?.id
    })

    this.GetReport();
  }
  onSelect(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        employeeId: selectedData.id,
      });
      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }

  GetReport() {
    const { year, month, employeeId } = this.form.value
    if(employeeId == null && this.type == 'admin'){
      this.toastr.error('Please select staff.')
    }
    else{
      this.reportService.getTimeAttendanceViewByUserReport(year, month, employeeId).subscribe({
        next: (resp: { data: any[], fields: any[] }) => {
          this.fields = resp.fields

          this.items = mapDatasetToFieldList(resp.fields, resp.data)

          for (const item of this.items) {
            item['tr.class'] = (item.raw.isWorkday == false || item.raw.isHoliday == true) ? 'bg-slate-200' : `bg-[${item.raw.color}]`
            item['calcHrs'] = this.transform(item['calcHrs'])
            item['workHrs'] = this.transform(item['workHrs'])
            // item['date'] = this._min2HrsPipe.transform(item['date'])
          }
        },
        error: (err) => {
          this.toastr.error(err.error.message[0])
      },
      })
    }

  }
  clearData() {
    this.employeeFilter.reset()
    this.form.reset();
    this.form.patchValue({
    departmentId: []
    })
    this.items = [];
  }


  transform(value: number, ...args: unknown[]): unknown {
    const { hours, remainingMinutes } = calculateHoursFromMinutes(value)
    
    if (hours != 0 || remainingMinutes != 0) {
      return `${hours}.${remainingMinutes.toString().padStart(2, "0")}`;
    } else {
      return '0.00'
    }
  }






  protected _filterBrand() {
    if (!this.employee) {
      return;
    }
    let search = this.employeeFilter.value;

    if (!search) {
      this.filterEmployee.next(this.employee.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employee.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }



 protected _onDestroy = new Subject<void>();
  export() {
      /* pass here the table id */
      let element = document.getElementById('excel-table');
      const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);

      /* generate workbook and add the worksheet */
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      /* save to file */
      XLSX.writeFile(wb, 'Time Attendance View By User.xlsx');
  }

  exportExcel() {
      const path = 'excel/time-attendance-view-by-user-excel'
      let formValue = this.form.value
   
      this._service.exportExcel(formValue, path).subscribe({
        next: (resp) => {
          createFileFromBlob(resp, `Time Attendance View By User.xlsx`)
          this.toastr.success('Successed')
        },
        error: (err) => {
          console.error(err)
          this.toastr.error('Please contact admin')
        }
      })
    }
}
