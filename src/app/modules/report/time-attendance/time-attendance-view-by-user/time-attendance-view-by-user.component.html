<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between pb-2 mb-5 gap-2 border-b-2 border-gray-300">
      <div>
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Time Attendance View By User
        </h2>
      </div>
      <div class="flex flex-row justify-end pb-2 my-2 gap-2">
        <button mat-flat-button [color]="'primary'" (click)="GetReport()">
            <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
            <span class="ml-2"> Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2"> Reset</span>
      </button>
        <button mat-flat-button [color]="'primary'" (click)="export()">
            <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
            <span class="ml-2">Download</span>
        </button>
    </div>
    </div>
    <form [formGroup]="form" class="border-b-2 border-gray-300">
      <div class="flex flex-col md:flex-row gap-2 my-2 justify-start">
        <div
        class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
          <mat-label>Year</mat-label>
            <mat-select placeholder="Select Year" formControlName="year">
                <mat-option *ngFor="let year of years" [value]="year">
                    {{year}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
        <div
              class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>Month</mat-label>
                  <mat-select placeholder="Select Month" formControlName="month">
                      <mat-option *ngFor="let month of months" [value]="month.value">
                          {{month.name}}
                      </mat-option>
                  </mat-select>
              </mat-form-field>
          </div>
          <div *ngIf="type === 'admin'"
          class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
            <mat-label>Department</mat-label>
              <mat-select placeholder="Select Department" formControlName="departmentId" multiple>
                  <mat-option *ngFor="let item of department" [value]="item.id">
                      {{item.name ?? '-'}}
                  </mat-option>
              </mat-select>
          </mat-form-field>
      </div>
      <div  *ngIf="type === 'admin'"
      class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-3/12">
      <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
          <mat-label>Staff</mat-label>
          <input
              matInput
              [formControl]="employeeFilter"
              [matAutocomplete]="AutoComplete"
              placeholder="Search Staff"
          />
          <mat-autocomplete
              #AutoComplete="matAutocomplete"
              (optionSelected)="onSelect($event.option.value, 'manual')">
              <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
              </mat-option>
          </mat-autocomplete>
      </mat-form-field>
  </div>
      </div>
    </form>

    <div class="overflow-auto">
      <table id="excel-table"
        class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
        <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
          <tr class="border-[1px] border-black">
            @for (field of fields; track $index) {
            <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">{{field.label}}</th>
            }
          </tr>
        </thead>
        <tbody>
          @for (item of items; track $index) {
          <tr [ngClass]="item['tr.class']">
            @for (field of fields; track $index) {
            <td class="px-2 py-2 text-md border-[1px] border-gray-900 whitespace-nowrap">{{item[field['field']]}}</td>
            }
          </tr>
          }
          <!-- <ng-container *ngFor="let item of items">
            <tr class="font-normal" [ngClass]="{
                            'bg-slate-200': item.day === 'Sat' || item.day === 'Sun',
                          }">
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ (item.date | date: 'dd/MM/yyyy') ?? ''}}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ item.day ?? ''}}
              </td>
              <td class="px-2 text-center  py-2 text-md border-[1px] border-gray-900">
                {{ item.timeIn ?? ''}}
              </td>
              <td class="px-2 py-2 text-center  text-md border-[1px] border-gray-900">
                {{ item.timeOut ?? ''}}
              </td>
              <td class="px-2 py-2 text-center  text-md border-[1px] border-gray-900">
                {{ (item.calcHrs | number : '1.2') ?? ''}}
              </td>
              <td class="px-2 py-2 text-center  text-md border-[1px] border-gray-900">
                {{ (item.workHrs | number : '1.2') ?? ''}}
              </td>
              <ng-container *ngFor="let leave of item.leaveType">
                <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                  {{ (leave.qtyDay | number : '1.1') ?? ''}}
                </td>
              </ng-container>
            </tr>
          </ng-container> -->
        </tbody>
      </table>
    </div>
  </div>
</div>
