<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-col md:flex-row justify-between pb-2 mb-5 gap-2 border-b-2 border-gray-300">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                    View Daily Check In
                </h2>
            </div>
            <div class="flex flex-col md:flex-row justify-end pb-2 my-2 gap-2">
                <button mat-flat-button [color]="'primary'" (click)="this.GetReport()">
                    <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
                    <span class="ml-2"> Search</span>
                </button>
                <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
                    <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
                    <span class="ml-2"> Reset</span>
                </button>
            </div>

        </div>
        <form [formGroup]="form">
            <div class="flex flex-col md:flex-row gap-2 my-2 justify-start border-b-2 border-gray-300">
                <div
                    class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Date</mat-label>
                        <input matInput [matDatepicker]="picker" placeholder="Select Date" formControlName="date">
                        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                </div>
                <div
                    class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Department</mat-label>
                        <mat-select [formControlName]="'departmentId'" placeholder="Select Department"
                            (selectionChange)="filterDepartEmployee()">
                            <mat-option *ngFor="let item of this.department;" value="{{item.id}}">
                                {{item.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div
                    class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-3/12">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                        <mat-label>Staff</mat-label>
                        <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoComplete"
                            placeholder="Search Staff" />
                        <mat-autocomplete #AutoComplete="matAutocomplete"
                            (optionSelected)="onSelect($event.option.value, 'manual')">
                            <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                                {{item.firstname}} {{item.lastname}}
                            </mat-option>
                        </mat-autocomplete>
                    </mat-form-field>
                </div>
            </div>
        </form>
        <div class="overflow-auto mt-4">
            <table
                class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
                <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                    <tr class="border-[1px] border-black">
                        <th scope="col" class="px-2 py-2 border-[1px] w-2/12 border-gray-900 text-center">User ID</th>
                        <th scope="col" class="px-2 py-2 border-[1px] w-4/12 border-gray-900 text-center">User Name</th>
                        <th scope="col" class="px-2 py-2 border-[1px] w-2/12 border-gray-900 text-center">Date</th>
                        <th scope="col" class="px-2 py-2 border-[1px] w-1/12 border-gray-900 text-center">Time In</th>
                        <th scope="col" class="px-2 py-2 border-[1px] w-1/12 border-gray-900 text-center">Image in</th>
                        <th scope="col" class="px-2 py-2 border-[1px] w-1/12 border-gray-900 text-center">Time Out</th>
                        <th scope="col" class="px-2 py-2 border-[1px] w-1/12 border-gray-900 text-center">Image Out</th>

                        <!-- <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Train</th> -->
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of items">
                        <!-- Row for Department -->
                        <tr class="font-normal hover:bg-slate-50">
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                                {{ item.code ?? ''}}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                                {{ item.firstname ?? ''}} {{ item.lastname ?? ''}}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                                {{ (item.date | date: 'dd/MM/yyyy') ?? ''}}
                            </td>
                            <td class="px-2 text-center  py-2 text-md border-[1px] border-gray-900">
                                {{ item.min ?? ''}}
                            </td>
                            <td class="px-2 py-2 text-center  text-md border-[1px] border-gray-900">
                                <button mat-menu-item (click)="openDialogViewImage(item.inImage)"
                                [disabled]="!item.inImage">
                                    <mat-icon svgIcon="feather:image"></mat-icon>
                                </button>
                            </td>
                            <td class="px-2 py-2 text-center  text-md border-[1px] border-gray-900">
                                {{ item.max ?? ''}}
                            </td>
                            <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                                <button mat-menu-item 
                                        (click)="openDialogViewImage(item.outImage)" 
                                        [disabled]="!item.outImage">
                                  <mat-icon svgIcon="feather:image"></mat-icon>
                                </button>
                              </td>

                        </tr>
                        <!-- Row for Each Staff -->
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>
</div>
<ng-template #btNg let-data="adtData">
    <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
    </button>
    <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="openDialogEdit(data)">
            <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
            <span>Edit</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="clickDelete(data.id)">
            <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
            <span>Delete</span>
        </button>
    </mat-menu>
</ng-template>