import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { StaffAutocompleteComponent } from 'app/modules/share/staff-autocomplete/staff-autocomplete.component';
import { ReportService } from '../../page.service';
import { DateTime } from 'luxon';
import { ToastrService } from 'ngx-toastr';
import { createFileFromBlob } from 'app/modules/share/helper';
import { environment } from 'environments/environment';
import { slice } from 'lodash';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ViewImageComponent } from './view-image/dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-report-view-daily-raw-transactions-check-in',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterOutlet,
    MatIcon,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    StaffAutocompleteComponent,
    MatAutocompleteModule,
    MatInputModule,
    MatDatepickerModule
  ],
  templateUrl: './view-daily-raw-transactions-check-in.component.html'
})
export class ViewDailyRawTransactionsCheckInComponent implements OnInit {
  items: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  years: number[] = [];
  form: FormGroup;
  department: any;
  employee: any;
  months: { value: number; name: string }[] = [
    { value: 1, name: 'January' },
    { value: 2, name: 'February' },
    { value: 3, name: 'March' },
    { value: 4, name: 'April' },
    { value: 5, name: 'May' },
    { value: 6, name: 'June' },
    { value: 7, name: 'July' },
    { value: 8, name: 'August' },
    { value: 9, name: 'September' },
    { value: 10, name: 'October' },
    { value: 11, name: 'November' },
    { value: 12, name: 'December' },
  ];
  employeeFilter = new FormControl('');
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  constructor(
    private _fb: FormBuilder,
    private _activated: ActivatedRoute,
    private _service: ReportService,
    private _toastr: ToastrService,
    private dialog: MatDialog
  ) {
    this.department = this._activated.snapshot.data.department
    this.employee = this._activated.snapshot.data.employee
    this.years = this.getPastYears();
  }

  ngOnInit(): void {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() เริ่มนับจาก 0 (0 = มกราคม)
    const currentYear = currentDate.getFullYear();
    this.form = this._fb.group({
      date: null,
      departmentId: null,
      employeeId: null
    })

    this.employeeFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => {
        this._filterBrand();
      });
  }

  getPastYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i <= 10; i++) {
      years.push(currentYear - i);
    }
    return years;
  }

  GetReport() {
    if (this.form.value.date === '' || this.form.value.date === null) {
      this._toastr.error("Please select date")
    } else {
      let formValue = this.form.value
      formValue.date = DateTime.fromISO(this.form.value.date).toFormat('yyyy-MM-dd')
      this._service.getViewDailyRawTransactionsWeb(formValue.date, formValue.departmentId, formValue.employeeId).subscribe((resp: any) => {

        this.items = resp.data.sort((a, b) => {
          const nameA = `${a?.firstname || ''} ${a?.lastname || ''}`.toLowerCase();
          const nameB = `${b?.firstname || ''} ${b?.lastname || ''}`.toLowerCase();
          return nameA.localeCompare(nameB);
        });
      })
    }
  }

  handleValue(value: any) {
    this.form.patchValue({
      employeeId: value.id
    })
    this.GetReport()
  }

  filterDepartEmployee() {
    const emp = this._activated.snapshot.data.employee
    this.employee = emp.filter(item => item.department?.id === +this.form.value.departmentId)

    // this.GetReport()
  }

  clearData() {
    this.employeeFilter.reset()
    this.form.reset()
    this.employee = []
  }

  downloadExcel() {
    if (this.form.value.date === '' || this.form.value.date === null) {
      this._toastr.error("Please select date")
    } else {

      this._service.excelViewDailyRawTransactions({
        date: this.form.value.date, employeeId: this.form.value.employeeId
      }).subscribe({
        next: (resp) => {
          this._toastr.success('Successed')
          createFileFromBlob(resp, `View_daily_raw_transactions_Check_in${this.form.value.date}.xlsx`);
        },
        error: (err) => {
          this._toastr.error('Please contact admin.')
        }
      })

    }

  }
  onSelect(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        employeeId: selectedData.id,
      });
      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }
  protected _filterBrand() {
    if (!this.employee) {
      return;
    }
    let search = this.employeeFilter.value;

    if (!search) {
      this.filterEmployee.next(this.employee.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employee.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }

  exportExcel() {
    const path = 'excel/view-daily-raw-transactions'
    let formValue = this.form.value
    this._service.exportExcel(formValue, path).subscribe({
      next: (resp) => {
        createFileFromBlob(resp, `View Daily Raw Transactions.xlsx`)
        this._toastr.success('Successed')
      },
      error: (err) => {
        console.error(err)
        this._toastr.error('Please contact admin')
      }
    })
  }
  protected _onDestroy = new Subject<void>();

  openDialogViewImage(data: any) {
          const DialogRef = this.dialog.open(ViewImageComponent, {
              disableClose: false,
              width: '500px',
              height: 'auto',
              enterAnimationDuration: 200,
              exitAnimationDuration: 100,
              data: data
          });
          DialogRef.afterClosed().subscribe((result) => {
              if (result) {
                  console.log(result, 'result')
         
              }
          });
  
      }
}
