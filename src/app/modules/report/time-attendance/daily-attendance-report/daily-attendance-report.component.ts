import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { ApprovalAutocompleteComponent } from 'app/modules/share/approval-autocomplete/approval-autocomplete.component';
import { StaffAutocompleteComponent } from 'app/modules/share/staff-autocomplete/staff-autocomplete.component';
import { DateTime } from 'luxon';
import { ReportService } from '../../page.service';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatRadioModule } from '@angular/material/radio';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import * as XLSX from 'xlsx';
import { createFileFromBlob } from 'app/modules/share/helper';

@Component({
  selector: 'app-report-daily-attendance-report',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterOutlet,
    MatIcon,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatInputModule,
    MatDatepickerModule,
    ApprovalAutocompleteComponent,
    MatRadioModule,
    MatAutocompleteModule,
  ],
  templateUrl: './daily-attendance-report.component.html'
})
export class DailyAttendanceReportComponent implements OnInit {
  items: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  years: number[] = [];
  form: FormGroup;
  department; any;
  startDateControl = new FormControl();
  endDateControl = new FormControl();
  months: { value: number; name: string }[] = [
    { value: 1, name: 'January' },
    { value: 2, name: 'February' },
    { value: 3, name: 'March' },
    { value: 4, name: 'April' },
    { value: 5, name: 'May' },
    { value: 6, name: 'June' },
    { value: 7, name: 'July' },
    { value: 8, name: 'August' },
    { value: 9, name: 'September' },
    { value: 10, name: 'October' },
    { value: 11, name: 'November' },
    { value: 12, name: 'December' },
  ];
  employeeFilter = new FormControl('');
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  employee: any[] = [];

  constructor(
    private _fb: FormBuilder,
    private _activated: ActivatedRoute,
    private _service: ReportService,
    private _toastr: ToastrService
  ) {
    this.department = this._activated.snapshot.data.department
    this.employee = this._activated.snapshot.data.employee
    this.years = this.getPastAndFutureYears();
  }

  ngOnInit(): void {
    const currentDate = new Date();
    this.form = this._fb.group({
      dateStart: '',
      dateEnd: '',
      departmentId: [[]],
      employeeId: null,
    })
    // this.items = [
    //     {
    //         teamLead: {
    //             code: 'MTL001',
    //             firstname: 'Anirut',
    //             lastname: 'Chachuen'
    //         },
    //         staff: [
    //             {
    //                 employee: {
    //                     code: 'MTL001',
    //                     firstname: 'Sitthisak',
    //                     lastname: 'Ophakulchanok'
    //                 },
    //                 date: '2024-10-01',
    //                 timeIn: '09:00',
    //                 timeOut: '18:00',
    //                 calcHrs: 9.00,
    //             },
    //             {
    //                 employee: {
    //                     code: 'MTL001',
    //                     firstname: 'Sitthisak',
    //                     lastname: 'Ophakulchanok'
    //                 },
    //                 date: '2024-10-02',
    //                 timeIn: '09:00',
    //                 timeOut: '18:00',
    //                 calcHrs: 9.00,
    //             },
    //             {
    //                 employee: {
    //                     code: 'MTL001',
    //                     firstname: 'Sitthisak',
    //                     lastname: 'Ophakulchanok'
    //                 },
    //                 date: '2024-10-03',
    //                 timeIn: '09:00',
    //                 timeOut: '18:00',
    //                 calcHrs: 9.00,
    //             },
    //             {
    //                 employee: {
    //                     code: 'MTL002',
    //                     firstname: 'Noppon',
    //                     lastname: 'Chunak'
    //                 },
    //                 date: '2024-10-01',
    //                 timeIn: '09:00',
    //                 timeOut: '18:00',
    //                 calcHrs: 9.00,
    //             },
    //             {
    //                 employee: {
    //                     code: 'MTL002',
    //                     firstname: 'Noppon',
    //                     lastname: 'Chunak'
    //                 },
    //                 date: '2024-10-02',
    //                 timeIn: '09:00',
    //                 timeOut: '18:00',
    //                 calcHrs: 9.00,
    //             },
    //             {
    //                 employee: {
    //                     code: 'MTL002',
    //                     firstname: 'Noppon',
    //                     lastname: 'Chunak'
    //                 },
    //                 date: '2024-10-03',
    //                 timeIn: '09:00',
    //                 timeOut: '18:00',
    //                 calcHrs: 9.00,
    //             },
    //         ]

    //     },
    // ]
    this.employeeFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => {
        this._filterBrand();
      });
  }
  protected _filterBrand() {
    if (!this.employee) {
      return;
    }
    let search = this.employeeFilter.value;

    if (!search) {
      this.filterEmployee.next(this.employee.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employee.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }



  protected _onDestroy = new Subject<void>();


  onSelect(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        employeeId: selectedData.id,
      });
      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }
  getPastYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i <= 10; i++) {
      years.push(currentYear - i);
    }
    return years;
  }
  getPastAndFutureYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let i = 1; i >= -2; i--) {
      years.push(currentYear + i);
    }

    return years;
  }
  GetReport() {
    let formValue = this.form.value
    console.log(this.form.value)
    this._service.getDailyAttendance(this.form.value)
      .subscribe({
        next: (resp: any) => {

          this.items = resp.sort((a, b) => {
            const nameA = `${a.employee?.firstname || ''} ${a.employee?.lastname || ''}`.toLowerCase();
            const nameB = `${b.employee?.firstname || ''} ${b.employee?.lastname || ''}`.toLowerCase();
            return nameA.localeCompare(nameB);
          });
          this._toastr.success('Successed')
        },
        error: (err: any) => {
          this._toastr.error(err.error.message)
        }
      })
  }

  handleValue(value: any) {
    console.log('value', value);
  }
  clearData() {
    this.employeeFilter.reset()
    this.form.reset();
    this.form.patchValue({
      departmentId: []
    })
    this.items = [];
  }
  dateChange() {
    const datePipe = new DatePipe("en-US");

    console.log(this.form.value)
    if (this.form.value.dateStart && this.form.value.dateEnd) {
      const datestart = datePipe.transform(
        this.form.value.dateStart,
        "YYYY-MM-dd"
      );
      const dateend = datePipe.transform(
        this.form.value.dateEnd,
        "YYYY-MM-dd"
      );
      console.log()
      this.form.patchValue({
        dateStart: datestart,
        dateEnd: dateend,
      })
    }
  }

  export() {
    /* pass here the table id */
    let element = document.getElementById('excel-table');
    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);

    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    /* save to file */
    XLSX.writeFile(wb, 'Daily Attendance Report.xlsx');
  }

  exportExcel() {
    const path = 'excel/daily-attendance-report-excel'
    let formValue = this.form.value
    this._service.exportExcel(formValue, path).subscribe({
      next: (resp) => {
        createFileFromBlob(resp, `Daily Attendance Report.xlsx`)
        this._toastr.success('Successed')
      },
      error: (err) => {
        console.error(err)
        this._toastr.error('Please contact admin')
      }
    })
  }
}
