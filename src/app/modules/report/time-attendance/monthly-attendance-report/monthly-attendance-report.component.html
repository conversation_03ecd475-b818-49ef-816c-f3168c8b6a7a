<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-col md:flex-row justify-between pb-2 mb-5 gap-2 border-b-2 border-gray-300">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                    Monthly Attendance Report
                </h2>
            </div>
            <div class="flex flex-col md:flex-row justify-end pb-2 my-2 gap-2">
                <button mat-flat-button [color]="'primary'" (click)="GetReport()">
                    <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
                    <span class="ml-2"> Search</span>
                </button>
                <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
                  <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
                  <span class="ml-2"> Reset</span>
              </button>
                <button mat-flat-button [color]="'primary'" (click)="exportExcel()">
                    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
                    <span class="ml-2">Download</span>
                </button>
            </div>
        </div>
        <form [formGroup]="form">
            <div class="flex flex-col md:flex-row gap-2 my-2 justify-start">
              <div
              class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>Year</mat-label>
                  <mat-select placeholder="Select Year" formControlName="year">
                      <mat-option *ngFor="let year of years" [value]="year">
                          {{year}}
                      </mat-option>
                  </mat-select>
              </mat-form-field>
          </div>
              <div
                    class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                      <mat-label>Month</mat-label>
                        <mat-select placeholder="Select Month" formControlName="month">
                            <mat-option *ngFor="let month of months" [value]="month.value">
                                {{month.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div
                class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Department</mat-label>
                    <mat-select placeholder="Select Department" formControlName="departmentId" multiple>
                        <mat-option *ngFor="let item of department" [value]="item.id">
                            {{item.name ?? '-'}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-3/12">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                <mat-label>Staff</mat-label>
                <input
                    matInput
                    [formControl]="employeeFilter"
                    [matAutocomplete]="AutoComplete"
                    placeholder="Search Staff"
                />
                <mat-autocomplete
                    #AutoComplete="matAutocomplete"
                    (optionSelected)="onSelect($event.option.value, 'manual')">
                    <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                        {{item.firstname}} {{item.lastname}}
                    </mat-option>
                </mat-autocomplete>
            </mat-form-field>
        </div>
                <div
                    class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-6/12 mt-2">
                    <mat-radio-group aria-label="Select an option" formControlName="type" [color]="'primary'">
                        <mat-radio-button [value]="'STD'">Standard </mat-radio-button>
                        <mat-radio-button [value]="'TLD'">With TeamLead Detail</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
        </form>
        <div class="overflow-auto">
            <table id="excel-table"
                class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
                <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                    <tr class="border-[1px] border-black">
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Date</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Time In</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Time Out</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Calc.Hrs</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Work Hrs</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Case 1</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Case 2</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Case 3</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Case 4</th>
                        @if (items) {
                          @for (item of items[0]?.column_leave_type; track $index) {
                            <ng-container >
                              <th scope="col" class="px-2 py-2 border-[1px] border-gray-900 whitespace-nowrap">{{item.name}}</th>
                            </ng-container>
                          }
                        }
                        <!-- <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Ann</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Cas</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Sick</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">WOP</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">MTT</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">OTH</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">TIL</th> -->
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900"
                            *ngIf="this.form.value.type !== 'STD'">Deduct</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900">Explanation</th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-gray-900"
                            *ngIf="this.form.value.type !== 'STD'">Team Lead Explanation</th>
                        <!-- <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Train</th> -->
                    </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let item of items; let i = index">
                      <!-- แสดงชื่อของคนครั้งแรกในรายการของเขา -->
                      <tr *ngIf="i === 0 || (item.employee.code !== items[i - 1]?.employee.code )" class="font-semibold text-left">
                          <td class="px-2 py-2 text-left text-md border-[1px] border-gray-900" colspan="17">
                              {{ item.employee.code }} {{ item.employee.firstname ?? '' }} {{ item.employee.lastname ?? '' }}
                          </td>
                      </tr>
                      <!-- รายการข้อมูล -->
                      <tr class="font-normal" [ngClass]="{
                          'bg-slate-200': item.day === 'Sat' || item.day === 'Sun',
                      }">
                          <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                              {{ (item.workDate | date: 'dd/MM/yyyy') ?? '' }}
                          </td>
                          <td class="px-2 text-center py-2 text-md border-[1px] border-gray-900">
                              {{ item.checkIn ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ item.checkOut ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ (item.calc_hrs | number : '1.2') ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ (item.work_hrs | number : '1.2') ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ (item.case1 | number : '1.0') ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ (item.case2 | number : '1.0') ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ (item.case3 | number : '1.0') ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ (item.case4 | number : '1.0') ?? '' }}
                          </td>
                          <ng-container *ngFor="let leave of item.column_leave_type">
                              <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                                  {{ (leave.qty | number : '1.1') ?? '' }}
                              </td>
                          </ng-container>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900" *ngIf="this.form.value.type !== 'STD'">
                              <p *ngIf="item.deduct == true">Yes</p>
                              <p *ngIf="item.deduct == false">No</p>
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900">
                              {{ item.reason ?? '' }}
                          </td>
                          <td class="px-2 py-2 text-center text-md border-[1px] border-gray-900" *ngIf="this.form.value.type !== 'STD'">
                              {{ item.explanation ?? '' }}
                          </td>
                      </tr>
                  </ng-container>
              </tbody>

            </table>
        </div>
    </div>
</div>
<ng-template #btNg let-data="adtData">
    <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
    </button>
    <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="openDialogEdit(data)">
            <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
            <span>Edit</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="clickDelete(data.id)">
            <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
            <span>Delete</span>
        </button>
    </mat-menu>
</ng-template>
