<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
      <div class="flex flex-col md:flex-row justify-between pb-2 mb-5 gap-2 border-b-2 border-gray-300">
          <div>
              <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                  Staff Review Report
              </h2>
          </div>
          <div class="flex flex-col md:flex-row justify-end pb-2 my-2 gap-2">
              <button mat-flat-button [color]="'primary'" (click)="GetReport()">
                  <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
                  <span class="ml-2"> Search</span>
              </button>
              <button mat-flat-button [color]="'accent'" (click)="clearData">
                  <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
                  <span class="ml-2"> Reset</span>
              </button>
              <button mat-flat-button [color]="'primary'" class="md:w-fit w-1/2">
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
                  <span class="ml-2">Download</span>
              </button>
          </div>
      </div>
      <form [formGroup]="form">
        <div class="flex flex-col md:flex-row gap-2 my-2 justify-start border-b-2 border-gray-300">
          <div
          class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
            <mat-label>Year</mat-label>
              <mat-select placeholder="Select Year" formControlName="year">
                  <mat-option *ngFor="let year of years" [value]="year">
                      {{year}}
                  </mat-option>
              </mat-select>
          </mat-form-field>
      </div>
          <div
                  class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-label>Month</mat-label>
                      <mat-select placeholder="Select Month" formControlName="month">
                          <mat-option *ngFor="let month of months" [value]="month.value">
                              {{month.name}}
                          </mat-option>
                      </mat-select>
                  </mat-form-field>
              </div>
<!--
              <div
                  class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-label>Department</mat-label>
                      <mat-select placeholder="Select Department" formControlName="departmentId" multiple>
                          <mat-option *ngFor="let item of department" [value]="item.id">
                              {{item.name ?? '-'}}
                          </mat-option>
                      </mat-select>
                  </mat-form-field>
              </div> -->
              <div
              class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-3/12">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Staff</mat-label>
                  <input
                      matInput
                      [formControl]="employeeFilter"
                      [matAutocomplete]="AutoComplete"
                      placeholder="Search Staff"
                  />
                  <mat-autocomplete
                      #AutoComplete="matAutocomplete"
                      (optionSelected)="onSelect($event.option.value, 'manual')">
                      <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                          {{item.firstname}} {{item.lastname}}
                      </mat-option>
                  </mat-autocomplete>
              </mat-form-field>
          </div>
          </div>
      </form>
        <div class="my-2 border-b-2 border-gray-300 overflow-auto">
            <table
                class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black my-2 ">
                <thead class="text-md text-white uppercase bg-blue-900 dark:bg-gray-700 dark:text-gray-400">
                    <tr class="border-[1px] border-black">
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center" [colSpan]="6">
                            Attendance Detail
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center" [colSpan]="8">
                            Leave Detail
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center" [colSpan]="4">
                            Case
                        </th>
                        <th class="px-2 py-2 border-[1px] border-black text-center w-60" [rowSpan]="2">
                            Reason
                        </th>
                    </tr>
                    <tr class="border-[1px] border-black">
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Date
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Day
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Time in
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Time Out
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Calc. Hrs
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Work Hrs
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Ann
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Cas
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Sick
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            WOP
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            MTN
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            OTH
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            TIL
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
                            Train
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center whitespace-nowrap">
                            Case 1
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center whitespace-nowrap">
                            Case 2
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center whitespace-nowrap">
                            Case 3
                        </th>
                        <th scope="col" class="px-2 py-2 border-[1px] border-black text-center whitespace-nowrap">
                            Case 4
                        </th>

                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of itemData; let i = index">
                        <tr class="hover:bg-slate-100 cursor-pointer"
                            [ngStyle]="{'background-color': item.color || 'transparent'}"
                            (click)="addReason(item.date,i)">
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{(item.date | date:
                                'dd/MM/yyyy') ?? '-'}}</td>
                            <td class="px-2 py-2 text-md text-left border-[1px] border-black ">{{item.day ?? '-'}}</td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">
                                {{transformTime(item.timeIn)}}</td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">
                                {{transformTime(item.timeOut)}}</td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.calcHrs |
                                min2Hrs}}</td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.workHrs |
                                min2Hrs}}</td>
                            <ng-container *ngFor="let leave of item.leaveType">
                                <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{leave.useDay ??
                                    0}}</td>
                            </ng-container>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case1 ?? 0}}
                            </td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case2 ?? 0}}
                            </td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case3 ?? 0}}
                            </td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case4 ?? 0}}
                            </td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black"
                                *ngIf="this.type === 'team-lead'">
                                <span *ngIf="item.deduct === true">
                                    Yes
                                </span>
                                <span *ngIf="item.deduct === false">
                                    No
                                </span>
                            </td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black ">
                                {{attendance.at(i).get('reason')?.value ?? ''}}
                            </td>
                            <td class="px-2 py-2 text-md text-center border-[1px] border-black "
                                *ngIf="this.type === 'team-lead'">
                                {{attendance.at(i).get('explanation')?.value ?? ''}}
                            </td>
                        </tr>
                    </ng-container>
                    <ng-container *ngIf="itemData.length === 0">
                        <tr class="hover:bg-slate-100">
                            <td class="px-2 py-2 text-md text-center" colspan="19"> No data</td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <div class="flex flex-row mt-2 justify-center" *ngIf="this.itemData.length > 0">
            <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate"
                *ngIf="this.type === 'staff'">
                <button class="px-6 ml-3" mat-flat-button [color]="'primary'" *ngIf="this.itemData " (click)="Submit()">
                    Confirm your review
                </button>
            </div>
            <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate"
                *ngIf="this.type !== 'staff'">
                <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                    Submit
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
