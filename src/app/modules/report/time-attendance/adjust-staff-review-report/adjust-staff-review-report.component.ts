import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { orderBy } from 'lodash';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Min2HrsPipe } from 'app/min2-hrs.pipe';
import { TimeAttendanceService } from 'app/modules/time-attendance/page.service';
import { DialogReasonComponent } from 'app/modules/time-attendance/dialog-reason/dialog-reason.component';
import { ReportService } from '../../page.service';
@Component({
    selector: 'app-time-attendance-staff-review-report',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe,
        Min2HrsPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        MatInputModule,
        MatAutocompleteModule,
        Min2HrsPipe
    ],
    templateUrl: './adjust-staff-review-report.component.html',
    styleUrl: './adjust-staff-review-report.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class StaffReviewReportComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    formStaffReview: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    title: any;
    level: any;
    department: any[] = [];
    itemData: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;
    years: number[] = [];
    year: any = ''
    month: any = ''
    months = [
        { value: '01', name: 'January' },
        { value: '02', name: 'February' },
        { value: '03', name: 'March' },
        { value: '04', name: 'April' },
        { value: '05', name: 'May' },
        { value: '06', name: 'June' },
        { value: '07', name: 'July' },
        { value: '08', name: 'August' },
        { value: '09', name: 'September' },
        { value: '10', name: 'October' },
        { value: '11', name: 'November' },
        { value: '12', name: 'December' }
    ];
    data: any;

    ///approver filter
    employeeFilter = new FormControl('');
    filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    employee: any[] = [];
    type: any;
    profile: any

    constructor(
        private _service: ReportService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _toast: ToastrService,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,

    ) {
      const currentDate = new Date();
      const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // เพิ่ม 0 นำหน้า
      const currentYear = currentDate.getFullYear();
      this.form = this._fb.group({
        month: currentMonth,
        year: currentYear,
        departmentId: [[]],
        employeeId: null,
      })
        this.formStaffReview = this._fb.group({
            employee_id: [null],
            month: [null],
            year: [null],
            attendance: this._fb.array([]),
            confirmStatus: 'draf'
        })
        this.type = this._activatedRoute.snapshot.data.type
        this.employee = this._activatedRoute.snapshot.data.employee
        this.department = this._activatedRoute.snapshot.data.department
        this.years = this.getPastAndFutureYears()

    }
    ngOnInit(): void {
        if (this.type === 'staff') {
            this.profile = JSON.parse(localStorage.getItem('user'));
            const currentYear = new Date().getFullYear();
            const currentDate = new Date();
            let previousMonth = currentDate.getMonth();

            if (previousMonth === 0) {
                previousMonth = 11; // ธันวาคม
            } else {
                previousMonth -= 1;
            }
            this.form.patchValue({
                year: currentYear - (previousMonth === 11 ? 1 : 0),
                month: String(previousMonth + 1).padStart(2, '0'),
            });
        } else if (this.type === 'team-lead') {
            this.profile = this._activatedRoute.snapshot.data.employee
            this.year = this._activatedRoute.snapshot.params.year
            let month = this._activatedRoute.snapshot.params.month
            this.month = this.months.find(item => item.value === month.toString())

            this.itemData = this._activatedRoute.snapshot.data.employeeAttendance.data
            this.formStaffReview.patchValue({
                employee_id: this.itemData[0].employeeId,
                year: +this._activatedRoute.snapshot.params.year,
                month: +this._activatedRoute.snapshot.params.month
            })
            for (let index = 0; index < this.itemData.length; index++) {
                const element = this.itemData[index];
                let formValue = this._fb.group({
                    id: element.id,
                    employeeId: element.employeeId,
                    date: element.date,
                    workStart: element.workStart,
                    workEnd: element.workEnd,
                    timeIn: element.timeIn,
                    timeOut: element.timeOut,
                    case1: element.case1,
                    case2: element.case2,
                    case3: element.case3,
                    case4: element.case4,
                    reason: element.reason,
                    explanation: element.explanation,
                    workHrs: element.workHrs,
                    otHrs: element.otHrs,
                    calcHrs: element.calcHrs,
                    isWorkday: element.isWorkday,
                    isHoliday: element.isHoliday,
                    deduct: element.deduct,
                })
                this.attendance.push(formValue)
            }
            console.log(this.formStaffReview.value);

        }
        this.employeeFilter.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe((value) => {
          this._filterEmployee();
        });

    }
    getPastAndFutureYears(): number[] {
      const currentYear = new Date().getFullYear();
      const years = [];

      for (let i = 1; i >= -2; i--) {
        years.push(currentYear + i);
      }

      return years;
    }
    get attendance(): FormArray {
        return this.formStaffReview.get('attendance') as FormArray;
    }

                   /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

    ngAfterViewInit() {

    }



    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event

    }

    onChangeType() {

    }

    clearData() {
      this.employeeFilter.reset()
        this.itemData = [];
    }

    transformTime(time: string) {
        if (time) {
            const timeWithSeconds = time;
            const timeWithoutSeconds = timeWithSeconds.slice(0, 5);
            // console.log(timeWithoutSeconds)
            return timeWithoutSeconds;
        } else {
            return '-';
        }
    }

    generateTimeAttendance() {
        let formValue = this.form.value
        const error = this.validateYearAndMonth(formValue.year, formValue.month);

        if (error) {
            this._toast.error(error)
        } else {
            this._service.getEmployeeAttendance(formValue.year, +formValue.month, this.profile.id).subscribe({
                next: (resp: any) => {
                    this.itemData = resp.data;
                    this.formStaffReview.patchValue({
                        employee_id: this.profile.id,
                        year: +formValue.year,
                        month: +formValue.month,
                    })
                    for (let index = 0; index < this.itemData.length; index++) {
                        const element = this.itemData[index];
                        let formValue = this._fb.group({
                            id: element.id,
                            employeeId: element.employeeId,
                            date: element.date,
                            workStart: element.workStart,
                            workEnd: element.workEnd,
                            timeIn: element.timeIn,
                            timeOut: element.timeOut,
                            case1: element.case1,
                            case2: element.case2,
                            case3: element.case3,
                            case4: element.case4,
                            reason: element.reason,
                            explanation: element.explanation,
                            workHrs: element.workHrs,
                            otHrs: element.otHrs,
                            calcHrs: element.calcHrs,
                            isWorkday: element.isWorkday,
                            isHoliday: element.isHoliday,
                            deduct: element.deduct,
                        })
                        this.attendance.push(formValue)
                    }
                    console.log(this.formStaffReview.value);
                },
                error: (err: any) => { }
            })

            this._toast.success('Successed')
        }
    }

    addDay(data: any) {

    }

    validateYearAndMonth(year: any, month: any): string | null {
        const now = new Date();
        let currentYear = now.getFullYear();
        let currentMonth = now.getMonth() + 1; // getMonth() ให้ค่าตั้งแต่ 0-11 ดังนั้นต้องบวก 1
        const monthNumber = parseInt(month, 10);
        if (year > currentYear || (year === currentYear && monthNumber >= currentMonth)) {
            return "The provided year and month must be earlier than the current year and month.";
        }
        return null;
    }

    addReason(date: any, i: any) {
        const dialogRef = this._matDialog.open(DialogReasonComponent, {
            width: '400px',
            maxHeight: '90vh',
            data: {
                type: this.type
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            if (item) {
                // เข้าถึง timeAttendance
                const timeAttendance = this.formStaffReview.get('attendance') as FormArray;

                // 1. ใช้ index เพื่อค้นหา
                const index = i; // ตัวอย่าง: ค้นหา index ที่ 1
                const attendanceGroup = timeAttendance.at(index) as FormGroup;

                // 2. ค้นหาโดยใช้เงื่อนไข (find)
                const attendance = timeAttendance.controls.find((control) => {
                    return (control as FormGroup).get('date')?.value === date;
                }) as FormGroup;

                // 3. แก้ไขค่าใน FormGroup ที่พบ
                if (attendance) {
                    if (this.type === 'staff') {
                        attendance.patchValue({ reason: item.reason });
                    } else if (this.type === 'team-lead') {
                        attendance.patchValue({
                            explanation: item.explanation,
                            deduct: item.deduct,
                        });
                    } else {
                        console.log('No data');

                    }

                }
            }
            // this._changeDetectorRef.markForCheck();
        });
    }


    protected _filterEmployee() {
        if (!this.employee) {
            return;
        }
        let search = this.employeeFilter.value;
        // console.log(search, 's');

        if (!search) {
            this.filterEmployee.next(this.employee.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterEmployee.next(
            this.employee.filter(item =>
                (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
                item.firstname.toLowerCase().includes(search) ||
                item.lastname.toLowerCase().includes(search)
            )
        );
    }

    onSelectEmployee(event: any, type: any) {
        if (!event) {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                headId: selectedData.id,
            });
            this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
        } else {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Found');
            return;
        }
    }

    Submit() {
        let formValue = this.formStaffReview.value
        let employeeData = ''
        if (this.type === 'staff') {
            employeeData = this.profile.id
            formValue.confirmStatus = 'confirm'
        } else if (this.type === 'team-lead') {
            employeeData = this._activatedRoute.snapshot.params.id
            formValue.confirmStatus = 'approve'
        }
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to save data?",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })
        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.updateStatusAttendance(+employeeData, formValue).subscribe({
                        error: (err) => {
                            this.toastr.error(err.error.message[0])
                        },
                        complete: () => {
                            this.toastr.success('Successed')
                            if (this.type === 'staff') {
                                this._router.navigate(['/time-attendance/history'])
                            } else {
                                this._router.navigate(['/time-attendance/list-team-lead-review'])
                            }
                        },
                    });
                }
            }
        )
    }

    backTo() {
        this._router.navigate(['/time-attendance/list-team-lead-review'])
    }
    onSelect(event: any, type: any) {
      if (!event) {
        if (this.employeeFilter.invalid) {
          this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
        }
        console.log('No Employee Selected');
        return;
      }

      const selectedData = event; // event จะเป็นออบเจ็กต์ item

      if (selectedData) {
        this.form.patchValue({
          employeeId: selectedData.id,
        });
        this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
      } else {
        if (this.employeeFilter.invalid) {
          this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
        }
        console.log('No Employee Found');
        return;
      }
    }

    GetReport() {
      let formValue = this.form.value
      console.log(this.form.value)
      this._service.getEmployeeAttendance(this.form.value.year,this.form.value.month,this.form.value.employeeId)
        .subscribe({
          next: (resp: any) => {
            this.itemData = resp
          },
          error: (err: any) => {

          }
        })
    }
}
