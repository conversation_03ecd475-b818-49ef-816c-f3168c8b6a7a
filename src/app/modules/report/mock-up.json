{"timeAttendance": [{"date": "01/11/2024", "day": "<PERSON><PERSON>", "timeIn": "09:18", "timeOut": "18:10", "calcHrs": "8:52", "workHrs": "07:52", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "02/11/2024", "day": "Sat", "timeIn": "-", "timeOut": "-", "calcHrs": "0:0", "workHrs": "0", "color": "grey"}, {"date": "03/11/2024", "day": "Sun", "timeIn": "-", "timeOut": "-", "calcHrs": "0:0", "workHrs": "0", "color": "grey"}, {"date": "04/11/2024", "day": "Mon", "timeIn": "09:15", "timeOut": "18:20", "calcHrs": "9:05", "workHrs": "08:05", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "05/11/2024", "day": "<PERSON><PERSON>", "timeIn": "09:10", "timeOut": "18:25", "calcHrs": "9:15", "workHrs": "08:15", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "06/11/2024", "day": "Wed", "timeIn": "09:20", "timeOut": "18:10", "calcHrs": "8:50", "workHrs": "07:50", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "07/11/2024", "day": "<PERSON>hu", "timeIn": "09:18", "timeOut": "18:15", "calcHrs": "8:57", "workHrs": "07:57", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "08/11/2024", "day": "<PERSON><PERSON>", "timeIn": "09:10", "timeOut": "18:30", "calcHrs": "9:20", "workHrs": "08:20", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "09/11/2024", "day": "Sat", "timeIn": "-", "timeOut": "-", "calcHrs": "0:0", "workHrs": "0", "color": "grey"}, {"date": "10/11/2024", "day": "Sun", "timeIn": "-", "timeOut": "-", "calcHrs": "0:0", "workHrs": "0", "color": "grey"}, {"date": "11/11/2024", "day": "Mon", "timeIn": "09:14", "timeOut": "18:17", "calcHrs": "9:03", "workHrs": "08:03", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "12/11/2024", "day": "<PERSON><PERSON>", "timeIn": "09:14", "timeOut": "18:17", "calcHrs": "9:03", "workHrs": "08:03", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "13/11/2024", "day": "Wed", "timeIn": "09:14", "timeOut": "18:17", "calcHrs": "9:03", "workHrs": "08:03", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "14/11/2024", "day": "<PERSON>hu", "timeIn": "09:14", "timeOut": "18:17", "calcHrs": "9:03", "workHrs": "08:03", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}, {"date": "15/11/2024", "day": "<PERSON><PERSON>", "timeIn": "09:14", "timeOut": "18:17", "calcHrs": "9:03", "workHrs": "08:03", "ann": 0, "cas": 0, "sick": 0, "wop": 0, "mtt": 0, "oth": 0, "til": 0, "train": 0, "color": "#FFFFFF"}]}