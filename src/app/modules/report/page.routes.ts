import { inject } from '@angular/core';
import { Routes } from '@angular/router';
import { DepartmentService } from '../hr/department/page.service';
import { EmployeeService } from '../hr/employee/page.service';
import { LeaveTypeService } from '../hr/leave-type/page.service';
import { ProjectService } from '../hr/project/page.service';
import { OtRequestComponent } from '../report/overtime/ot-request/ot-request.component';
import { OtRequestByOwnerComponent } from '../report/overtime/ot-request-owner/ot-request-owner.component';
import { OtRequestByTeamComponent } from '../report/overtime/ot-request-team/ot-request-team.component';
import { OtRequestByStaffComponent } from '../report/overtime/ot-request-staff/ot-request-staff.component';
import { AccumulateStaffDetailComponent } from './leave/accumulate-staff-detail/accumulate-staff-detail.component';
import { AccumulateStaffComponent } from './leave/accumulate-staff/accumulate-staff.component';
import { HistoryMonthlyLeaveReportComponent } from './leave/history-monthly-leave-report/history-monthly-leave-report.component';
import { MonthlyLeaveReportComponent } from './leave/monthly-leave-report/monthly-leave-report.component';
import { ReportComponent } from './report.component';
import { AdjustDepartReviewReportComponent } from './time-attendance/adjust-depart-review-report/adjust-depart-review-report.component';
import { StaffReviewReportComponent } from './time-attendance/adjust-staff-review-report/adjust-staff-review-report.component';
import { DailyAttendanceReportComponent } from './time-attendance/daily-attendance-report/daily-attendance-report.component';
import { MonthlyAttendanceReportComponent } from './time-attendance/monthly-attendance-report/monthly-attendance-report.component';
import { SummaryDeductionReport } from './time-attendance/summary-deduction-report/summary-deduction-report.component';
import { TimeAttendanceViewByUserComponent } from './time-attendance/time-attendance-view-by-user/time-attendance-view-by-user.component';
import { ViewDailyRawTransactionsComponent } from './time-attendance/view-daily-raw-transactions/view-daily-raw-transactions.component';
import { ViewPendingComponent } from './overtime/ot-view-pending/view-pending.component';
import { ContactListComponent } from './client-list/contact-list/contact-list.component';
import { ClientListService } from '../admin/client-list/page.service';
import { OtAirRequestComponent } from './overtime-air/ot-air-request/ot-air-request.component';
import { ViewDailyRawTransactionsCheckInComponent } from './time-attendance/view-daily-raw-transactions-check-in/view-daily-raw-transactions-check-in.component';

export default [
    {
        path: '',
        component: ReportComponent,
        children: [
            {
                path: 'accumulate-staff',
                component: AccumulateStaffComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'accumulate-staff-detail',
                component: AccumulateStaffDetailComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    leavetype: () => inject(LeaveTypeService).getData(),
                },
            },
            {
                path: 'monthly-leave-report',
                component: MonthlyLeaveReportComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'history-monthly-leave-report',
                component: HistoryMonthlyLeaveReportComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'time-attendance-view-by-user',
                component: TimeAttendanceViewByUserComponent,
                data: {
                    type: 'admin',
                },
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'time-attendance-view-by-user-view',
                component: TimeAttendanceViewByUserComponent,
                data: {
                    type: 'staff',
                },
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'view-daily-raw-transactions',
                component: ViewDailyRawTransactionsComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'view-daily-raw-transactions-check-in',
                component: ViewDailyRawTransactionsCheckInComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'daily-attendance-report',
                component: DailyAttendanceReportComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                },
            },
            {
                path: 'monthly-attendance-report',
                component: MonthlyAttendanceReportComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'summary-deduction-report',
                component: SummaryDeductionReport,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approver: () => inject(EmployeeService).getApproval(),
                },
            },
            {
                path: 'adjust-staff-review-report',
                component: StaffReviewReportComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'adjust-depart-review-report',
                component: AdjustDepartReviewReportComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'ot-request-report',
                component: OtRequestComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                },
            },
            {
                path: 'ot-air-request-report',
                component: OtAirRequestComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                },
            },
            {
                path: 'ot-request-byowner-report',
                component: OtRequestByOwnerComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                },
            },
            {
                path: 'ot-request-byteam-report',
                component: OtRequestByTeamComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                    owner: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'ot-request-byteam-report',
                component: OtRequestByTeamComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                    owner: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'ot-request-bystaff-report',
                component: OtRequestByStaffComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                    owner: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'view-pending-request-report',
                component: ViewPendingComponent,
                resolve: {
                    department: () => inject(DepartmentService).getDepartment(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    head: () => inject(EmployeeService).getApproval(),
                    project: () => inject(ProjectService).getProject(),
                    owner: () => inject(EmployeeService).getEmployee(),
                },
            },
            {
                path: 'contact',
                component: ContactListComponent,
                resolve: {
                    companys: () => inject(ClientListService).getCompany(),
                    category: () => inject(ClientListService).getCategory(),
                    activity: () => inject(ClientListService).getActivity(),
                    employee: () => inject(EmployeeService).getEmployee(),
                },
            },
        ],
    },
] as Routes;
