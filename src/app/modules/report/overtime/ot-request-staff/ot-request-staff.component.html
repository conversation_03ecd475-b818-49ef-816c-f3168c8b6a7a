<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between pb-2 mb-5 gap-2 border-b-2 border-gray-300">
      <div>
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          OT Request Report Group By Staff
        </h2>
      </div>
      <div class="flex flex-col md:flex-row justify-end pb-2 my-2 gap-2">

        <button mat-flat-button [color]="'primary'" (click)="this.GetReport()">
          <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
          <span class="ml-2">Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2">Reset</span>
        </button>
        <button mat-flat-button [color]="'primary'" (click)="exportExcel()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
          <span class="ml-2">Download</span>
        </button>
      </div>
    </div>
    <form [formGroup]="form">
      <div class="gap-2 my-2 justify-start border-b-2 border-gray-300">
        <div class="flex flex-col md:flex-row justify-start w-full gap-2">
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full ">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
              <mat-label>Start - End</mat-label>
              <mat-date-range-input [rangePicker]="picker1">
                <input matStartDate formControlName="dateStart" placeholder="Start" (dateChange)="dateChange()">
                <input matEndDate formControlName="dateEnd" placeholder="End" (dateChange)="dateChange()">
              </mat-date-range-input>
              <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
              <mat-date-range-picker #picker1></mat-date-range-picker>
            </mat-form-field>
          </div>
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
              <mat-label>Department</mat-label>
              <mat-select placeholder="Select Department" formControlName="departmentId" multiple>
                <mat-option *ngFor="let item of department" [value]="item.id">
                  {{item.name ?? '-'}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Employee</mat-label>
              <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoCompleteemployee"
                placeholder="Search Employee" (selectionChange)="GetReport()" />
              <mat-autocomplete #AutoCompleteemployee="matAutocomplete"
                (optionSelected)="onSelectemployee($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full ">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Head</mat-label>
              <input matInput [formControl]="headFilter" [matAutocomplete]="AutoCompleteHead" placeholder="Search head"
                (selectionChange)="GetReport()" />
              <mat-autocomplete #AutoCompleteHead="matAutocomplete"
                (optionSelected)="onSelecthead($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterHead | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
        </div>
        <div class="flex flex-col md:flex-row  justify-start w-full gap-2">
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Approver</mat-label>
              <input matInput [formControl]="approverFilter" [matAutocomplete]="AutoCompleteApprover"
                placeholder="Search approver" (selectionChange)="GetReport()" />
              <mat-autocomplete #AutoCompleteApprover="matAutocomplete"
                (optionSelected)="onSelectapproval($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterApprover | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full ">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Project</mat-label>
              <input matInput [formControl]="projectFilter" [matAutocomplete]="AutoCompleteProject"
                placeholder="Search project" (selectionChange)="GetReport()" />
              <mat-autocomplete #AutoCompleteProject="matAutocomplete"
                (optionSelected)="onSelectproject($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterproject | async" [value]="item">
                  [{{item.code}}] {{ item.name }}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
          <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full ">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Project Owner</mat-label>
              <input matInput [formControl]="ownerFilter" [matAutocomplete]="AutoCompleteOwner"
                placeholder="Search owner" (selectionChange)="GetReport()" />
              <mat-autocomplete #AutoCompleteOwner="matAutocomplete"
                (optionSelected)="onSelectowner($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterowner | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
          <!-- <div
            class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full ">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
              <mat-label>Status</mat-label>
              <mat-select [formControlName]="'status'" placeholder="Select status">
                <mat-option *ngFor="let item of this.status;" value="{{item.value}}">
                  {{item.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div> -->
        </div>
      </div>
    </form>
    <div class="overflow-auto">
      <table id="excel-table"
        class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
        <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
          <tr class="border-[1px] border-black">
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">RequestID</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">Day</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">Date</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">StartTime</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">EndTime</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">TotalHours</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">ProjectCode</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">Detail</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">Appover</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">TimeIn</th>
            <th scope="col" class="px-2 py-2 w-1/10 border-[1px] border-gray-900">TimeOut</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of items">
            <!-- Row for Department -->
            <tr class="font-bold">
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 bg-blue-100">
                {{ item?.code ?? ''}}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 bg-blue-100" colspan="10">
                {{ item?.name ?? '' }}
              </td>
            </tr>
              <!-- Row for Leave Types -->
              <ng-container *ngFor="let otdata of item.ots">
                <tr [ngClass]="{'bg-red-200': otdata?.isHoliday}">
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.code ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (otdata?.date | date : 'EEEE') ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (otdata?.date | date : 'dd/MM/yyyy') ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.timeStart ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.timeEnd ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.qtyHour ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.project?.code ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.detail ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.approver?.firstname ?? ''}} {{ otdata?.approver?.lastname ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.in ?? ''}}</td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ otdata?.out ?? ''}}</td>
                </tr>
              </ng-container>
              <tr>
                <td class="px-2 py-2 text-md border-[1px] font-bold border-gray-900 border-r-0 text-end" colspan="5">Total</td>
                <td class="px-2 py-2 text-md border-[1px] border-gray-900 border-l-0 " colspan="6">{{ item.total ?? ''}}
                </td>
              </tr>
            </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>Edit</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>Delete</span>
    </button>
  </mat-menu>
</ng-template>
