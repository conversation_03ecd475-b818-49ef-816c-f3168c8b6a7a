import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
    Form<PERSON>uilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import * as XLSX from 'xlsx';
import { ReportService } from '../../page.service';
import { createFileFromBlob } from 'app/modules/share/helper';

@Component({
    selector: 'app-report-accumulate-staff',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        RouterOutlet,
        MatIcon,
        MatButtonModule,
        MatFormFieldModule,
        MatSelectModule,
        ReactiveFormsModule,
        MatAutocompleteModule,
        MatInputModule,
        MatDatepickerModule,
    ],
    templateUrl: './ot-request-staff.component.html',
})
export class OtRequestByStaffComponent implements OnInit {
    items: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    years: number[] = [];
    form: FormGroup;
    department;
    any;
    status: any[] = [
        {
            value: 'open',
            name: 'Request',
        },
        {
            value: 'head_approved',
            name: 'Reviewed',
        },
        {
            value: 'approved',
            name: 'Approved',
        },
        {
            value: 'cancel',
            name: 'Canceled',
        },
        {
            value: 'reject',
            name: 'Rejected',
        },
    ];
    months: { value: string; name: string }[] = [
        { value: '01', name: 'January' },
        { value: '02', name: 'February' },
        { value: '03', name: 'March' },
        { value: '04', name: 'April' },
        { value: '05', name: 'May' },
        { value: '06', name: 'June' },
        { value: '07', name: 'July' },
        { value: '08', name: 'August' },
        { value: '09', name: 'September' },
        { value: '10', name: 'October' },
        { value: '11', name: 'November' },
        { value: '12', name: 'December' },
    ];
    approverFilter = new FormControl('');
    filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    approval: any[] = [];

    employeeFilter = new FormControl('');
    filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    employee: any[] = [];

    headFilter = new FormControl('');
    filterHead: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    head: any[] = [];

    projectFilter = new FormControl('');
    filterproject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    project: any[] = [];

    ownerFilter = new FormControl('');
    filterowner: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    owner: any[] = [];
    constructor(
        private _fb: FormBuilder,
        private _activated: ActivatedRoute,
        private _service: ReportService,
        private _toastr: ToastrService
    ) {
        this.department = this._activated.snapshot.data.department;
        this.employee = this._activated.snapshot.data.employee;
        this.approval = this._activated.snapshot.data.approval;
        this.head = this._activated.snapshot.data.head;
        this.project = this._activated.snapshot.data.project;
        this.owner = this._activated.snapshot.data.owner;
        this.filterEmployee.next(this.employee.slice());
        this.filterApprover.next(this.approval.slice());
        this.filterHead.next(this.head.slice());
        this.filterproject.next(this.project.slice());
        this.filterowner.next(this.owner.slice());

        this.years = this.getPastAndFutureYears();
    }
    protected _onDestroy = new Subject<void>();

    ngOnInit(): void {
        const currentDate = new Date();
        const currentMonth = (currentDate.getMonth() + 1)
            .toString()
            .padStart(2, '0'); // เพิ่ม 0 นำหน้า
        const currentYear = currentDate.getFullYear();
        this.form = this._fb.group({
            dateStart: '',
            dateEnd: '',
            employeeId: null,
            headId: null,
            approverId: null,
            projectId: null,
            projectOwnerId: null,
            departmentId: [[]],
            status: null,
        });
        this.employeeFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filteremployee();
            });
        this.headFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filterhead();
            });
        this.approverFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filterapprover();
            });
        this.projectFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filterproject();
            });
        this.ownerFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filterowner();
            });
    }

    protected _filteremployee() {
        if (!this.employee) {
            return;
        }
        let search = this.employeeFilter.value;

        if (!search) {
            this.filterEmployee.next(this.employee.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterEmployee.next(
            this.employee.filter(
                (item) =>
                    (
                        item.firstname.toLowerCase() +
                        ' ' +
                        item.lastname.toLowerCase()
                    ).includes(search) ||
                    item.firstname.toLowerCase().includes(search) ||
                    item.lastname.toLowerCase().includes(search)
            )
        );
    }
    protected _filterhead() {
        if (!this.head) {
            return;
        }
        let search = this.headFilter.value;

        if (!search) {
            this.filterHead.next(this.head.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterHead.next(
            this.head.filter(
                (item) =>
                    (
                        item.firstname.toLowerCase() +
                        ' ' +
                        item.lastname.toLowerCase()
                    ).includes(search) ||
                    item.firstname.toLowerCase().includes(search) ||
                    item.lastname.toLowerCase().includes(search)
            )
        );
    }
    protected _filterapprover() {
        if (!this.approval) {
            return;
        }
        let search = this.approverFilter.value;

        if (!search) {
            this.filterApprover.next(this.approval.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterApprover.next(
            this.approval.filter(
                (item) =>
                    (
                        item.firstname.toLowerCase() +
                        ' ' +
                        item.lastname.toLowerCase()
                    ).includes(search) ||
                    item.firstname.toLowerCase().includes(search) ||
                    item.lastname.toLowerCase().includes(search)
            )
        );
    }
    protected _filterproject() {
        if (!this.project) {
            return;
        }
        let search = this.projectFilter.value;

        if (!search) {
            this.filterproject.next(this.project.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterproject.next(
            this.project.filter((item) =>
                item.code.toLowerCase().includes(search)
            )
        );
    }
    protected _filterowner() {
        if (!this.owner) {
            return;
        }
        let search = this.ownerFilter.value;

        if (!search) {
            this.filterowner.next(this.owner.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterowner.next(
            this.owner.filter(
                (item) =>
                    (
                        item.firstname.toLowerCase() +
                        ' ' +
                        item.lastname.toLowerCase()
                    ).includes(search) ||
                    item.firstname.toLowerCase().includes(search) ||
                    item.lastname.toLowerCase().includes(search)
            )
        );
    }

    onSelectemployee(event: any, type: any) {
        if (!event) {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                employeeId: selectedData.id,
            });
            this.employeeFilter.setValue(
                `${selectedData.firstname} ${selectedData.lastname}`
            );
        } else {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Found');
            return;
        }
    }
    onSelecthead(event: any, type: any) {
        if (!event) {
            if (this.headFilter.invalid) {
                this.headFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No head Selected');
            return;
        }
        console.log('event', event);

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                headId: selectedData.id,
            });
            this.headFilter.setValue(
                `${selectedData.firstname} ${selectedData.lastname}`
            );
        } else {
            if (this.headFilter.invalid) {
                this.headFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No head Found');
            return;
        }
    }
    onSelectapproval(event: any, type: any) {
        if (!event) {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No approver Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                approverId: selectedData.id,
            });
            this.approverFilter.setValue(
                `${selectedData.firstname} ${selectedData.lastname}`
            );
        } else {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No approver Found');
            return;
        }
    }

    onSelectproject(event: any, type: any) {
        if (!event) {
            if (this.projectFilter.invalid) {
                this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No project Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                projectId: selectedData.id,
            });
            this.projectFilter.setValue(`${selectedData.code}`);
        } else {
            if (this.projectFilter.invalid) {
                this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No project Found');
            return;
        }
    }
    onSelectowner(event: any, type: any) {
        if (!event) {
            if (this.ownerFilter.invalid) {
                this.ownerFilter.markAsTouched();
            }
            console.log('No owner Selected');
            return;
        }

        const selectedData = event;

        if (selectedData) {
            this.form.patchValue({
                projectOwnerId: selectedData.id,
            });
            this.ownerFilter.setValue(
                `${selectedData.firstname} ${selectedData.lastname}`
            );
        } else {
            if (this.ownerFilter.invalid) {
                this.ownerFilter.markAsTouched();
            }
            console.log('No owner Found');
            return;
        }
    }

    getPastAndFutureYears(): number[] {
        const currentYear = new Date().getFullYear();
        const years = [];

        for (let i = 2; i >= -2; i--) {
            // ลูปจาก 2 (ปีถัดไป) ไป -2 (ย้อนหลัง)
            years.push(currentYear + i);
        }

        return years;
    }

    GetReport() {
        if (
            this.form.value.dateStart === '' ||
            this.form.value.dateEnd === null
        ) {
            this._toastr.error('Please select date');
        } else {
            let formValue = this.form.value;
            console.log(formValue);
            this._service.getOtRequestBystaff(formValue).subscribe((resp: any) => {
                this.items = resp
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map((item: any) => {
                        // จัดเรียง ots ตามวันที่
                        return {
                            ...item,
                            ots: item.ots?.sort(
                                (a: any, b: any) =>
                                    new Date(a.date).getTime() - new Date(b.date).getTime()
                            ),
                        };
                    });

                    console.log(this.items);
                    
            });
        }
    }

    export() {
        /* pass here the table id */
        let element = document.getElementById('excel-table');
        const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);

        /* generate workbook and add the worksheet */
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

        /* save to file */
        XLSX.writeFile(wb, 'OT Request Report Group By Staff.xlsx');
    }

    clearData() {
        this.employeeFilter.reset();
        this.approverFilter.reset();
        this.projectFilter.reset();
        this.headFilter.reset();
        this.ownerFilter.reset();
        this.form.reset();
        this.form.patchValue({
            departmentId: [],
        });
        this.items = [];
    }

    dateChange() {
        const datePipe = new DatePipe('en-US');

        console.log(this.form.value);
        if (this.form.value.dateStart && this.form.value.dateEnd) {
            const datestart = datePipe.transform(
                this.form.value.dateStart,
                'YYYY-MM-dd'
            );
            const dateend = datePipe.transform(
                this.form.value.dateEnd,
                'YYYY-MM-dd'
            );
            console.log();
            this.form.patchValue({
                dateStart: datestart,
                dateEnd: dateend,
            });
        }
    }

    checkStatus(row: any) {
        if (!row) {
            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
        }
        switch (row) {
            case 'open':
                return 'Request';
            case 'head_approved':
                return 'Reviewed';
            case 'approved':
                return 'Approved';
            case 'cancel':
                return 'Canceled';
            case 'reject':
                return 'Rejected';
            default:
                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
        }
    }

    exportExcel() {
        const path = 'excel/ot-request-group-by-staff-excel'
        let formValue = this.form.value
        this._service.exportExcel(formValue, path).subscribe({
            next: (resp) => {
                createFileFromBlob(resp, `OT Request Report Group By Staff.xlsx`)
                this._toastr.success('Successed')
            },
            error: (err) => {
                console.error(err)
                this._toastr.error('Please contact admin')
            }
        })
    }
}
