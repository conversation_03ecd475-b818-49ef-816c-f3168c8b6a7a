<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto p-4 m-4 rounded-md sm:p-10 bg-card">
    <div class="flex flex-col justify-between gap-2 pb-2 mb-5 border-b-2 border-gray-300 md:flex-row">
      <div>
        <h2 class="text-3xl font-extrabold leading-7 tracking-tight truncate sm:leading-10 md:text-4xl">
          Customer list report
        </h2>
      </div>
      <div class="flex flex-col justify-end gap-2 pb-2 my-2 md:flex-row">
        <button mat-flat-button [color]="'primary'" (click)="this.GetReport()">
          <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
          <span class="ml-2">Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2">Reset</span>
        </button>
        <button mat-flat-button [color]="'primary'" (click)="export()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
          <span class="ml-2">Download</span>
        </button>
      </div>
    </div>
    <form [formGroup]="form">
      <div class="flex flex-row justify-start items-center w-full gap-2">
        <div
          class="flex flex-col items-start w-1/3 leading-7 tracking-tight truncate sm:leading-10">
          <mat-label class="font-semibold">Type</mat-label>
          <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="type">
            <mat-radio-button [value]="''">All</mat-radio-button>
            <mat-radio-button [value]="'individual'">Individual</mat-radio-button>
            <mat-radio-button [value]="'business'">Business</mat-radio-button>
          </mat-radio-group>
        </div>
        <div
          class="items-end w-1/3 text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10" *ngIf="this.form.get('type').value === 'business'">
          <app-company-autocomplete [data]="companys"
          (selectedValue)="handleValue($event, 'company')" [employeeFilter]="companyName"
          class="w-full"></app-company-autocomplete>
        </div>
      </div>
      <div class="justify-start gap-2 my-2 border-b-2 border-gray-300 md:flex-row">
        <div class="flex flex-row justify-start w-full gap-2">
          <div
            class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
              <mat-label>Category</mat-label>
              <mat-select placeholder="Select Department" formControlName="categoryId" multiple>
                <mat-option *ngFor="let item of category" [value]="item.id">
                  {{item.name ?? '-'}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div
            class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
              <mat-label>Activity</mat-label>
              <mat-select placeholder="Select Department" formControlName="activityId" multiple>
                <mat-option *ngFor="let item of activity" [value]="item.id">
                  {{item.name ?? '-'}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div
            class="items-end w-full text-3xl font-extrabold leading-7 tracking-tight truncate md:text-4xl sm:leading-10">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Employee</mat-label>
              <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoCompleteemployee"
                placeholder="Search Employee" (selectionChange)="GetReport()" />
              <mat-autocomplete #AutoCompleteemployee="matAutocomplete"
                (optionSelected)="onSelectemployee($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
        </div>

      </div>
    </form>
    <div class="overflow-auto">
      <table id="excel-table"
        class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
        <thead class="text-gray-700 uppercase bg-gray-200 text-md dark:bg-gray-700 dark:text-gray-400">
          <tr class="border-[1px] border-black">
            <th scope="col" class="px-2 py-2 w-[20px] border-[1px] border-gray-900">No.</th>
            <th scope="col" class="px-2 py-2 w-[20px] border-[1px] border-gray-900">Type</th>
            <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Full name</th>
            <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Position</th>
            <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Email</th>
            <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Phone</th>
            <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Company</th>
            <th scope="col" class="px-2 py-2 w-[300px] border-[1px] border-gray-900">Address </th>
            <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Staff</th>

          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of items; let i = index">
            <!-- Row for Department -->
            <tr class="font-normal" [ngClass]="{'bg-red-200': item?.isHoliday}">
              <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                {{ i + 1}}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ convertType(item) }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ ConvertFullName(item) }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ item?.position ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ item?.email ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ item?.mobileNumber ?? '' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ item?.company?.name ?? '-' }}
              </td>
              <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                {{ ConvertAddress(item)}}
              </td>

              <td class="px-2 py-2 text-md border-[1px] border-gray-900 text-wrap">
                {{ConvertStaff(item)}}
              </td>
            </tr>
          </ng-container>
          <tr *ngIf="items.length === 0">
            <td class="px-2 py-2 text-md border-[1px] border-gray-900 text-wrap font-semibold text-center" colspan="9">
              No Data
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
