import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import * as XLSX from 'xlsx';
import { ReportService } from '../../page.service';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { createFileFromBlob } from 'app/modules/share/helper';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-report-accumulate-staff',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterOutlet,
    MatIcon,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatInputModule,
  ],
  templateUrl: './accumulate-staff.component.html'
})
export class AccumulateStaffComponent implements OnInit {
  items: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  years: number[] = [];
  form: FormGroup;
  department; any;
  months: { value: string; name: string }[] = [
    { value: '01', name: 'January' },
    { value: '02', name: 'February' },
    { value: '03', name: 'March' },
    { value: '04', name: 'April' },
    { value: '05', name: 'May' },
    { value: '06', name: 'June' },
    { value: '07', name: 'July' },
    { value: '08', name: 'August' },
    { value: '09', name: 'September' },
    { value: '10', name: 'October' },
    { value: '11', name: 'November' },
    { value: '12', name: 'December' },
  ];
  approverFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];

  employeeFilter = new FormControl('');
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  employee: any[] = [];
  constructor(
    private _fb: FormBuilder,
    private _activated: ActivatedRoute,
    private _service: ReportService,
    private toastr: ToastrService

  ) {
    this.department = this._activated.snapshot.data.department
    this.employee = this._activated.snapshot.data.employee;
    this.filterEmployee.next(this.employee.slice());
    console.log(this.department);

    this.years = this.getPastAndFutureYears()

  }
  protected _onDestroy = new Subject<void>();

  ngOnInit(): void {
    const currentDate = new Date();
    const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // เพิ่ม 0 นำหน้า
    const currentYear = currentDate.getFullYear();
    this.form = this._fb.group({
      // month: currentMonth,
      // year: currentYear,
      departmentId: [[]],
      employeeId: null,
    })

    // this.items = [
    //     {
    //         department: 'Accountant',
    //         staff: [
    //             {
    //                 fullName: 'Kanyanat Sungwirat',
    //                 leaveType: [
    //                     {
    //                         name: 'Annual',
    //                         entitlement: 12,
    //                         taken: 2,
    //                         remain: 10,
    //                         excess: 0,
    //                     },
    //                     {
    //                         name: 'Casual',
    //                         entitlement: 5,
    //                         taken: 3,
    //                         remain: 2,
    //                         excess: 0,
    //                     },
    //                     {
    //                         name: 'Maternity',
    //                         entitlement: 90,
    //                         taken: 0,
    //                         remain: 90,
    //                         excess: 0,
    //                     },
    //                     {
    //                         name: 'Sick',
    //                         entitlement: 30,
    //                         taken: 0,
    //                         remain: 30,
    //                         excess: 0,
    //                     },
    //                 ]
    //             },
    //             {
    //                 fullName: 'Orawan Plangklang',
    //                 leaveType: [
    //                     {
    //                         name: 'Annual',
    //                         entitlement: 12,
    //                         taken: 2,
    //                         remain: 10,
    //                         excess: 0,
    //                     },
    //                     {
    //                         name: 'Casual',
    //                         entitlement: 5,
    //                         taken: 3,
    //                         remain: 2,
    //                         excess: 0,
    //                     },
    //                     {
    //                         name: 'Maternity',
    //                         entitlement: 90,
    //                         taken: 0,
    //                         remain: 90,
    //                         excess: 0,
    //                     },
    //                     {
    //                         name: 'Sick',
    //                         entitlement: 30,
    //                         taken: 0,
    //                         remain: 30,
    //                         excess: 0,
    //                     },
    //                 ]
    //             },
    //         ]
    //     }
    // ]
        this.employeeFilter.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe((value) => {
            this._filterBrand();
          });

  }

  protected _filterBrand() {
    if (!this.employee) {
      return;
    }
    let search = this.employeeFilter.value;

    if (!search) {
      this.filterEmployee.next(this.employee.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employee.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }

  onSelect(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        employeeId: selectedData.id,
      });
      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }

  getPastAndFutureYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let i = 2; i >= -2; i--) { // ลูปจาก 2 (ปีถัดไป) ไป -2 (ย้อนหลัง)
      years.push(currentYear + i);
    }

    return years;
  }



  GetReport() {
    let formValue = this.form.value
    console.log(formValue);
    this._service.getAccumulateStaff(this.form.value).subscribe((resp: any) => {
      this.items = resp;
    })
  }

  export() {
    /* pass here the table id */
    let element = document.getElementById('excel-table');
    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);

    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    /* save to file */
    XLSX.writeFile(wb, 'Accumulate Staff.xlsx');
  }

  clearData() {
    this.employeeFilter.reset()
    this.form.reset();
    this.form.patchValue({
    departmentId: []
    })
    this.items = [];
  }

  exportExcel() {
      const path = 'excel/accumulate-staff-report-excel'
      let formValue = this.form.value
      this._service.exportExcel(formValue, path).subscribe({
        next: (resp) => {
          createFileFromBlob(resp, `Accumulate Staff.xlsx`)
          this.toastr.success('Successed')
        },
        error: (err) => {
          console.error(err)
          this.toastr.error('Please contact admin')
        }
      })
    }
}
