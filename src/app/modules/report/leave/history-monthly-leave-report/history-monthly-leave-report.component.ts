import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-report-history-monthly-leave-report',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        RouterOutlet,
        MatIcon,
        MatButtonModule,
        MatFormFieldModule,
        MatSelectModule,
        ReactiveFormsModule,
    ],
    templateUrl: './history-monthly-leave-report.component.html'
})
export class HistoryMonthlyLeaveReportComponent implements OnInit {
    items: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    years: number[] = [];
    form: FormGroup;
    department; any;
    months: { value: number; name: string }[] = [
        { value: 1, name: 'January' },
        { value: 2, name: 'February' },
        { value: 3, name: 'March' },
        { value: 4, name: 'April' },
        { value: 5, name: 'May' },
        { value: 6, name: 'June' },
        { value: 7, name: 'July' },
        { value: 8, name: 'August' },
        { value: 9, name: 'September' },
        { value: 10, name: 'October' },
        { value: 11, name: 'November' },
        { value: 12, name: 'December' },
    ];
    constructor(
        private _fb: FormBuilder,
        private _activated: ActivatedRoute
    ) {
        this.department = this._activated.snapshot.data.department
        this.years = this.getPastAndFutureYears();
    }

    ngOnInit(): void {
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1; // getMonth() เริ่มนับจาก 0 (0 = มกราคม)
        const currentYear = currentDate.getFullYear();
        this.form = this._fb.group({
            month: currentMonth,
            year: currentYear,
            departmentId:[],
        })
        this.items = [
            {
                department: 'Accountant',
                staff: [
                    {
                        fullName: 'Kanyanat Sungwirat',
                        leaveHistory: [
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-11-30',
                                dateEnd: '2024-11-30',
                                entitlement: 12,
                                taken: 1,
                                remain: 11,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Casual',
                                qtyDay: 1,
                                dateStart: '2024-12-01',
                                dateEnd: '2024-12-01',
                                entitlement: 3,
                                taken: 1,
                                remain: 2,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Sick',
                                qtyDay: 1,
                                dateStart: '2024-12-02',
                                dateEnd: '2024-12-02',
                                entitlement: 30,
                                taken: 1,
                                remain: 29,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-03',
                                dateEnd: '2024-12-03',
                                entitlement: 12,
                                taken: 1,
                                remain: 10,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-04',
                                dateEnd: '2024-12-04',
                                entitlement: 12,
                                taken: 1,
                                remain: 9,
                                excess: 0,
                                status: 'Approved',
                            },
                        ]
                    },
                    {
                        fullName: 'Orawan Plangklang',
                        leaveHistory: [
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-11-30',
                                dateEnd: '2024-11-30',
                                entitlement: 12,
                                taken: 1,
                                remain: 11,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Casual',
                                qtyDay: 1,
                                dateStart: '2024-12-01',
                                dateEnd: '2024-12-01',
                                entitlement: 3,
                                taken: 1,
                                remain: 2,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Sick',
                                qtyDay: 1,
                                dateStart: '2024-12-02',
                                dateEnd: '2024-12-02',
                                entitlement: 30,
                                taken: 1,
                                remain: 29,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-03',
                                dateEnd: '2024-12-03',
                                entitlement: 12,
                                taken: 1,
                                remain: 10,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-04',
                                dateEnd: '2024-12-04',
                                entitlement: 12,
                                taken: 1,
                                remain: 9,
                                excess: 0,
                                status: 'Approved',
                            },
                        ]
                    },
                ]
            },
            {
                department: 'Admin',
                staff: [
                    {
                        fullName: 'Kanyanat Sungwirat',
                        leaveHistory: [
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-11-30',
                                dateEnd: '2024-11-30',
                                entitlement: 12,
                                taken: 1,
                                remain: 11,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Casual',
                                qtyDay: 1,
                                dateStart: '2024-12-01',
                                dateEnd: '2024-12-01',
                                entitlement: 3,
                                taken: 1,
                                remain: 2,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Sick',
                                qtyDay: 1,
                                dateStart: '2024-12-02',
                                dateEnd: '2024-12-02',
                                entitlement: 30,
                                taken: 1,
                                remain: 29,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-03',
                                dateEnd: '2024-12-03',
                                entitlement: 12,
                                taken: 1,
                                remain: 10,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-04',
                                dateEnd: '2024-12-04',
                                entitlement: 12,
                                taken: 1,
                                remain: 9,
                                excess: 0,
                                status: 'Approved',
                            },
                        ]
                    },
                    {
                        fullName: 'Orawan Plangklang',
                        leaveHistory: [
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-11-30',
                                dateEnd: '2024-11-30',
                                entitlement: 12,
                                taken: 1,
                                remain: 11,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Casual',
                                qtyDay: 1,
                                dateStart: '2024-12-01',
                                dateEnd: '2024-12-01',
                                entitlement: 3,
                                taken: 1,
                                remain: 2,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Sick',
                                qtyDay: 1,
                                dateStart: '2024-12-02',
                                dateEnd: '2024-12-02',
                                entitlement: 30,
                                taken: 1,
                                remain: 29,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-03',
                                dateEnd: '2024-12-03',
                                entitlement: 12,
                                taken: 1,
                                remain: 10,
                                excess: 0,
                                status: 'Approved',
                            },
                            {
                                leaveType: 'Annual',
                                qtyDay: 1,
                                dateStart: '2024-12-04',
                                dateEnd: '2024-12-04',
                                entitlement: 12,
                                taken: 1,
                                remain: 9,
                                excess: 0,
                                status: 'Approved',
                            },
                        ]
                    },
                ]
            }
        ]
    }

    getPastYears(): number[] {
        const currentYear = new Date().getFullYear();
        const years = [];
        for (let i = 0; i <= 10; i++) {
            years.push(currentYear - i);
        }
        return years;
    }
    getPastAndFutureYears(): number[] {
      const currentYear = new Date().getFullYear();
      const years = [];

      for (let i = 1; i >= -2; i--) {
        years.push(currentYear + i);
      }

      return years;
    }

    GetReport() {
        let formValue = this.form.value
        console.log(formValue);

    }
}
