<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-col md:flex-row justify-between pb-2 mb-5 gap-2 border-b-2 border-gray-300">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                    Detail Accumulate Staff
                </h2>
            </div>
            <div class="flex flex-col md:flex-row justify-end pb-2 my-2 gap-2">

            <button mat-flat-button [color]="'primary'" (click)="this.GetReport()">
              <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
              <span class="ml-2"> Search</span>
          </button>
          <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
              <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
              <span class="ml-2"> Reset</span>
          </button>
            <button mat-flat-button [color]="'primary'" (click)="exportExcel()">
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cloud-arrow-down'"></mat-icon>
                <span class="ml-2">Download</span>
            </button>
        </div>
      </div>
      <form [formGroup]="form">
        <div class="flex flex-col md:flex-row gap-2 my-2 justify-start border-b-2 border-gray-300">
          <!-- <div
          class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
            <mat-label>Year</mat-label>
              <mat-select placeholder="Select Year" formControlName="year">
                  <mat-option *ngFor="let year of years" [value]="year">
                      {{year}}
                  </mat-option>
              </mat-select>
          </mat-form-field>
      </div>
          <div
                  class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-label>Month</mat-label>
                      <mat-select placeholder="Select Month" formControlName="month">
                          <mat-option *ngFor="let month of months" [value]="month.value">
                              {{month.name}}
                          </mat-option>
                      </mat-select>
                  </mat-form-field>
              </div> -->
              <div
              class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>Start - End</mat-label>
                <mat-date-range-input [rangePicker]="picker1">
                    <input matStartDate formControlName="dateStart" placeholder="Start" (dateChange)="dateChange()">
                    <input matEndDate formControlName="dateEnd" placeholder="End"(dateChange)="dateChange()">
                  </mat-date-range-input>
                  <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                  <mat-date-range-picker #picker1></mat-date-range-picker>
            </mat-form-field>

          </div>
              <div
                  class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-label>Department</mat-label>
                      <mat-select placeholder="Select Department" formControlName="departmentId" multiple>
                          <mat-option *ngFor="let item of department" [value]="item.id">
                              {{item.name ?? '-'}}
                          </mat-option>
                      </mat-select>
                  </mat-form-field>
              </div>

              <div
              class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-3/12">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Staff</mat-label>
                  <input
                      matInput
                      [formControl]="employeeFilter"
                      [matAutocomplete]="AutoComplete"
                      placeholder="Search Staff"
                  />
                  <mat-autocomplete
                      #AutoComplete="matAutocomplete"
                      (optionSelected)="onSelect($event.option.value, 'manual')">
                      <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                          {{item.firstname}} {{item.lastname}}
                      </mat-option>
                  </mat-autocomplete>
              </mat-form-field>
          </div>
          <div
          class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-2/12">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
            <mat-label>Leave Type</mat-label>
              <mat-select placeholder="Select Leave Type" formControlName="leaveTypeId" >
                  <mat-option *ngFor="let item of leavetype" [value]="item.id">
                      {{item.name ?? '-'}}
                  </mat-option>
              </mat-select>
          </mat-form-field>
      </div>
          </div>
      </form>
        <div class="overflow-auto">
            <table id="excel-table"
                class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
                <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                    <tr class="border-[1px] border-black">
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Name</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">From</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">To</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Leave Type</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Leave Total</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Entitlement</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Taken</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Remain</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Excess</th>
                        <th scope="col" class="px-2 py-2 w-2/10 border-[1px] border-gray-900">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of items">
                        <!-- Row for Each Staff -->
                        <tr class="bg-yellow-100 font-semibold">
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900 border-l-0" >{{ item.firstname }} {{ item.lastname }}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900 border-l-0" colspan="9"></td>
                        </tr>
                        <!-- Row for Leave Types -->
                        <ng-container *ngFor="let leave of item.leaves">
                            <tr>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900"></td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.dateStart | date : 'dd/MM/yyyy') ?? '' }}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.dateEnd | date : 'dd/MM/yyyy') ?? '' }}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ leave.leaveType.name ?? '' }}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.qtyDay | number : '1.2') ?? '' }}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.entitlementDay | number : '1.2') ?? ''}}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.takenDay | number : '1.2') ?? ''}}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.remainDay | number : '1.2') ?? '' }}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{ (leave.excessDay | number : '1.2') ?? ''}}</td>
                                <td class="px-2 py-2 text-md border-[1px] border-gray-900">{{checkStatus(leave.status)}}</td>
                            </tr>
                        </ng-container>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>
</div>
<ng-template #btNg let-data="adtData">
    <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
    </button>
    <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="openDialogEdit(data)">
            <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
            <span>Edit</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="clickDelete(data.id)">
            <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
            <span>Delete</span>
        </button>
    </mat-menu>
</ng-template>
