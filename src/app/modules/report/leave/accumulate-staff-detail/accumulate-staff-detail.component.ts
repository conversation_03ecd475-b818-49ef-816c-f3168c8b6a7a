import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import * as XLSX from 'xlsx';
import { ReportService } from '../../page.service';
import { MatDatepickerModule, MatDateRangePicker } from '@angular/material/datepicker';
import { createFileFromBlob } from 'app/modules/share/helper';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-report-accumulate-staff-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterOutlet,
    MatIcon,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatInputModule,
    MatDatepickerModule,
  ],
  templateUrl: './accumulate-staff-detail.component.html'
})
export class AccumulateStaffDetailComponent implements OnInit {
  items: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  years: number[] = [];
  form: FormGroup;
  department; any;

  ///approver filter
  approverFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];

  employeeFilter = new FormControl('');
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  employee: any[] = [];
  leavetype: any[]
  months: { value: string; name: string }[] = [
    { value: '01', name: 'January' },
    { value: '02', name: 'February' },
    { value: '03', name: 'March' },
    { value: '04', name: 'April' },
    { value: '05', name: 'May' },
    { value: '06', name: 'June' },
    { value: '07', name: 'July' },
    { value: '08', name: 'August' },
    { value: '09', name: 'September' },
    { value: '10', name: 'October' },
    { value: '11', name: 'November' },
    { value: '12', name: 'December' },
  ];
  constructor(
    private _fb: FormBuilder,
    private _activated: ActivatedRoute,
    private _service: ReportService,
    private toastr: ToastrService
  ) {
    this.department = this._activated.snapshot.data.department;
    this.employee = this._activated.snapshot.data.employee;
    this.leavetype = this._activated.snapshot.data.leavetype;

    this.filterEmployee.next(this.employee.slice());
    this.years = this.getPastAndFutureYears()
  }

  ngOnInit(): void {
    const currentDate = new Date();
    const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // เพิ่ม 0 นำหน้า
    const currentYear = currentDate.getFullYear();
    this.form = this._fb.group({
      dateStart: '',
      dateEnd: '',
      // month: currentMonth,
      // year: currentYear,
      departmentId: [[]],
      leaveTypeId: null,
      employeeId: null,
    })

    // this.GetReport()

    // this.items = [
    //   {
    //     department: 'Accountant',
    //     staff: [
    //       {
    //         fullName: 'Kanyanat Sungwirat',
    //         leaveHistory: [
    //           {
    //             leaveType: 'Annual',
    //             qtyDay: 1,
    //             dateStart: '2024-11-30',
    //             dateEnd: '2024-11-30',
    //             entitlement: 12,
    //             taken: 1,
    //             remain: 11,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Casual',
    //             qtyDay: 1,
    //             dateStart: '2024-12-01',
    //             dateEnd: '2024-12-01',
    //             entitlement: 3,
    //             taken: 1,
    //             remain: 2,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Sick',
    //             qtyDay: 1,
    //             dateStart: '2024-12-02',
    //             dateEnd: '2024-12-02',
    //             entitlement: 30,
    //             taken: 1,
    //             remain: 29,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Annual',
    //             qtyDay: 1,
    //             dateStart: '2024-12-03',
    //             dateEnd: '2024-12-03',
    //             entitlement: 12,
    //             taken: 1,
    //             remain: 10,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Annual',
    //             qtyDay: 1,
    //             dateStart: '2024-12-04',
    //             dateEnd: '2024-12-04',
    //             entitlement: 12,
    //             taken: 1,
    //             remain: 9,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //         ]
    //       },
    //       {
    //         fullName: 'Orawan Plangklang',
    //         leaveHistory: [
    //           {
    //             leaveType: 'Annual',
    //             qtyDay: 1,
    //             dateStart: '2024-11-30',
    //             dateEnd: '2024-11-30',
    //             entitlement: 12,
    //             taken: 1,
    //             remain: 11,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Casual',
    //             qtyDay: 1,
    //             dateStart: '2024-12-01',
    //             dateEnd: '2024-12-01',
    //             entitlement: 3,
    //             taken: 1,
    //             remain: 2,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Sick',
    //             qtyDay: 1,
    //             dateStart: '2024-12-02',
    //             dateEnd: '2024-12-02',
    //             entitlement: 30,
    //             taken: 1,
    //             remain: 29,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Annual',
    //             qtyDay: 1,
    //             dateStart: '2024-12-03',
    //             dateEnd: '2024-12-03',
    //             entitlement: 12,
    //             taken: 1,
    //             remain: 10,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //           {
    //             leaveType: 'Annual',
    //             qtyDay: 1,
    //             dateStart: '2024-12-04',
    //             dateEnd: '2024-12-04',
    //             entitlement: 12,
    //             taken: 1,
    //             remain: 9,
    //             excess: 0,
    //             status: 'Approved',
    //           },
    //         ]
    //       },
    //     ]
    //   }
    // ]
    this.employeeFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe((value) => {
        this._filterBrand();
      });

  }

  protected _filterBrand() {
    if (!this.employee) {
      return;
    }
    let search = this.employeeFilter.value;

    if (!search) {
      this.filterEmployee.next(this.employee.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employee.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }

                   /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();


  getPastYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let i = 0; i <= 10; i++) {
      years.push(currentYear - i);
    }

    return years;
  }


  GetReport() {
    let formValue = this.form.value
    console.log(this.form.value)
    this._service.getDetailAccumulateStaff(this.form.value)
      .subscribe({
        next: (resp: any) => {

          this.items = resp.sort((a, b) => {
            const nameA = `${a?.firstname || ''} ${a?.lastname || ''}`.toLowerCase();
            const nameB = `${b?.firstname || ''} ${b?.lastname || ''}`.toLowerCase();
            return nameA.localeCompare(nameB);
          });
        },
        error: (err: any) => {

        }
      })
  }

  onSelect(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        employeeId: selectedData.id,
      });
      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Employee Found');
      return;
    }
  }

  export() {
    /* pass here the table id */
    let element = document.getElementById('excel-table');
    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);

    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    /* save to file */
    XLSX.writeFile(wb, 'Detail Accumulate Staff.xlsx');
  }
  clearData() {
    this.employeeFilter.reset()
    this.form.reset();
    this.form.patchValue({
      departmentId: []
    })
    this.items = [];
  }
  getPastAndFutureYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let i = 2; i >= -2; i--) {
      years.push(currentYear + i);
    }

    return years;
  }
  dateChange() {
    const datePipe = new DatePipe("en-US");

    console.log(this.form.value)
    if (this.form.value.dateStart && this.form.value.dateEnd) {
      const datestart = datePipe.transform(
        this.form.value.dateStart,
        "YYYY-MM-dd"
      );
      const dateend = datePipe.transform(
        this.form.value.dateEnd,
        "YYYY-MM-dd"
      );
      console.log()
      this.form.patchValue({
        dateStart: datestart,
        dateEnd: dateend,
      })
    }
  }
  checkStatus(row: any) {
    if (!row) {
      return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
    }
    switch (row) {
      case 'open':
        return 'On process';
      case 'reject':
        return 'Rejected';
      case 'approved':
        return 'Approved';
      case 'cancel':
        return 'Canceled';
      default:
        return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
    }
  }

  exportExcel() {
    const path = 'excel/detail-accumulate-staff-report-excel'
    let formValue = this.form.value
    this._service.exportExcel(formValue, path).subscribe({
      next: (resp) => {
        createFileFromBlob(resp, `Detail Accumulate Staff.xlsx`)
        this.toastr.success('Successed')
      },
      error: (err) => {
        console.error(err)
        this.toastr.error('Please contact admin')
      }
    })
  }
}
