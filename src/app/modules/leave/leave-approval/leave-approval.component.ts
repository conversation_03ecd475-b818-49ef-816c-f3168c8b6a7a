import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { LeaveService } from '../leave.service';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateTime } from 'luxon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { DialogViewComponent } from '../dialog-view/dialog-view.component';
import { DateTimeToSQL } from 'app/helper';
import { Router, RouterEvent } from '@angular/router';
import { orderBy, sortBy } from 'lodash';
import { DialogRejectComponent } from '../dialog-reject/dialog-reject.component';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
    selector: 'leave-approval',
    standalone: true,
    templateUrl: './leave-approval.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        MatSelectModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatButtonModule,
        MatTooltipModule
    ]
})
export class LeaveApprovalComponent implements OnInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('fullName') fullName: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild('selectAll') selectAll: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    itemData: any;
    itemStatus: any;
    form: FormGroup;
    formApprove: FormGroup;
    employee: any;
    multiSelect: any[] = []
    /**
     * Constructor
     */
    constructor(
        private _service: LeaveService,
        private toastr: ToastrService,
        private _fb: FormBuilder,
        private _matDialog: MatDialog,
        private _router: Router,
        private fuseConfirmationService: FuseConfirmationService
    ) {
        this.form = this._fb.group({
            titleId: null,
            levelId: null,
            departmentId: null,
            employeeTypeId: null,
            headId: null,

        })
        this.formApprove = this._fb.group({
            status: null,
            statusRemark: null
        })


        this.employee = JSON.parse(localStorage.getItem('user'));

    }


    ngOnInit(): void {
        this.GetLeaveRemainning()
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }
    rows: any[] = [];
    loadTable(): void {
        let formValue = this.form.value
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            orderBy: [[0, 'ASC']],   // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.status': 'open',
                },
                    this._service.getLeaveApprove(dataTablesParameters).subscribe({

                        next: (resp: any) => {
                            this.itemStatus = resp.data
                            this.rows = resp.data.map(row => ({
                                ...row,
                                selected: false  // ค่าเริ่มต้นเป็น false
                            }));
                            callback({
                                recordsTotal: resp.meta.totalItems,
                                recordsFiltered: resp.meta.totalItems,
                                data: resp.data
                            });
                        }, error: () => {
                            this.toastr.error('Invalid')
                        }
                    })
            },
            columns: [
                {
                    title: 'All',
                    isButton: true, // กำหนดว่าคอลัมน์นี้มีปุ่มใน Header
                    className: 'h-8 w-15 text-center',
                    data: null,
                    orderable: false,
                    ngTemplateRef: {
                        ref: this.btNg
                    },
                },
                {
                    title: 'Leave No.',
                    data: 'code',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.leaveNo,
                    },
                    className: 'h-8 w-30 text-center'
                },
                {
                    title: 'Leave Type',
                    data: 'leaveType.name',
                    defaultContent: '-',
                    className: 'w-30 text-left'
                },
                {
                    title: 'Name',
                    data: 'name',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.fullName,
                    },
                    className: 'w-40 text-left'
                },
                {
                    title: 'Request Date',
                    data: function (row: any) {
                        return DateTime.fromSQL(row.date).toFormat('dd/MM/yyyy');
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Time',
                    data: function (row: any) {
                        if (!row.type) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.type) {
                            case 'half_day_morning':
                                return 'Half day morning';
                            case 'half_day_afternoon':
                                return 'Half day afternoon';
                            case 'full_day':
                                return 'Full day';
                            case 'consecutive_full_day_and_morning':
                                return 'Consecutive Full Day and Half Day Morning';
                            case 'half_afternoon_consecutive':
                                return 'Half Day Afternoon and Consecutive Full Day';
                            case 'consecutive_full_day_and_both_half':
                                return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Start Date',
                    data: function (row: any) {
                        return DateTime.fromSQL(row.dateStart).toFormat('dd/MM/yyyy');
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'End Date',
                    data: function (row: any) {
                        return DateTime.fromSQL(row.dateEnd).toFormat('dd/MM/yyyy');
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },

                {
                    title: 'Total',
                    data: 'qtyDay',
                    defaultContent: '-',
                    className: 'text-center'
                },

                {
                    title: 'Status',
                    data: function (row: any) {
                        if (!row.status) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.status) {
                            case 'open':
                                return 'On process';;
                            case 'reject':
                                return 'Rejected';
                            case 'approved':
                                return 'Approved';
                            case 'cancel':
                                return 'Canceled';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },


            ],
        }
    }

    get ids(): FormArray {
        return this.form.get('ids') as FormArray;
    }

    onCheckboxChange(event: any, id: number): void {
        if (event.checked) {
            // เพิ่ม id เข้าไปใน multiSelect
            this.multiSelect.push(id);
        } else {
            // ลบ id ออกจาก multiSelect
            const index = this.multiSelect.indexOf(id);
            if (index !== -1) {
                this.multiSelect.splice(index, 1); // ใช้ splice เพื่อลบค่าออก
            }
        }
    }

    isAllSelected: boolean = false; // ใช้เก็บสถานะเลือกทั้งหมด

    toggleSelectAll(isSelectAll: boolean): void {
        this.isAllSelected = isSelectAll; // อัปเดตสถานะเลือกทั้งหมด
    
        if (isSelectAll) {
            // เลือกทั้งหมด: เพิ่ม id ของทุกแถวใน multiSelect
            this.itemStatus.forEach((row: any) => {
                if (!this.multiSelect.includes(row.id)) {
                    this.multiSelect.push(row.id); // เพิ่ม id ถ้ายังไม่มีใน multiSelect
                }
                row.selected = true; // ตั้งค่า selected เป็น true
            });
        } else {
            // ยกเลิกการเลือกทั้งหมด: ลบ id ของทุกแถวออกจาก multiSelect
            this.itemStatus.forEach((row: any) => {
                const index = this.multiSelect.indexOf(row.id);
                if (index !== -1) {
                    this.multiSelect.splice(index, 1); // ลบ id ออกจาก multiSelect
                }
                row.selected = false; // ตั้งค่า selected เป็น false
            });
        }
    }

    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    GetLeaveRemainning() {
        this._service.getAll().subscribe((resp: any) => {
            this.itemData = resp

        })
    }

    ViewLeave(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: '700px',
            data: {
                itemid: itemId,
                url: this._router.url,
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            this.rerender();
            // this._changeDetectorRef.markForCheck();
        });
    }

    Submit(status: string) {
        this.formApprove.patchValue({
            status: status,
        })
        if (status === 'reject' || status === 'cancel') {
            const dialogRef = this._matDialog.open(DialogRejectComponent, {
                width: '500px',
                height: 'auto',
                data: {
                    url: this._router.url,
                    type: 'multi'
                }
            });
            dialogRef.afterClosed().subscribe(item => {
                if (item) {
                    this.formApprove.patchValue({
                        statusRemark: item,
                    })
                    const confirmation = this.fuseConfirmationService.open({
                        title: "Do you want to save data ?",
                        icon: {
                            show: true,
                            name: "heroicons_outline:exclamation-triangle",
                            color: "primary"
                        },
                        actions: {
                            confirm: {
                                show: true,
                                label: "Save",
                                color: "primary"
                            },
                            cancel: {
                                show: true,
                                label: "Cancel"
                            }
                        },
                        dismissible: false
                    })
                    let formValue = this.formApprove.value
                    confirmation.afterClosed().subscribe(
                        async result => {
                            if (result == 'confirmed') {
                                try {
                                    for (const element of this.multiSelect) {
                                        await this._service.updateStatus(element, formValue).toPromise();
                                    }
                                    // แสดง Toast หลังจากทุกคำสั่งเสร็จ
                                    this.toastr.success('All updates completed successfully');
                                    this.rerender()
                                } catch (error) {
                                    // แสดงข้อความ Error หากมีข้อผิดพลาด
                                    this.toastr.error(error.error?.message || 'An error occurred');
                                }
                            }
                        }
                    )
                }
            });
       
        } else {
            let formValue = this.formApprove.value
            const confirmation = this.fuseConfirmationService.open({
                title: "Do you want to save data ?",
                icon: {
                    show: true,
                    name: "heroicons_outline:exclamation-triangle",
                    color: "primary"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: false
            })
    
            confirmation.afterClosed().subscribe(
                async result => {
                    if (result == 'confirmed') {
                        try {
                            for (const element of this.multiSelect) {
                                await this._service.updateStatus(element, formValue).toPromise();
                            }
                            // แสดง Toast หลังจากทุกคำสั่งเสร็จ
                            this.toastr.success('All updates completed successfully');
                            this.rerender()
                        } catch (error) {
                            // แสดงข้อความ Error หากมีข้อผิดพลาด
                            this.toastr.error(error.error?.message || 'An error occurred');
                        }
                    }
                }
            )
        }

   
    }
}
