<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">

        <div class="flex flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl mx-2">
                    List of history leave
                </h2>
            </div>
        </div>
        <div class="flex flex-row my-2 justify-start">
            <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-select placeholder="เลือกปี" (selectionChange)="getData($event.value)">
                        <mat-option *ngFor="let year of years" [value]="year">
                            {{year}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
        </table>
    </div>

</div>

<ng-template #btNg let-data="adtData">
    <input type="checkbox" (change)="onCheckboxChange($event, row.id)" />
</ng-template>

<ng-template #leaveNo let-data="adtData">
    <p (click)="ViewLeave(data)" class="underline cursor-pointer text-blue-500">{{data.code}}</p>
</ng-template>

<ng-template #fullName let-data="adtData">
    <p class=" ">{{data.employee.firstname}} {{data.employee.lastname}}</p>
</ng-template>