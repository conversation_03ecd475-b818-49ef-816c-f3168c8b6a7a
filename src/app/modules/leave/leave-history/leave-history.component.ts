import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { LeaveService } from '../leave.service';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateTime } from 'luxon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { DialogViewComponent } from '../dialog-view/dialog-view.component';
import { Router } from '@angular/router';

@Component({
    selector: 'leave-history',
    standalone: true,
    templateUrl: './leave-history.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        MatSelectModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatButtonModule
    ]
})
export class LeaveHistoryComponent implements OnInit {
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild('fullName') fullName: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    itemData: any;
    itemStatus: any;
    form: FormGroup;
    years: number[] = [];
    year: number;
    employee: any;
    /**
     * Constructor
     */
    constructor(
        private _service: LeaveService,
        private toastr: ToastrService,
        private fb: FormBuilder,
        private _matDialog: MatDialog,
        private _router: Router
    ) {

        this.employee = JSON.parse(localStorage.getItem('user'))
        this.form = this.fb.group({
            ids: this.fb.array([])
        });
        this.years = this.getPastAndFutureYears()
    }


    ngOnInit(): void {
        this.GetLeaveRemainning()
        setTimeout(() =>
            this.loadTable());
    }

    getPastYears(): number[] {
        const currentYear = new Date().getFullYear();
        const years = [];

        for (let i = 0; i <= 10; i++) {
            years.push(currentYear - i);
        }

        return years;
    }
    getPastAndFutureYears(): number[] {
      const currentYear = new Date().getFullYear();
      const years = [];

      for (let i = 1; i >= -2; i--) {
        years.push(currentYear + i);
      }

      return years;
    }

    getData(data: number) {
        this.year = data
        this.rerender()
    }


    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    // 'filter.status': 'open,
                }
                dataTablesParameters.employeeId = this.employee?.id
                this._service.getLeaveEmp(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        this.itemStatus = resp.data;
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: 'Leave No.',
                    data: 'leaveNo',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.leaveNo,
                    },
                    className: 'h-8 w-30 text-center'
                },
                {
                    title: 'Leave Type',
                    data: 'leaveType.name',
                    defaultContent: '-',
                    className: 'w-30 text-left'
                },
                {
                    title: 'Name',
                    data: 'name',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.fullName,
                    },
                    className: 'w-40 text-left'
                },
                {
                    title: 'Request Date',
                    data: function (row: any) {
                        return DateTime.fromSQL(row.date).toFormat('dd/MM/yyyy');
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Time',
                    data: function (row: any) {
                        if (!row.type) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }

                        switch (row.type) {
                            case 'half_day_morning':
                                return 'Half day morning';
                            case 'half_day_afternoon':
                                return 'Half day afternoon';
                            case 'full_day':
                                return 'Full day';
                            case 'consecutive_full_day_and_morning':
                                return 'Consecutive Full Day and Half Day Morning';
                            case 'half_afternoon_consecutive':
                                return 'Half Day Afternoon and Consecutive Full Day';
                            case 'consecutive_full_day_and_both_half':
                                return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Start Date',
                    data: function (row: any) {
                        return DateTime.fromSQL(row.dateStart).toFormat('dd/MM/yyyy');
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'End Date',
                    data: function (row: any) {
                        return DateTime.fromSQL(row.dateEnd).toFormat('dd/MM/yyyy');
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },

                {
                    title: 'Total',
                    data: 'qtyDay',
                    defaultContent: '-',
                    className: 'text-center'
                },

                {
                    title: 'Status',
                    data: function (row: any) {
                        if (!row.status) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.status) {
                            case 'open':
                                return 'On process';;
                            case 'reject':
                                return 'Rejected';
                            case 'approved':
                                return 'Approved';
                            case 'cancel':
                                return 'Canceled';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },

            ]
        }
    }

    get ids(): FormArray {
        return this.form.get('ids') as FormArray;
    }

    onCheckboxChange(event: any, id: number) {
        if (event.target.checked) {
            this.ids.push(this.fb.control(id));
        } else {
            const index = this.ids.controls.findIndex(x => x.value === id);
            if (index !== -1) {
                this.ids.removeAt(index);
            }
        }
    }

    isAllSelected = false;

    toggleSelectAll(event: any) {
        this.isAllSelected = event.target.checked;
        this.itemStatus.forEach((row: any) => {
            row.selected = this.isAllSelected;
        });
    }

    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    GetLeaveRemainning() {
        this._service.getAll().subscribe((resp: any) => {
            this.itemData = resp

        })
    }

    ViewLeave(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: '700px',
            data: {
                itemid: itemId,
                url: this._router.url
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            this.rerender();
            // this._changeDetectorRef.markForCheck();
        });
    }


}
