import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { LeaveService } from '../leave.service';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatInputModule } from '@angular/material/input';
import { difference } from 'lodash';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DateTime } from 'luxon';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { DateTimeToISO, DateTimeToJSDate } from 'app/helper';
import { DialogPreviewComponent } from './dialog-preview/dialog-preview.component';
import { MatDialog } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatNativeDateModule } from '@angular/material/core';

@Component({
  selector: 'leave-application-form',
  standalone: true,
  templateUrl: './form.component.html',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatRadioModule,
    MatInputModule,
    ImageUploadComponent,
    MatAutocompleteModule,
    MatNativeDateModule

  ]
})
export class LeaveApplicationFormComponent implements OnInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  itemData: any;
  form: FormGroup;
  leaveType: any[] = [];
  employee: any;
  startDate: Date | null = null;
  endDate: Date | null = null;
  dayDifference: number | null = null;
  ///approver filter
  approverFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];
  expireDate: Date | null = null;
  leavePermission: any;
  errorMessage: string | null = null;

  get isDisabled() {
    return !this.form.value.leaveTypeId;
  };
  /**
   * Constructor
   */
  constructor(
    private _service: LeaveService,
    private _fb: FormBuilder,
    private _fuseConfirmationService: FuseConfirmationService,
    private imageUploadService: ImageUploadService,
    private toastr: ToastrService,
    private _router: Router,
    private _activateRoute: ActivatedRoute,
    private _matDialog: MatDialog
  ) {


    this.leaveType = this._activateRoute.snapshot.data.leaveType
    this.leavePermission = this._activateRoute.snapshot.data.leavePermission;
    this.approval = this._activateRoute.snapshot.data.approval
    this.filterApprover.next(this.approval.slice());
    this.employee = JSON.parse(localStorage.getItem('user'))

  }

  ngOnInit(): void {

    this.form = this._fb.group({
      date: null,
      type: 'full_day',
      dateEnd: [null, Validators.required],
      dateStart: [null, Validators.required],
      qtyDay: 0,
      reason: [null, Validators.required],
      file: null,
      file_name: null,
      employeeId: this.employee?.id,
      leaveTypeId: [null, Validators.required],
      headId: this.employee?.head?.toString()
    });
    const head = this.approval.find(item => item.id === this.employee?.head)
    if (head) {
      this.approverFilter.setValue(`${head.firstname} ${head.lastname}`);
    }


    this.approverFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterBrand();
      });
    // console.log(this.employee);


  }



  Submit() {
    if (this.form.invalid) {
      return;
    }

    let data = {
      ...this.form.value
    };
    
    
    data.date = DateTime.now().toFormat('yyyy-MM-dd')
    this._service.getLeaveQty(data).subscribe({
      next: (response) => {

        data.qtyDay = response; // เก็บข้อมูลที่ได้จาก API
        
      },
      error: (err) => {
        this.toastr.error(err.error.message);
      },
      complete: () => {
        let formValue = {
          leaveTypeId: this.leavePermission.find(item => item?.leaveType?.id === +data.leaveTypeId)?.leaveType?.name,
          head: (() => {
            const foundItem = this.approval.find(item => item?.id === +data.headId);
            // ตรวจสอบว่า foundItem เป็น undefined หรือไม่ก่อนที่จะเข้าถึง firstname และ lastname
            return foundItem && foundItem.firstname && foundItem.lastname
              ? `${foundItem.firstname} ${foundItem.lastname}`
              : null;
          })()
        };
        const statusType = (() => {
          switch (data.type) {
            case 'half_day_morning':
              return 'Half day morning';
            case 'half_day_afternoon':
              return 'Half day afternoon';
            case 'full_day':
              return 'Full day';
            case 'consecutive_full_day_and_morning':
              return 'Consecutive Full Day and Half Day Morning';
            case 'half_afternoon_consecutive':
              return 'Half Day Afternoon and Consecutive Full Day';
            case 'consecutive_full_day_and_both_half':
              return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
            default:
              return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
          }
        })();
        const warningStatus = this.checkLeave(formValue.leaveTypeId, data.dateStart)
        let checkLeave = this.leavePermission.find(item => item?.leaveType?.id === +data.leaveTypeId)
        const remain = this.CalculateLeave(checkLeave?.qtyDay, checkLeave?.usedDay)
        if (data.qtyDay > remain) {
          this.toastr.error('Your leave days are insufficient.')
          return;
        }  else if (data.qtyDay === 0) {
          this.toastr.error("Please select a date that is not a holiday.");
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return ;
        }else {

          let leavePreview = {
            leaveType: formValue.leaveTypeId,
            date: `${DateTimeToJSDate(data.dateStart, 'dd/MM/yyyy')} to ${DateTimeToJSDate(data.dateEnd, 'dd/MM/yyyy')}`,
            amount: data.qtyDay,
            time: statusType,
            approver: formValue.head,
            reason: data.reason,
            warningStatus: warningStatus.status,
            warningText: warningStatus.text
          }

          const dialogRef = this._matDialog.open(DialogPreviewComponent, {
            width: '500px',
            // height: 'auto',
            maxHeight: '90%',
            data: leavePreview
          });
          dialogRef.afterClosed().subscribe(item => {
            data.dateStart = DateTime.fromJSDate(this.form.value.dateStart).toFormat('yyyy-MM-dd')
            data.dateEnd = DateTime.fromJSDate(this.form.value.dateEnd).toFormat('yyyy-MM-dd')
            if (item) {
              this._service.createLeave(data).subscribe({
                error: (err) => {
                  this.toastr.error(err.error.message)
                },
                complete: () => {
                  this.toastr.success('Successed')
                  this._router.navigate(['/leave/leave-remainning'])
                },
              });
            }

          });

        }
      },
    });
  }

  checkLeave(leaveType: string, startDate: Date) {
    const currentDate = new Date();
    const differenceInDays = Math.floor(
      (new Date(startDate).getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    console.log(differenceInDays);

    if (leaveType === 'Annual' && differenceInDays > 0 && differenceInDays >= 7) {
      return { status: true, text: 'Valid' };
    }

    if (leaveType === 'Casual' && differenceInDays > 0 && differenceInDays >= 3) {
      return { status: true, text: 'Valid' };
    }

    if (leaveType === 'Annual' && differenceInDays > 0 && differenceInDays < 7) {
      return { status: false, text: 'Annual leave must be requested at least 7 days in advance.' };
    }

    if (leaveType === 'Casual' && differenceInDays > 0 && differenceInDays < 3) {
      return { status: false, text: 'Casual leave must be requested at least 3 days in advance.' };
    }
    // If no conditions match, return true (no restrictions)
    return { status: true, text: 'No specific conditions for leave.' };
  }

  backTo() {
    this._router.navigate(['/leave/leave-remainning'])
  }

  files: File[] = [];
  onSelect(event, input: any) {
    if (input === 'addfile') {

      this.form.patchValue({
        file: event[0],
        file_name: event[0].name,
      });
    }
  }

  uploadSuccess(event): void {
    this.imageUploadService.upload(event).subscribe({
      next: (resp: any) => {
        // console.log(resp);

        this.form.patchValue({
          file: resp.pathUrl
        });
        // console.log(this.form.value);

      },
      error: (err) => {
        this.toastr.error(JSON.stringify(err))
      },
    })
  }

  CalculateLeave(qty: any, used: any) {
    const remain = qty - used;
    return remain
  }

  onSelectionChange(event: any): void {

    const selectedId = event.value;
    const selectedItem = this.leavePermission.find(item => item.leaveType.id === selectedId);
    if (selectedItem && this.CalculateLeave(selectedItem.qtyDay, selectedItem.usedDay) === 0) {
      // แจ้ง Error ว่าคุณไม่สามารถเลือกตัวเลือกนี้ได้
      alert('You cannot select this leave type because the remaining days are 0.');
      this.toastr.error('You cannot select this leave type because the remaining days are 0.')
      // รีเซ็ตค่าในฟอร์มให้เป็น null
      this.form.get('leaveTypeId')?.setValue(null);
    }
  }

  calculateDays() {
    const dateStart: Date = this.form.get('dateStart')?.value;
    const dateEnd: Date = this.form.get('dateEnd')?.value;
    const timeOption = this.form.get('type')?.value;
    const expireDate = new Date(this.expireDate)


    if (dateEnd > expireDate) {
      this.errorMessage = `Please select a date on or before ${DateTime.fromJSDate(expireDate).toLocal().toFormat('dd/MM/yyyy')}`;
      this.toastr.error(this.errorMessage);
      this.form.patchValue({
        dateEnd: null,
        dateStart: null,
      })

      return;
    }

    if (!dateStart || !dateEnd || !timeOption) {
      this.errorMessage = null;
      return;
    }

    const daysDifference =
      (new Date(dateEnd).getTime() - new Date(dateStart).getTime()) /
      (1000 * 60 * 60 * 24) +
      1;

    switch (timeOption) {
      case 'half_day_morning':
      case 'half_day_afternoon':
        if (daysDifference !== 1) {
          this.errorMessage = 'Half Day options can only be selected for 1 day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'full_day':
        if (daysDifference < 1) {
          this.errorMessage = 'Full Day requires at least 1 day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'consecutive_full_day_and_morning':
      case 'half_afternoon_consecutive':
        if (daysDifference <= 1) {
          this.errorMessage =
            'This option requires more than 1 consecutive day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'consecutive_full_day_and_both_half':
        if (daysDifference < 3) {
          this.errorMessage =
            'This option requires at least 3 consecutive days.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      default:
        this.errorMessage = 'Invalid option selected.';
        this.toastr.error(this.errorMessage)
        this.form.patchValue({
          dateEnd: null,
          dateStart: null,
        })
        return;
    }
    // หากไม่มีปัญหา
    this.errorMessage = null;
  }

                 /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

  protected _filterBrand() {
    if (!this.approval) {
      return;
    }
    let search = this.approverFilter.value;
    console.log(search, 's');

    if (!search) {
      this.filterApprover.next(this.approval.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterApprover.next(
      this.approval.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }

  onSelectApprover(event: any, type: any) {
    if (!event) {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        headId: selectedData.id,
      });
      this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Found');
      return;
    }
  }

  calculateMaxdate(event: any) {
    const id = +event.value
    const formValue = this.leavePermission.find(item => item.leaveType.id === id)
    this.expireDate = formValue.expireDate
  }

}
