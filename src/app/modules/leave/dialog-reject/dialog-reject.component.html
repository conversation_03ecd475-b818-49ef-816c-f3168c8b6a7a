<div>
    <div class="flex flex-row justify-between border-b-2 border-gray-300">
        <div class="my-2 text-lg text-black font-bold">
            <h4>Reject</h4>
        </div>
    </div>
    <div class="grid grid-cols-1 my-2 border-b-2 border-gray-300">
        <div class="flex flex-col gap-2 mb-2 mx-2">
            <div class="flex flex-row mt-2 gap-2" *ngIf="this.data?.url !== '/admin-leave/leave'">
                <div class="md:w-full" [formGroup]="form">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Reason</mat-label>
                        <textarea matInput [rows]="3" formControlName="statusRemark"></textarea>
                        <mat-error>Reson is required</mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2" *ngIf="this.data?.url === '/admin-leave/leave'">
                <div class="md:w-full" [formGroup]="form">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Reason</mat-label>
                        <textarea matInput [rows]="3" formControlName="hrRemark"></textarea>
                        <mat-error>Reson is required</mat-error>
                    </mat-form-field>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-row mt-2 justify-center">
        <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                *ngIf="this.data?.url === '/leave/leave-remainning' || this.data?.url === '/admin-leave/leave'" (click)="Submit('cancel')">
                Reject
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                *ngIf="this.data?.url === '/leave/leave-approval'" (click)="Submit('reject')">
                Reject
            </button>
            <button class="px-6 ml-3 border-[1px] border-gray-200" mat-flat-button [color]="'accent'" (click)="onClose()">
                close
            </button>
        </div>
    </div>
</div>