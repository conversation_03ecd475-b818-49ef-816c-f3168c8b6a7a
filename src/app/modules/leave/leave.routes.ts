import { Routes } from '@angular/router';
import { LeaveComponent } from './leave.component';
import { LeaveRemainningComponent } from './leave-remainning/leave-remainning.component';
import { LeaveApplicationFormComponent } from './leave-application-form/form.component';
import { LeaveApprovalComponent } from './leave-approval/leave-approval.component';
import { LeaveHistoryComponent } from './leave-history/leave-history.component';
import { LeaveOfListComponent } from './leave-of-list/leave-list.component';
import { EmployeeService } from '../hr/employee/page.service';
import { inject } from '@angular/core';
import { LeaveService } from './leave.service';
import { ListOfApproverComponent } from '../report/list-of-approver/list-of-approver.component';
import { ReportService } from '../report/page.service';


export default [
    {
        path     : '',
        component: LeaveComponent,
    },
    {
        path     : 'leave-remainning',
        component: LeaveRemainningComponent,
        resolve: {
            leavePermission: () => inject(LeaveService).getLaevePermission(),
            
        }
    },
    {
        path     : 'leave-application-form',
        component: LeaveApplicationFormComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            leavePermission: () => inject(LeaveService).getLaevePermission(),
            
        }
    },
    {
        path     : 'leave-approval',
        component: LeaveApprovalComponent,
    },
    {
        path     : 'leave-history',
        component: LeaveHistoryComponent,
    },
    {
        path     : 'leave-list',
        component: LeaveOfListComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),
        }
    },
    {
        path     : 'leave-list-of-approver',
        component: ListOfApproverComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),
            listOfApprover: () => inject(ReportService).getListOfApprover(),
            
        }
    },
] as Routes;
