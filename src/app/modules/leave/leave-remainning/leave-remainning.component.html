<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl mx-2">
                    Leave Summary
                </h2>
            </div>
        </div>
        <div class="flex flex-row justify-between mb-2">
            <div class="">
                <p class="sm:text-sm md:text-lg font-semibold">Your leave period is from :
                    <span class="sm:text-sm font-normal text-green-900">{{datePeriod}}</span>
                </p>

            </div>
        </div>
        <div class="overflow-auto">
            <table
            class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
            <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                <tr class="border-[1px] border-black">
                    <th scope="col" class="px-2 py-2 w-2/10">
                        Leave Type
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/10">
                        Entitlement
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/10">
                        Taken
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/10 ">
                        Remain
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/10">
                        Excess
                    </th>
                </tr>

            </thead>
            <tbody>
                <ng-container *ngFor="let item of leavePermission; let i = index">
                    <tr *ngIf="item.qtyDay > 0 || item?.leaveType?.show === true">
                        <td class="px-2 py-2 text-md">
                            {{ item?.leaveType?.name ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md">
                            {{ item?.qtyDay ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md">
                            {{ item?.usedDay ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md">
                            {{CalculateLeave(item?.qtyDay,item?.usedDay) ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md text-red-500 ">
                            {{ item?.excessDay ?? '-' }}
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
        </div>
    
        <div class="overflow-auto">
            <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full"></table>
        </div>

    </div>

</div>

<ng-template #btNg let-data="adtData">
    <input type="checkbox" (change)="onCheckboxChange($event, row.id)" />
</ng-template>
<ng-template #leaveNo let-data="adtData">
   <p (click)="ViewLeave(data)" class="underline cursor-pointer text-blue-500">{{data.code}}</p>
</ng-template>
<ng-template #fullName let-data="adtData">
   <p class=" ">{{data.employee.firstname}} {{data.employee.lastname}}</p>
</ng-template>