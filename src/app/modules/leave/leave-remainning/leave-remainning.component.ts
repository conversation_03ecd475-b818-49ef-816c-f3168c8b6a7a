import { ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { LeaveService } from '../leave.service';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateTime } from 'luxon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { DialogViewComponent } from '../dialog-view/dialog-view.component';
import { DateTimeToSQL } from 'app/helper';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
    selector: 'leave-remainning',
    standalone: true,
    templateUrl: './leave-remainning.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        MatSelectModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatButtonModule
    ]
})
export class LeaveRemainningComponent implements OnInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild('fullName') fullName: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    itemData: any;
    itemStatus: any;
    form: FormGroup;
    leavePermission: any;
    employee: any;
    datePeriod: string = '';
    /**
     * Constructor
     */
    constructor(
        private _service: LeaveService,
        private toastr: ToastrService,
        private fb: FormBuilder,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activated: ActivatedRoute,
        private _changeDetectorRef: ChangeDetectorRef
    ) {
        this.leavePermission = this._activated.snapshot.data.leavePermission;
        this.employee = JSON.parse(localStorage.getItem('user'))
        this.form = this.fb.group({
            ids: this.fb.array([])
        });

       

        this.datePeriod = DateTimeToSQL(this.leavePermission[0]?.startPeriod, 'dd/MM/yyyy') + ' to ' + DateTimeToSQL(this.leavePermission[0]?.endPeriod, 'dd/MM/yyyy')
    }


    ngOnInit(): void {
        this.GetLeaveRemainning()
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.status': 'open',
                    // 'filter.employee': this.employee?.id 
                },
                dataTablesParameters.employeeId = this.employee.id
                this._service.getLeaveEmp(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        this.itemStatus = resp.data;
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                // {
                //     title: '<input type="checkbox" (click)="toggleSelectAll($event)" />',
                //     data: null,
                //     defaultContent: '',
                //     ngTemplateRef: {
                //         ref: this.btNg,
                //     },
                //     className: 'w-15 text-center h-10',
                //     orderable: false
                // },
                {
                    title: 'Leave No.',
                    data: 'code',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.leaveNo,
                    },
                    className: 'h-8 w-30 text-center'
                },
                {
                    title: 'Name',
                    data: '',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.fullName,
                    },
                    className: 'w-40 text-left'
                },
                {
                    title: 'Leave Type',
                    data: 'leaveType.name',
                    defaultContent: '-',
                    className: 'w-30 text-left'
                },
                {
                    title: 'Request Date',
                    data: function (row: any) {
                        return DateTimeToSQL(row.date, 'dd/MM/yyyy')
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Time',
                    data: function (row: any) {
                        if (!row.type) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }

                        switch (row.type) {
                            case 'half_day_morning':
                                return 'Half day morning';
                            case 'half_day_afternoon':
                                return 'Half day afternoon';
                            case 'full_day':
                                return 'Full day';
                            case 'consecutive_full_day_and_morning':
                                return 'Consecutive Full Day and Half Day Morning';
                            case 'half_afternoon_consecutive':
                                return 'Half Day Afternoon and Consecutive Full Day';
                            case 'consecutive_full_day_and_both_half':
                                return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Start Date',
                    data: function (row: any) {
                        return DateTimeToSQL(row.dateStart, 'dd/MM/yyyy')
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },

                {
                    title: 'End Date',
                    data: function (row: any) {
                        return DateTimeToSQL(row.dateEnd, 'dd/MM/yyyy')
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Taken',
                    data: 'qtyDay',
                    defaultContent: '-',
                    className: 'text-left'
                },
                // {
                //     title: 'Remain',
                //     data: 'remain',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
                {
                    title: 'Status',
                    data: function (row: any) {
                        if (!row.status) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.status) {
                            case 'open':
                                return 'On process';
                            case 'reject':
                                return 'rejected';
                            case 'approved':
                                return 'Approved';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                }
                // {
                //     title: 'Is Lap ',
                //     data: 'isLap',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
            ]
        }
    }

    get ids(): FormArray {
        return this.form.get('ids') as FormArray;
    }

    onCheckboxChange(event: any, id: number) {
        if (event.target.checked) {
            this.ids.push(this.fb.control(id));
        } else {
            const index = this.ids.controls.findIndex(x => x.value === id);
            if (index !== -1) {
                this.ids.removeAt(index);
            }
        }
    }

    isAllSelected = false;

    toggleSelectAll(event: any) {
        this.isAllSelected = event.target.checked;
        this.itemStatus.forEach((row: any) => {
            row.selected = this.isAllSelected;
        });
    }


    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    GetLeaveRemainning() {
        this._service.getAll().subscribe((resp: any) => {
            this.itemData = resp

        })
    }

    ViewLeave(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: '700px',
            data: {
                itemid: itemId,
                url: this._router.url,
                period: this.datePeriod
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            this.rerender();
            this.GetLeavePermission();
            this.GetLeaveRemainning();
            this._changeDetectorRef.markForCheck();
        });
    }


    CalculateLeave(qty: any, used: any) {
        const remain = qty - used;
        return remain
    }

    GetLeavePermission() {
        this._service.getLaevePermission().subscribe((resp: any)=>{
            this.leavePermission = resp
        })
    }


}
