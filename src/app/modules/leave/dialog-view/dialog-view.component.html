<div >
    <div class="flex flex-row justify-between border-b-2 border-gray-300">
        <div class="my-2 text-lg text-black font-bold">
            <h4>Leave Preview</h4>
        </div>
        <div class="flex flex-row mb-2 justify-end" *ngIf="this.itemData.status !== 'cancel'">
            <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                    *ngIf="this.data?.url === '/leave/leave-remainning'" (click)="Submit('cancel')">
                    Reject
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                    *ngIf="this.data?.url === '/leave/leave-approval'" (click)="Submit('reject')">
                    Reject
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'primary'"
                    *ngIf="this.data?.url === '/leave/leave-approval'" (click)="Submit('approved')">
                    Approve
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                    *ngIf="this.data?.url === '/admin-leave/leave' && (this.itemData.status === 'open' || this.itemData.status === 'approved')" (click)="Submit('cancel')">
                    Reject
                </button>
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 my-2 border-b-2 border-gray-300">
        <div class="flex flex-col gap-2 mb-2 mx-2">
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Leave No. </h4>
                </div>
                <div class="w-4/6">
                    <span class="font-bold text-gray-700">: {{this.itemData?.code ?? '-'}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Name </h4>
                </div>
                <div class="w-4/6">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.employee?.firstname ?? '-')}}
                        {{(this.itemData?.employee?.lastname ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Leave Period </h4>
                </div>
                <div class="w-4/6">
                    <span class="font-bold text-gray-700">: {{this.itemData?.employee?.currentStartPeriod | date: 'dd/MM/yyyy'}} to {{this.itemData?.employee?.currentEndPeriod | date: 'dd/MM/yyyy'}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Leave Type </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.leaveType?.name ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Leave Time </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(changeType(this.itemData?.type) ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Start Date </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(formatDate(this.itemData?.dateStart ) ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>End Date </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(formatDate(this.itemData?.dateEnd) ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Amount </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.qtyDay ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Reason </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.reason ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2" *ngIf="this.itemData?.statusRemark">
                <div class="w-2/6">
                    <h4>Reason Reject</h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.statusRemark ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2" *ngIf="this.itemData?.hrRemark">
                <div class="w-2/6">
                    <h4>HR Reason Reject</h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.hrRemark ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2 mb-2">
                <div class="w-2/6">
                    <h4>Approver </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.head?.firstname ?? '-')}}
                        {{(this.itemData?.head?.lastname ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2 mb-2" *ngIf="this.itemData?.file">
                <div class="w-2/6">
                    <h4>Attachments </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <a class="flex h-full w-full flex-col items-left justify-left no-underline"
                        [href]="this.itemData?.file" target="_blank"> <span class="font-bold text-gray-700 underline">: File</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-row mt-2 justify-center">
        <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3 mb-2" mat-flat-button [color]="'accent'" (click)="onClose()">
                Close
            </button>
        </div>
    </div>
</div>
