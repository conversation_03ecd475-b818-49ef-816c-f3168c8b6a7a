<div>
    <div class="flex flex-row justify-between border-b-2 border-gray-300">
        <div class="my-2 text-lg text-black font-bold">
            <h4>Leave Preview</h4>

        </div>
    </div>
    <div class="grid grid-cols-1 my-2 border-b-2 border-gray-300">
        <div class="flex flex-col gap-2 mb-2 text-sm md:text-lg mx-0 md:mx-2">
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Name </h4>
                </div>
                <div class="w-4/6">
                    <span class="font-bold text-gray-700">: {{(this.data.fullName ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Leave Type </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.data.leaveType ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Leave Time </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.data?.time ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Date </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{data.date ?? '-'}}</span>
                </div>
            </div>

            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Amount day(s)</h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.data?.amount ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Reason </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.data?.reason ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2 mb-2">
                <div class="w-2/6">
                    <h4>Approver </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{ this.data?.approver ?? '-'}}</span>
                </div>
            </div>
            <div class="flex flex-col mt-2 gap-2 mb-2 p-4 bg-orange-50 border-[1px] border-orange-500 rounded-md" *ngIf="this.data?.warningStatus === false">
                <div class=" flex flex-row w-full gap-2 border-b-2 border-orange-400 items-center py-2">
                    <mat-icon class="text-orange-500 font-bold icon-size-7" svgIcon="heroicons_outline:exclamation-triangle"></mat-icon> 
                    <p class="text-orange-500 font-bold text-lg"> Warning</p>
                </div>
                <div class="w-full whitespace-normal">
                    <span class="font-normal text-orange-400"> {{ this.data?.warningText ?? '-'}}</span>
                </div>
            </div>
        </div>
        
    </div>
    <div class="flex flex-row mt-2 justify-center">
        <div
            class="flex items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate gap-2">
            <button mat-flat-button [color]="'accent'" (click)="onClose()">
                Cancel
            </button>
            <button mat-flat-button [color]="'primary'" (click)="Submit()">
                Submit
            </button>
        </div>
    </div>