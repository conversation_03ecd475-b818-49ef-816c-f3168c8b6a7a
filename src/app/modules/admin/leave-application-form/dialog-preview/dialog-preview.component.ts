import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';

import { CommonModule } from '@angular/common';
import { DateTimeToSQL } from 'app/helper';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { LeaveService } from 'app/modules/leave/leave.service';

@Component({
  selector: 'app-dialog-view-leave-application-form',
  standalone: true,
  imports: [
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule
  ],
  templateUrl: './dialog-preview.component.html',
  styleUrls: ['./dialog-preview.component.scss']
})
export class DialogPreviewComponent implements OnInit {
  form: FormGroup;
  flashErrorMessage: string;
  flashMessage: 'success' | 'error' | null = null;
  isLoading: boolean = false;
  searchInputControl: FormControl = new FormControl();
  selectedProduct: any | null = null;
  filterForm: FormGroup;
  tagsEditMode: boolean = false;
  totalPrice: number;
  itemData: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  user: any;
  constructor(
    public dialogRef: MatDialogRef<DialogPreviewComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _changeDetectorRef: ChangeDetectorRef,
    private _service: LeaveService,
    private toastr: ToastrService,
    private fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder,
    private _matDialog: MatDialog
  ) {
    this.user = JSON.parse(localStorage.getItem('user'))
    // console.log(this.data);


  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

  onClose() {
    this.dialogRef.close();
  }

  showFlashMessage(type: 'success' | 'error'): void {
    // Show the message
    this.flashMessage = type;

    // Mark for check
    this._changeDetectorRef.markForCheck();

    // Hide it after 3 seconds
    setTimeout(() => {
      this.flashMessage = null;
      // Mark for check
      this._changeDetectorRef.markForCheck();
    }, 3000);
  }

  formatDate(data: any) {
    const dateFormat = DateTimeToSQL(data, 'dd/MM/yyyy')
    return dateFormat
  }

  Submit() {
    this.dialogRef.close(true)
  }

  reasonRequiredIfRejected(statusControlName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const formGroup = control.parent;
      if (!formGroup) return null; // ต้องแน่ใจว่าเป็นฟอร์มกลุ่ม

      const statusControl = formGroup.get(statusControlName);
      if (statusControl?.value === 'reject' && !control.value) {
        return { reasonRequired: true };
      }
      return null;
    };

  }

  changeType(data: any) {
    switch (data) {
      case 'half_day_morning':
          return 'Half day morning';
      case 'half_day_afternoon':
          return 'Half day afternoon';
      case 'full_day':
          return 'Full day';
      case 'consecutive_full_day_and_morning':
          return 'Consecutive Full Day and Half Day Morning';
      case 'half_afternoon_consecutive':
          return 'Half Day Afternoon and Consecutive Full Day';
      case 'consecutive_full_day_and_both_half':
          return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
      default:
          return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
  }
  }


}
