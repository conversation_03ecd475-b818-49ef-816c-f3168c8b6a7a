<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto bg-card m-4 p-4 rounded-md">
        <div class="flex flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
            <div>
                <h2 class=" mb-2 truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                    Leave Request
                </h2>
            </div>
        </div>
        <div class="flex flex-row justify-center pb-2 my-5 w-full">
            <form class="overflow-hidden px-0 md:px-8  mb-4 flex flex-col w-full" [formGroup]="form">

              <div class="flex flex-col md:flex-row gap-4 justify-center w-full">
                    <div class="flex flex-col w-full md:w-full">
                      <div class="-mx-3 md:flex mb-0 mt-0">
                        <div class="md:min-w-full px-3 mb-2 md:mb-0">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                                <mat-label>Employee</mat-label>
                                <input
                                    matInput
                                    [formControl]="employeeFilter"
                                    [matAutocomplete]="employeeAutoComplete"
                                    placeholder="Search Employee"
                                />
                                <mat-autocomplete
                                    #employeeAutoComplete="matAutocomplete"
                                    (optionSelected)="onSelectEmployer($event.option.value, 'manual')">
                                    <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                                        {{item.firstname}} {{item.lastname}}
                                    </mat-option>
                                </mat-autocomplete>
                            </mat-form-field>
                        </div>
                    </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Leave Type</mat-label>
                                    <mat-select placeholder="Please Select" formControlName="leaveTypeId" (selectionChange)="calculateMaxdate($event)">
                                        <mat-option *ngFor="let item of leavePermission;" value="{{item.leaveType?.id}}"
                                            [disabled]="CalculateLeave(item.qtyDay, item.usedDay) === 0">
                                            {{item?.leaveType?.name}} , Remain
                                            [<strong>{{CalculateLeave(item?.qtyDay,item?.usedDay)}}</strong>] , Date Expire : {{item?.expireDate | date : 'dd/MM/yyyy'}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="flex flex-col md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-label>Time</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'" class="flex flex-col"
                                    formControlName="type" (change)="calculateDays()">
                                    <mat-radio-button value="half_day_morning">Half Day Morning </mat-radio-button>
                                    <mat-radio-button value="half_day_afternoon">Half Day Afternoon </mat-radio-button>
                                    <mat-radio-button value="full_day">Full Day </mat-radio-button>
                                    <mat-radio-button value="consecutive_full_day_and_morning">Consecutive Full Day and Half Day Morning</mat-radio-button>
                                        <mat-radio-button value="half_afternoon_consecutive">Half Day Afternoon and Consecutive Full Day </mat-radio-button>
                                        <mat-radio-button value="consecutive_full_day_and_both_half">Half Day Afternoon And Consecutive Full Day And Half Day Morning </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Start - End</mat-label>
                                    <mat-date-range-input [rangePicker]="picker" (click)="picker.open()">
                                        <input matStartDate placeholder="Start date" name="dateStart"
                                            formControlName="dateStart" (dateChange)="calculateDays()">
                                        <input matEndDate placeholder="End date" name="dateEnd"
                                            formControlName="dateEnd" (dateChange)="calculateDays()">
                                    </mat-date-range-input>
                                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                                    <mat-date-range-picker #picker></mat-date-range-picker>
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-full">
                        <div class="-mx-3 md:flex mb-0 mt-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                                    <mat-label>Default Approver</mat-label>
                                    <input
                                        matInput
                                        [formControl]="approverFilter"
                                        [matAutocomplete]="approverAutoComplete"
                                        placeholder="Search Approver"
                                    />
                                    <mat-autocomplete
                                        #approverAutoComplete="matAutocomplete"
                                        (optionSelected)="onSelectApprover($event.option.value, 'manual')">
                                        <mat-option *ngFor="let item of filterApprover | async" [value]="item">
                                            {{item.firstname}} {{item.lastname}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Reason</mat-label>
                                    <textarea matInput [rows]="5" formControlName="reason"></textarea>
                                    <mat-hint>(Use 200 character maximum. Ex. Has a headache or has a
                                        stomachache)</mat-hint>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0 mt-2">
                            <div class="w-full px-3 mb-2 md:mb-0">
                                <asha-image-upload [initial]="itemData?.imageUrl"
                                    (uploadSuccess)="uploadSuccess($event)"></asha-image-upload>
                                <mat-hint class="text-sm text-gray-400">(Supports file types: JPEG, PNG, and PDF, with a maximum size of 1 MB.)</mat-hint>
                            </div>
                        </div>
                        <!-- <div class="-mx-3 md:flex mb-0 mt-2" *ngIf="errorMessage" class="error">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <p>{{ errorMessage }}</p>
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div
                        class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Process
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
