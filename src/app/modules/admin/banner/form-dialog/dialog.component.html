<div class="md:max-w-lg">
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">Create new banner</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">Edit banner</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Name</mat-label>
                            <input matInput [placeholder]="''" formControlName="name">
                            <mat-error> Name is required </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="-mx-3 md:flex mb-0 mt-2">
                        <div class="w-full px-3 mb-2 md:mb-0">
                            <asha-image-upload [initial]="itemData?.imageUrl"
                                (uploadSuccess)="uploadSuccess($event)"></asha-image-upload>
                            <mat-hint class="text-sm text-gray-400">(Supports file types: JPEG, PNG, and PDF, with a maximum size of 1 MB.)</mat-hint>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
</div>