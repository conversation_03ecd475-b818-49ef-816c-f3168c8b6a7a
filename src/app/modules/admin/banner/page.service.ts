import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { data } from './mock-data';

@Injectable({
  providedIn: 'root'
})
export class BannerService {

  private _banner: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/banner/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }



  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/banner`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/banner/${id}`, data)
  }

  getbannerById(id:any) {
    return this.http.get(`${environment.apiUrl}/api/banner/`+ id).pipe(
      tap((resp: any) => {
        this._banner.next(resp.data);
      }),
    )
  }
  getbanner() {
    return this.http.get(environment.apiUrl + '/api/banner').pipe(
      tap((resp: any) => {
        this._banner.next(resp);
      }),
    )
  }
  
  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/banner/' + id)
  }
}
