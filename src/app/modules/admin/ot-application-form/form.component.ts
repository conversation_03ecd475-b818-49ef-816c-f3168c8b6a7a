import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatInputModule } from '@angular/material/input';
import { difference } from 'lodash';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DateTime } from 'luxon';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { DateTimeToISO, DateTimeToJSDate } from 'app/helper';
import { MatDialog } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { OtService } from 'app/modules/ot/ot.service';
import { AutocompleteConfig, GenericAutocompleteComponent } from 'app/modules/share/generic-autocomplete/generic-autocomplete.component';

@Component({
  selector: 'leave-application-form',
  standalone: true,
  templateUrl: './form.component.html',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatRadioModule,
    MatInputModule,
    MatAutocompleteModule,
    GenericAutocompleteComponent,
  ]
})
export class OtApplicationFormComponent implements OnInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  itemData: any;
  form: FormGroup;
  getform: FormGroup;
  leaveType: any[] = [];
  employee: any;
  startDate: Date | null = null;
  endDate: Date | null = null;
  dayDifference: number | null = null;
  ///approver filter
  approverFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  projectFilter = new FormControl('');
  filterProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];
  leavePermission: any;
  ProjectData: any;
  errorMessage: string | null = null;
  timeOptions: string[] = [];
  selectedTime: string = '';
  duration: number | null = null;
  timeEnd: string;
  timeStart: string;
  id: any = '';
  employeeFilter = new FormControl('');
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  employeedata: any[] = [];

  // Configurations
  projectApproverConfig: AutocompleteConfig = {
    valueKey: 'id',
    displayKeys: ['firstname', 'lastname'],
    searchKeys: ['firstname', 'lastname'],
    displaySeparator: ' '
  };

  /**
   * Constructor
   */
  constructor(
    private _service: OtService,
    private _fb: FormBuilder,
    private _fuseConfirmationService: FuseConfirmationService,
    private imageUploadService: ImageUploadService,
    private toastr: ToastrService,
    private _router: Router,
    private _activateRoute: ActivatedRoute,
    private _matDialog: MatDialog
  ) {


    this.leaveType = this._activateRoute.snapshot.data.leaveType
    this.ProjectData = this._activateRoute.snapshot.data.ProjectData;
    this.approval = this._activateRoute.snapshot.data.approval
    this.id = this._activateRoute.snapshot.params.id
    this.employeedata = this._activateRoute.snapshot.data.employee
    this.filterApprover.next(this.approval.slice());
    this.filterProject.next(this.ProjectData.slice());
    this.filterEmployee.next(this.employeedata.slice());
    this.employee = JSON.parse(localStorage.getItem('user'))
    // console.log(this.employeedata,'data')
  }

  ngOnInit(): void {
    this.form = this._fb.group({
      date: null,
      timeStart: null,
      timeEnd: null,
      detail: null,
      projectId: null,
      employeeId: null,
      headId: null,
      approverId: null,
    });

    if (this.id) {
      this._service.getOTById(this.id).subscribe((resp: any) => {
        
        const projectdata = this.ProjectData.find(item => item.id === resp.project?.id)
        if (projectdata) {
          this.projectFilter.setValue(`(${projectdata.code}) ${projectdata.name}`);
        }
        const headgetbyid = this.approval.find(item => item.id === resp.head?.id)
        if (headgetbyid) {
          this.approverFilter.setValue(`${headgetbyid.firstname} ${headgetbyid.lastname}`);
        }
        const employeeData = this.employeedata.find(item => item.id === resp.employee?.id)
        if (employeeData) {
          this.employeeFilter.setValue(`${employeeData.firstname} ${employeeData.lastname}`);
        }

        this.duration = resp.qtyHour
        const timeIn = resp.timeStart ? resp.timeStart.replace(/:00$/, '') : resp.timeIn;
        const timeOut = resp.timeEnd ? resp.timeEnd.replace(/:00$/, '') : resp.timeEnd;
        this.timeStart = timeIn
        this.timeEnd = timeOut
        this.form.patchValue({
          ...resp,
          date: resp.date,
          projectId: +resp.project?.id,
          headId: resp.head?.id,
          timeStart: timeIn,
          timeEnd: timeOut,
          employeeId: resp.employee?.id,
          approverId: resp?.approver?.id,
        })
      })
    } else {
      this.form.patchValue({
        date: DateTime.now().toFormat('yyyy-MM-dd'),
        timeStart: null,
        timeEnd: null,
        detail: null,
        projectId: null,
        employeeId: null,
        headId: null,
        approverId: null,
      });

      this.getform = this._fb.group({
        date: DateTime.now().toFormat('yyyy-MM-dd'),
        employeeId: null,
      });
      const head = this.approval.find(item => item.id === this.employee?.head)
      if (head) {
        this.approverFilter.setValue(`${head.firstname} ${head.lastname}`);
      }
    }
    this.approverFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterBrand();
      });
    this.projectFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterProject();
      });
    this.employeeFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterEmployee();
      });
    this.generateTimeList()

  }

  Submit() {
    if (this.duration < 1 || this.duration === null || this.duration === undefined) {
      this.toastr.error('Please select time start and time end.')
      return;
    }
    let data = this.form.value;
    if (!this.id) {
      this._service.createOT(data).subscribe({
        error: (err) => {
          this.toastr.error(err.error.message)
        },
        complete: () => {
          this.toastr.success('Successed')
          this._router.navigate(['/admin-ot/ot'])
        },
      });
    } else {

      this._service.updateOT(data, this.id).subscribe({
        error: (err) => {
          this.toastr.error(err.error.message[0])
        },
        complete: () => {
          this.toastr.success('Successed')
          this._router.navigate(['/admin-ot/ot'])
        },
      });
    }
  }

  changeDateFormat() {
    this.form.patchValue({
      date: DateTime.fromISO(this.form.value.date).toFormat('yyyy-MM-dd')
    });

    this.getOTtime()
  }

  getOTtime() {
    this._service.getOTtime(this.form.value).subscribe((resp: any) => {
      const timeIn = resp.timeIn ? resp.timeIn.replace(/:00$/, '') : resp.timeIn;
      const timeOut = resp.timeOut ? resp.timeOut.replace(/:00$/, '') : resp.timeOut;
      this.timeEnd = timeOut
      this.form.patchValue({
        timeStart: timeIn,
        timeEnd: timeOut,
      });
      this.calculateDuration();

      console.log(this.form.value)
    })
  }


  backTo() {
    this._router.navigate(['/admin-ot/ot'])
  }

  files: File[] = [];
  onSelect(event, input: any) {
    if (input === 'addfile') {

      this.form.patchValue({
        file: event[0],
        file_name: event[0].name,
      });
    }
  }

  uploadSuccess(event): void {
    this.imageUploadService.upload(event).subscribe({
      next: (resp: any) => {
        // console.log(resp);

        this.form.patchValue({
          file: resp.pathUrl
        });
        // console.log(this.form.value);

      },
      error: (err) => {
        this.toastr.error(JSON.stringify(err))
      },
    })
  }



                 /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

  protected _filterBrand() {
    if (!this.approval) {
      return;
    }
    let search = this.approverFilter.value;
    console.log(search, 's');

    if (!search) {
      this.filterApprover.next(this.approval.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterApprover.next(
      this.approval.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }
  protected _filterProject() {
    if (!this.ProjectData) {
      return;
    }
    let search = this.projectFilter.value;
    console.log(search, 's');

    if (!search) {
      this.filterProject.next(this.ProjectData.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }


    this.filterProject.next(
      this.ProjectData.filter(item =>
        (item.code.toLowerCase() + ' ' + item.name.toLowerCase()).includes(search)
      )
    );
  }
  onSelectApprover(event: any, type: any) {
    if (!event) {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        headId: selectedData.id,
      });
      this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Found');
      return;
    }
  }
  onSelectProject(event: any, type: any) {
    if (!event) {
      if (this.projectFilter.invalid) {
        this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Project Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        projectId: selectedData.id,
        approverId: selectedData?.employee?.id,
      });
      this.projectFilter.setValue(`(${selectedData.code}) ${selectedData.name}`);
    } else {
      if (this.projectFilter.invalid) {
        this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Project Found');
      return;
    }
  }
  generateTimeList(): void {
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const formattedHour = String(hour).padStart(2, '0');
        const formattedMinute = String(minute).padStart(2, '0');
        this.timeOptions.push(`${formattedHour}:${formattedMinute}`);
      }
    }
  }

  calculateDuration(): void {
    const startTime = this.form.controls['timeStart'].value;
    const endTime = this.form.controls['timeEnd'].value;

    if (startTime && endTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      const startInMinutes = startHours * 60 + startMinutes;
      const endInMinutes = endHours * 60 + endMinutes;

      // หากสิ้นสุดน้อยกว่าหรือเท่ากับเริ่มต้น แสดงว่าเป็นเวลาข้ามวัน เช่น 22:00 - 02:00
      if (endInMinutes <= startInMinutes) {
        this.duration = ((1440 - startInMinutes) + endInMinutes) / 60; // 1440 = นาทีใน 1 วัน
      } else {
        this.duration = (endInMinutes - startInMinutes) / 60;
      }
    } else {
      this.duration = null; // ยังเลือกเวลาไม่ครบ
    }
  }


  isTimeDisabled(time: string): boolean {
    const startTime = this.timeEnd

    if (!startTime) {
      return false;
    }

    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const [timeHours, timeMinutes] = time.split(':').map(Number);

    const startInMinutes = startHours * 60 + startMinutes;
    const currentTimeInMinutes = timeHours * 60 + timeMinutes;

    return currentTimeInMinutes <= startInMinutes;
  }
  protected _filterEmployee() {
    if (!this.employeedata) {
      return;
    }
    let search = this.employeeFilter.value;
    // console.log(search, 's');

    if (!search) {
      this.filterApprover.next(this.employeedata.slice());
      return;
    } else {
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterEmployee.next(
      this.employeedata.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }
  onSelectEmployer(event: any, type: any) {
    if (!event) {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      // console.log(selectedData, 1);

      this.form.patchValue({
        employeeId: selectedData.id,
        headId: selectedData?.head?.id,
      });
      // console.log(this.employer)
      const employer = this.employeedata.find(item => item.id === selectedData?.id)
      if (employer) {
        const head = this.approval.find(item => item.id === employer?.head.id)
        // console.log(head,'head')
        if (head) {
          this.approverFilter.setValue(`${head.firstname} ${head.lastname}`);
        }
        this.getform.patchValue({
          employeeId: selectedData.id,
        })
        this.getOTtime()
      }

      this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.employeeFilter.invalid) {
        this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Found');
      return;
    }
  }
}
