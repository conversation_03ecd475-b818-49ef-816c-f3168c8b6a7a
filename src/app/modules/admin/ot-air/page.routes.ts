import { Routes } from '@angular/router';
import { inject } from '@angular/core';
import { EmployeeService } from 'app/modules/hr/employee/page.service';
import { OtairComponent } from './ot-air.component';
import { ProjectService } from 'app/modules/hr/project/page.service';
import { OtairApplicationFormComponent } from '../ot-air-application-form/form.component';
import { ZoneService } from 'app/modules/hr/zone/page.service';
import { FloorService } from 'app/modules/hr/floor/page.service';
import { OtairSettingComponent } from '../ot-email/form.component';
import { OtairService } from 'app/modules/ot-air/ot-air.service';


export default [
    {
        path: 'ot-air',
        component: OtairComponent,
        resolve: {
            ProjectData: () => inject(ProjectService).getProject(),
            employees: () => inject(EmployeeService).getEmployee(),
        },
    },
    {
        path: 'otair-application-form',
        component: OtairApplicationFormComponent,
        resolve: {
            email :  () => inject(OtairService).getAppSetting('MAIL_OT'),
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            ProjectData: () => inject(ProjectService).getProject(),
            ZoneData: () => inject(ZoneService).getZone(),
            FloorData: () => inject(FloorService).getFloor(),
        }
    },
    {
        path: ':id',
        component: OtairApplicationFormComponent,
        resolve: {
            email :  () => inject(OtairService).getAppSetting('MAIL_OT'),
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            ProjectData: () => inject(ProjectService).getProject(),
            ZoneData: () => inject(ZoneService).getZone(),
            FloorData: () => inject(FloorService).getFloor(),
        }
    },
    {
        path: 'otair-setting-form',
        component: OtairSettingComponent,
        // resolve: {
        //     leaveType: () => inject(EmployeeService).getLeaveType(),
        // }
    },

] as Routes;
