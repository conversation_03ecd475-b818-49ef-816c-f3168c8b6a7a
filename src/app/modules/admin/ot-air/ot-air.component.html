<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-col md:flex-row justify-between pb-2 my-5">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl mx-2">
                    Overtime Air History
                </h2>
            </div>
            <div class="flex flex-row justify-end pb-2 my-2 gap-2">
                <button mat-flat-button [color]="'primary'" (click)="this.rerender()">
                    <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
                    <span class="ml-2"> Search</span>
                </button>
                <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
                    <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
                    <span class="ml-2"> Reset</span>
                </button>
                <button mat-flat-button [color]="'primary'" (click)="gotootrequest()">
                    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
                    <span class="ml-2"> OT Air Request</span>
                </button>
            </div>
        </div>
        <form [formGroup]="form">
            <div class="flex flex-col md:flex-row justify-start pb-2 my-2 gap-2">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
                    <mat-label>Project</mat-label>
                    <input matInput [formControl]="projectFilter" [matAutocomplete]="projectAutoComplete"
                        placeholder="Search Project" />
                    <mat-autocomplete #projectAutoComplete="matAutocomplete"
                        (optionSelected)="onSelectProject($event.option.value, 'manual')">
                        <mat-option *ngFor="let item of filterProject | async" [value]="item">
                            ({{item.code}}) {{item.name}}
                        </mat-option>
                    </mat-autocomplete>
                </mat-form-field>
                <!-- <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
                    <mat-label>Request Date</mat-label>
                    <input matInput [matDatepicker]="picker" [formControlName]="'date'" placeholder="Date"
                        (dateChange)="dateChange()">
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                </mat-form-field> -->
                <mat-form-field [ngClass]="formFieldHelpers" class="w-1/4">
                    <mat-label>Start - End</mat-label>
                    <mat-date-range-input [rangePicker]="picker1">
                        <input matStartDate formControlName="dateStart" placeholder="Start" (dateChange)="dateChange()">
                        <input matEndDate formControlName="dateEnd" placeholder="End" (dateChange)="dateChange()">
                    </mat-date-range-input>
                    <mat-datepicker-toggle matIconSuffix [for]="picker1"></mat-datepicker-toggle>
                    <mat-date-range-picker #picker1></mat-date-range-picker>
                </mat-form-field>
                <div class="flex gap-2 w-1/4">
                    <mat-form-field class="w-full">
                        <mat-label>Time From</mat-label>
                        <mat-select placeholder="Start Time" formControlName="timeStart">
                            <mat-option *ngFor="let time of timeOptions" [value]="time">
                                {{ time }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>

                    <!-- End Time -->
                    <mat-form-field class="w-full">
                        <mat-label>To</mat-label>
                        <mat-select placeholder="End Time" formControlName="timeEnd">
                            <mat-option *ngFor="let time of timeOptions" [value]="time">
                                {{ time }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <mat-form-field [ngClass]="formFieldHelpers" class="w-1/4">
                    <mat-label>Search by Name</mat-label>
                    <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoComplete"
                        placeholder="Search Requester" />
                    <mat-autocomplete #AutoComplete="matAutocomplete"
                        (optionSelected)="onSelectEmployee($event.option.value, 'manual')">
                        <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                            {{item.fullName}}
                        </mat-option>
                    </mat-autocomplete>
                </mat-form-field>
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
                    <mat-label>Status</mat-label>
                    <mat-select [formControlName]="'status'" placeholder="Select status" multiple>
                        <mat-option *ngFor="let item of this.status;" value="{{item.value}}">
                            {{item.name}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

            </div>

        </form>
        <div class="overflow-auto">
            <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full"></table>
        </div>
    </div>
</div>
<ng-template #leaveNo let-data="adtData">
    <p class=" text-blue-500 underline cursor-pointer" (click)="ViewLeave(data)">{{data.code}}</p>
</ng-template>
<ng-template #fullName let-data="adtData">
    <p class=" ">{{data.employee.firstname}} {{data.employee.lastname}}</p>
</ng-template>
<ng-template #date let-data="adtData">
    <p class=" ">{{data.date | date : 'dd/MM/yyyy'}}</p>
</ng-template>