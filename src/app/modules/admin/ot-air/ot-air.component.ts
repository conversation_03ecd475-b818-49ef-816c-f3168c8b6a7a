import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateTime } from 'luxon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { DateTimeToSQL } from 'app/helper';
import { ActivatedRoute, Router } from '@angular/router';
import { OtService } from 'app/modules/ot/ot.service';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { OtairService } from 'app/modules/ot-air/ot-air.service';
import { DialogViewComponent } from 'app/modules/ot-air/dialog-view/dialog-view.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { slice } from 'lodash';

@Component({
    selector: 'admin-ot-air',
    standalone: true,
    templateUrl: './ot-air.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        MatSelectModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        MatAutocompleteModule,
    ]
})
export class OtairComponent implements OnInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild('fullName') fullName: any;
    @ViewChild('date') date: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    itemData: any;
    itemStatus: any;
    form: FormGroup;
    leavePermission: any;
    employee: any;
    datePeriod: string = '';
    start: string = '';
    end: string = '';
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    ProjectData: any[] = [];
    projectFilter = new FormControl('');
    filterProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    status: any[] = [
        {
            value: 'open',
            name: 'Request'
        },
        {
            value: 'head_approved',
            name: 'Reviewed'
        },
        {
            value: 'approved',
            name: 'Approved'
        },
        {
            value: 'cancel',
            name: 'Canceled'
        },
        {
            value: 'reject',
            name: 'Rejected'
        },
    ]

    employeeFilter = new FormControl('');
    filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    employees: any[] = [];
    timeOptions: any[] = [];
    /**
     * Constructor
     */
    constructor(
        private _service: OtairService,
        private toastr: ToastrService,
        private fb: FormBuilder,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activated: ActivatedRoute,
    ) {
        this.ProjectData = this._activated.snapshot.data.ProjectData;
        this.employees = this._activated.snapshot.data.employees
        this.filterProject.next(this.ProjectData.slice());
        this.filterEmployee.next(this.employees.slice());
        this.employee = JSON.parse(localStorage.getItem('user'))
        this.form = this.fb.group({
            status: [[]],
            projectId: [],
            date: [],
            dateStart: [],
            dateEnd: [],
            timeStart: [],
            timeEnd: [],
            employeeId: [],
        })
    }


    ngOnInit(): void {
        // this.GetLeaveRemainning()
        setTimeout(() =>
            this.loadTable());

        this.generateTimeList();

        this.projectFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._filterProject();
            });

        this.employeeFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._filterEmployee();
            });

        // โหลดค่าที่เคยเลือกไว้
        this.loadPreviousFormData();

        // Subscribe เพื่อเก็บข้อมูลใหม่ทุกครั้งที่มีการเปลี่ยน
        this.form.valueChanges.subscribe(formValue => {
            localStorage.setItem('searchFormOtAir', JSON.stringify(formValue));
        });
    }
    protected _onDestroy = new Subject<void>();
    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    filterDate(dateStart: string, dateEnd: string) {
        if (!dateStart || !dateEnd) {
            return '';
        }

        const start = DateTime.fromISO(dateStart).startOf('day').toFormat('yyyy-MM-dd HH:mm:ss');
        const end = DateTime.fromISO(dateEnd).endOf('day').toFormat('yyyy-MM-dd HH:mm:ss');
        return `$btw:${start},${end}`;
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    ...(this.form.value.status.length > 0 && { 'filter.status': '$in:' + this.form.value.status.join(',') }),
                    'filter.project.id': this.form.value.projectId,
                    'filter.employee.id': this.form.value.employeeId,
                    ...(this.form.value.timeStart && { 'filter.timeStart': '$gte:' + this.form.value.timeStart }),
                    ...(this.form.value.timeEnd && { 'filter.timeEnd': '$lte:' + this.form.value.timeEnd }),
                    ...((this.form.value.dateStart && this.form.value.dateEnd) && { 'filter.date': this.filterDate(this.form.value.dateStart, this.form.value.dateEnd) }),
                    // ...(filterDate ? { 'filter.date': filterDate } : {}),
                    // 'filter.employee': this.employee?.id
                },
                    // dataTablesParameters.employeeId = this.employee.id
                    this._service.getOtPage(dataTablesParameters).subscribe({
                        next: (resp: any) => {
                            this.itemStatus = resp.data;
                            callback({
                                recordsTotal: resp.meta.totalItems,
                                recordsFiltered: resp.meta.totalItems,
                                data: resp.data
                            });
                        }, error: () => {
                            this.toastr.error('Invalid')
                        }
                    })
            },
            order: [[0, 'desc']],
            columns: [
                // {
                //     title: '<input type="checkbox" (click)="toggleSelectAll($event)" />',
                //     data: null,
                //     defaultContent: '',
                //     ngTemplateRef: {
                //         ref: this.btNg,
                //     },
                //     className: 'w-15 text-center h-10',
                //     orderable: false
                // },
                {
                    title: 'OT No.',
                    data: 'code',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.leaveNo,
                    },
                    className: 'h-8 w-30 text-center'
                },
                {
                    title: 'First Name',
                    data: 'employee.firstname',
                    defaultContent: '-',
                    className: 'w-40 text-left'
                },
                {
                    title: 'Last Name',
                    data: 'employee.lastname',
                    defaultContent: '-',
                    className: 'w-40 text-left'
                },
                {
                    title: 'Project Code',
                    data: `project.code`,
                    defaultContent: '-',
                    className: 'w-30 text-center'
                },
                {
                    title: 'Project',
                    data: `project.name`,
                    defaultContent: '-',
                    className: 'w-30 text-left whitespace-nowrap'
                },
                {
                    title: 'Request Date',
                    data: 'date',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.date,
                    },
                    className: 'text-left'
                },

                {
                    title: 'Start Time',
                    data: function (row: any) {
                        return (row.timeStart)
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },


                {
                    title: 'End Time',
                    data: function (row: any) {
                        return (row.timeEnd)
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                // {
                //     title: 'Remain',
                //     data: 'remain',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
                {
                    title: 'Approver',
                    data: 'head',
                    defaultContent: '-',
                    className: 'text-left',
                    render: function (data: any) {
                        return data ? data.firstname + ' ' + data.lastname : '-';
                    }
                },
                // {
                //     title: 'Approver',
                //     data: 'approver',
                //     defaultContent: '-',
                //     className: 'text-left',
                //     render: function (data: any) {
                //         return data ? data.firstname + ' ' + data.lastname : '-';
                //     }
                // },
                {
                    title: 'Status',
                    data: function (row: any) {
                        if (!row.status) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.status) {
                            case 'open':
                                return 'Request';
                            case 'head_approved':
                                return 'Reviewed';
                            case 'approved':
                                return 'Approved';
                            case 'cancel':
                                return 'Canceled';
                            case 'reject':
                                return 'Rejected';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left whitespace-nowrap'
                }
                // {
                //     title: 'Is Lap ',
                //     data: 'isLap',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
            ]
        }
    }

    get ids(): FormArray {
        return this.form.get('ids') as FormArray;
    }

    onCheckboxChange(event: any, id: number) {
        if (event.target.checked) {
            this.ids.push(this.fb.control(id));
        } else {
            const index = this.ids.controls.findIndex(x => x.value === id);
            if (index !== -1) {
                this.ids.removeAt(index);
            }
        }
    }

    generateTimeList(): void {
        for (let hour = 0; hour < 24; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
                const formattedHour = String(hour).padStart(2, '0');
                const formattedMinute = String(minute).padStart(2, '0');
                this.timeOptions.push(`${formattedHour}:${formattedMinute}`);
            }
        }
    }

    isAllSelected = false;

    toggleSelectAll(event: any) {
        this.isAllSelected = event.target.checked;
        this.itemStatus.forEach((row: any) => {
            row.selected = this.isAllSelected;
        });
    }


    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    GetLeaveRemainning() {
        this._service.getAll().subscribe((resp: any) => {
            this.itemData = resp

        })
    }

    ViewLeave(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: '700px',
            data: {
                itemid: itemId,
                url: this._router.url,
                period: this.datePeriod,
                type: 'single'
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            this.rerender();
            // this._changeDetectorRef.markForCheck();
        });
    }


    CalculateLeave(qty: any, used: any) {
        const remain = qty - used;
        return remain
    }


    clearData() {
        this.form.reset();
        this.employeeFilter.reset();
        this.projectFilter.reset();
        this.form.patchValue({
            status: []
        })
        localStorage.removeItem('searchFormOtAir');
        this.rerender();
    }

    dateChange() {

        let formValue = this.form.value
        if (formValue.date) {
            formValue.date = DateTime.fromISO(this.form.value.date).toFormat('yyyy-MM-dd');
            this.form.patchValue({
                date: formValue.date
            })
        }
        if (formValue.dateStart && formValue.dateEnd) {
            formValue.dateStart = DateTime.fromISO(this.form.value.dateStart).toFormat('yyyy-MM-dd');
            formValue.dateEnd = DateTime.fromISO(this.form.value.dateEnd).toFormat('yyyy-MM-dd');
            this.start = `$gte:${formValue.dateStart}`,
                this.end = `$lte:${formValue.dateEnd}`,
                this.form.patchValue({
                    dateStart: formValue.dateStart,
                    dateEnd: formValue.dateEnd
                })
        }
    }

    protected _filterProject() {
        if (!this.ProjectData) {
            return;
        }
        let search = this.projectFilter.value;
        console.log(search, 's');

        if (!search) {
            this.filterProject.next(this.ProjectData.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }


        this.filterProject.next(
            this.ProjectData.filter(item =>
                (item.code.toLowerCase() + ' ' + item.name.toLowerCase()).includes(search)
            )
        );
    }
    onSelectProject(event: any, type: any) {
        if (!event) {
            if (this.projectFilter.invalid) {
                this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Project Selected');
            return;
        }

        if (type === 'manual') {
            const selectedData = event; // event จะเป็นออบเจ็กต์ item

            if (selectedData) {
                this.form.patchValue({
                    projectId: selectedData.id,
                });
                this.projectFilter.setValue(`(${selectedData.code}) ${selectedData.name}`);
            } else {
                if (this.projectFilter.invalid) {
                    this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
                }
                console.log('No Project Found');
                return;
            }
        } else if (type === 'auto') {
            const selectedData = this.ProjectData.find(item => item.id === event)

            if (selectedData) {
                this.form.patchValue({
                    projectId: selectedData.id,
                });
                this.projectFilter.setValue(`(${selectedData.code}) ${selectedData.name}`);
            } else {
                if (this.projectFilter.invalid) {
                    this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
                }
                console.log('No Project Found');
                return;
            }
        }


    }
    gotootrequest() {
        this._router.navigate(['/admin-ot-air/otair-application-form'])
    }

    protected _filterEmployee() {
        if (!this.employeeFilter) {
            return;
        }
        let search = this.employeeFilter.value;

        if (!search) {
            this.filterEmployee.next(this.employees.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }


        this.filterEmployee.next(
            this.employees.filter(item =>
                item?.fullName.toLowerCase().includes(search)
            )
        );
    }

    onSelectEmployee(event: any, type: any) {
        if (!event) {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Selected');
            return;
        }

        if (type === 'manual') {
            const selectedData = event; // event จะเป็นออบเจ็กต์ item

            if (selectedData) {
                this.form.patchValue({
                    employeeId: selectedData.id,
                });
                this.employeeFilter.setValue(`${selectedData.fullName}`);
            } else {
                if (this.employeeFilter.invalid) {
                    this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
                }
                console.log('No Employee Found');
                return;
            }
        } else if (type === 'auto') {
            const selectedData = this.employees.find(item => item.id === event)

            if (selectedData) {
                this.form.patchValue({
                    employeeId: selectedData.id,
                });
                this.employeeFilter.setValue(`${selectedData.fullName}`);
            } else {
                if (this.employeeFilter.invalid) {
                    this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
                }
                console.log('No Employee Found');
                return;
            }
        }

    }

    loadPreviousFormData() {
        const savedForm = localStorage.getItem('searchFormOtAir');
        if (savedForm) {
            const formValue = JSON.parse(savedForm);

            // patch ค่ากลับเข้า form
            this.form.patchValue(formValue);

            // หากต้องการให้ autocomplete แสดง label ที่เคยเลือกไว้ ต้องเรียก function ที่เติม label ให้
            if (formValue.projectId) {
                this.onSelectProject(formValue.projectId, 'auto');
            }
            if (formValue.employeeId) {
                this.onSelectEmployee(formValue.employeeId, 'auto');
            }
        }
    }

}
