import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatInputModule } from '@angular/material/input';
import { difference } from 'lodash';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DateTime } from 'luxon';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { DateTimeToISO, DateTimeToJSDate } from 'app/helper';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { ZoneService } from 'app/modules/hr/zone/page.service';
import { MatDividerModule } from '@angular/material/divider';
import { PictureComponent } from 'app/modules/hr/picture/picture.component';
import { Dialog } from '@angular/cdk/dialog';
import { OtairService } from 'app/modules/ot-air/ot-air.service';

@Component({
  selector: 'ot-air-setting-form-email',
  standalone: true,
  templateUrl: './form.component.html',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    MatDialogModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatRadioModule,
    MatInputModule,
    MatAutocompleteModule,
    MatDividerModule,
  ]
})
export class OtairSettingComponent implements OnInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  itemData: any;
  form: FormGroup;
  getform: FormGroup;
  leaveType: any[] = [];
  employee: any;
  startDate: Date | null = null;
  endDate: Date | null = null;
  dayDifference: number | null = null;
  ///approver filter
  approverFilter = new FormControl('');
  projectFilter = new FormControl('');
  employeeFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  filterProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];
  leavePermission: any;
  ProjectData: any;
  errorMessage: string | null = null;
  timeOptions: string[] = [];
  selectedTime: string = '';
  duration: number | null = null;
  ZoneData: any;
  FloorData: any;
  timeEnd: string;
  timeStart: string;
  imgSelected: string;
  employeedata: any[]
  key: string = 'MAIL_OT'
  /**
   * Constructor
   */
  constructor(
    private _service: OtairService,
    private _fb: FormBuilder,
    private _fuseConfirmationService: FuseConfirmationService,
    private imageUploadService: ImageUploadService,
    private toastr: ToastrService,
    private _router: Router,
    private _activateRoute: ActivatedRoute,
    private _matDialog: MatDialog,
    public dialog: MatDialog,
    ) {
    this.employee = JSON.parse(localStorage.getItem('user'))
  }

  ngOnInit(): void {
    this.form = this._fb.group({
      value: '',
      type: 'TEXT'
    });
  }

  Submit() {
    let data = this.form.value;
    this._service.updateSetting(this.key,data).subscribe({
      error: (err) => {
        this.toastr.error(err.error.message[0])
      },
      complete: () => {
        this.toastr.success('Successed')
        this._router.navigate(['/admin-ot-air/otair-setting-form'])
      },
    });
  }

  backTo() {
    this._router.navigate(['/admin-ot-air/ot-air'])
  }

}
