<div class="md:max-w-lg">
    <h1 mat-dialog-title class="text-xl font-semibold mb-4">Reset Password</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <div class="md:w-full" *ngIf="this.data.type !== 'EDIT'"> 
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>New Password</mat-label>
                            <input id="password" matInput type="password" [formControlName]="'password'"
                                #passwordField />
                            <button mat-icon-button type="button" (click)="
                                passwordField.type === 'password'
                                    ? (passwordField.type = 'text')
                                    : (passwordField.type = 'password')
                            " matSuffix>
                                @if (passwordField.type === 'password') {
                                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                                }
                                @if (passwordField.type === 'text') {
                                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                                }
                            </button>
                            <mat-error> Password is required </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
</div>