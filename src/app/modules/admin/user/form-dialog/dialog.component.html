<div class="md:max-w-lg">
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">Create new user</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">Edit user</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Firstname</mat-label>
                            <input matInput [placeholder]="''" formControlName="firstname">
                            <mat-error> Firstname is required </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Lastname</mat-label>
                            <input matInput [placeholder]="''" formControlName="lastname">
                            <mat-error> Lastname is required </mat-error>
                        </mat-form-field>
                        
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Username</mat-label>
                            <input matInput [placeholder]="''" formControlName="username">
                            <mat-error> Username is required </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full" *ngIf="this.data.type !== 'EDIT'"> 
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Password</mat-label>
                            <input id="password" matInput type="password" [formControlName]="'password'"
                                #passwordField />
                            <button mat-icon-button type="button" (click)="
                                passwordField.type === 'password'
                                    ? (passwordField.type = 'text')
                                    : (passwordField.type = 'password')
                            " matSuffix>
                                @if (passwordField.type === 'password') {
                                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                                }
                                @if (passwordField.type === 'text') {
                                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                                }
                            </button>
                            <mat-error> Password is required </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Role</mat-label>
                            <mat-select [formControlName]="'roles'" multiple>
                                <mat-option *ngFor="let item of role;" value="{{item}}">
                                    {{item}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4" *ngIf="this.data.type === 'EDIT'">
                        <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="active">
                            <mat-radio-button [value]="true">Active </mat-radio-button>
                            <mat-radio-button [value]="false">Inactive </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
</div>