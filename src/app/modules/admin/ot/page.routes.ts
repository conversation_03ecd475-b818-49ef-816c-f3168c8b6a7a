import { Routes } from '@angular/router';
import { inject } from '@angular/core';
import { EmployeeService } from 'app/modules/hr/employee/page.service';
import { OtComponent } from './ot.component';
import { ProjectService } from 'app/modules/hr/project/page.service';
import { OtService } from 'app/modules/ot/ot.service';
import { OtApplicationFormComponent } from '../ot-application-form/form.component';


export default [
    {
        path     : 'ot',
        component: OtComponent,
        resolve: {
            ProjectData: () => inject(ProjectService).getProject(),
            employees: () => inject(EmployeeService).getEmployee(),
        },
    },
        {
            path     : 'ot-application-form',
            component: OtApplicationFormComponent,
            resolve: {
                leaveType: () => inject(EmployeeService).getLeaveType(),
                employee: () => inject(EmployeeService).getEmployee(),
                approval: () => inject(EmployeeService).getApproval(),
                ProjectData: () => inject(ProjectService).getProject(),

            }
        },
        {
                path: 'ot-application-edit/:id',
                component: OtApplicationFormComponent,
                resolve: {
                    leaveType: () => inject(EmployeeService).getLeaveType(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),
                    ProjectData: () => inject(ProjectService).getProject(),
                }
            },

] as Routes;
