import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { removeEmpty } from 'app/helper';
import { param } from 'jquery';

@Injectable({
  providedIn: 'root'
})
export class ClientListService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _title: BehaviorSubject<any[] | null> = new BehaviorSubject(null);




  constructor(private http: HttpClient) { }



  getAll(dataTablesParameters: any) {
    console.log(dataTablesParameters);

    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/company/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  getAllContact(dataTablesParameters: any) {
    console.log(dataTablesParameters);

    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/contact/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }



  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/company`, data)
  }
  createContact(data: any) {
    return this.http.post(`${environment.apiUrl}/api/contact`, data)
  }
  checkDuplicate(data: any) {
    return this.http.post(`${environment.apiUrl}/api/company/check_duplicate_company`, data)
  }
  update(id: number, data: object) {
    return this.http.put(`${environment.apiUrl}/api/company/${id}`, data)
  }
  updateContact(id: number, data: object) {
    return this.http.put(`${environment.apiUrl}/api/contact/${id}`, data)
  }
  getClientListId(id: any) {
    return this.http.get(`${environment.apiUrl}/api/company/` + id).pipe(
      tap((resp: any) => {
        this._user.next(resp.data);
      }),
    )
  }
  getContactId(id: any) {
    return this.http.get(`${environment.apiUrl}/api/contact/` + id).pipe(
      tap((resp: any) => {
        this._user.next(resp.data);
      }),
    )
  }
  getCompany() {
    return this.http.get(environment.apiUrl + '/api/company').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }

  getCategory() {
    return this.http.get(environment.apiUrl + '/api/category').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }

  getActivity() {
    return this.http.get(environment.apiUrl + '/api/activity').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/contact/' + id)
  }
}
