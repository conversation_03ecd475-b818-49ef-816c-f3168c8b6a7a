<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a class="whitespace-nowrap text-primary-500 cursor-pointer">Company list</a>
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon class="text-secondary icon-size-5" [svgIcon]="'heroicons_mini:chevron-right'"></mat-icon>
                    <a class="ml-1 text-primary-500">Create new Company</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Create New Company
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Edit Company
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-2 md:p-6">
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
                [formGroup]="form">
                <div class="flex flex-col md:flex-row gap-4 justify-center">
                    <div class="flex flex-col w-full md:w-3/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Company name</mat-label>
                                    <input matInput [formControlName]="'name'"
                                        [placeholder]="'Please enter company name'">
                                </mat-form-field>
                                <div class="w-1/3">
                                    <button class="px-6 mt-6" mat-flat-button [color]="'primary'" (click)="CheckName()">
                                        Check
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-col gap-4 justify-center mb-4">
                    <mat-label>Categories</mat-label>
                    <div class="grid grid-cols-4 w-full md:w-3/3" formArrayName="companyCategory">
                        <div class="-mx-3 md:flex mb-0" *ngFor="let item of categorys; let i = index">
                            <mat-checkbox [formControlName]="i">
                                {{ item.name }}
                            </mat-checkbox>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Building</mat-label>
                                    <input matInput [formControlName]="'building'"
                                        [placeholder]="'Please enter building'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Street</mat-label>
                                    <input matInput [formControlName]="'street'" [placeholder]="'Please enter street'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>District</mat-label>
                                    <input matInput [formControlName]="'district'"
                                        [placeholder]="'Please enter district'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>City</mat-label>
                                    <input matInput [formControlName]="'city'" [placeholder]="'Please enter city'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Post code</mat-label>
                                    <input matInput [formControlName]="'postCode'"
                                        [placeholder]="'Please enter post code'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Country</mat-label>
                                    <input matInput [formControlName]="'country'"
                                        [placeholder]="'Please enter country'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Email</mat-label>
                                    <input matInput [formControlName]="'email'" [placeholder]="'Please enter email'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Website</mat-label>
                                    <input matInput [formControlName]="'webSite'"
                                        [placeholder]="'Please enter post code'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">

                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Phone number</mat-label>
                                    <input matInput [formControlName]="'phoneNumber'"
                                        [placeholder]="'Please enter phone number example (+669999999)'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Fax number</mat-label>
                                    <input matInput [formControlName]="'faxNumber'"
                                        [placeholder]="'Please enter fax number (+662222222)'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                      <div class="flex flex-col w-full md:w-1/2 px-3 mb-2 md:mb-0">
                        <mat-label class="font-bold">Status</mat-label>
                        <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="active">
                          <mat-radio-button [value]="true">Active</mat-radio-button>
                          <mat-radio-button [value]="false">Inactive </mat-radio-button>
                        </mat-radio-group>
                      </div>
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div
                        class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Save
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
                <div class="overflow-auto mt-10">
                    <p class="font-bold text-lg mb-2">Customer List</p>
                    <table id="excel-table"
                      class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
                      <thead class="text-gray-700 uppercase bg-gray-200 text-md dark:bg-gray-700 dark:text-gray-400">
                        <tr class="border-[1px] border-black">
                          <th scope="col" class="px-2 py-2 w-[20px] border-[1px] border-gray-900">No.</th>
                          <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Full name</th>
                          <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Position</th>
                          <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Email</th>
                          <th scope="col" class="px-2 py-2 w-auto border-[1px] border-gray-900">Phone</th>
                        </tr>
                      </thead>
                      <tbody>
                        <ng-container *ngFor="let item of companyContact; let i = index">
                          <!-- Row for Department -->
                          <tr class="font-normal cursor-pointer" [ngClass]="{'bg-red-200': item?.isHoliday}"(click)="editContact(item.id)">
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900 ">
                              {{ i + 1}}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                              {{ ConvertFullName(item) }}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                              {{ item?.position ?? '' }}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                              {{ item?.email ?? '' }}
                            </td>
                            <td class="px-2 py-2 text-md border-[1px] border-gray-900">
                              {{ item?.mobileNumber ?? '' }}
                            </td>
                          </tr>
                        </ng-container>
                        <tr *ngIf="companyContact.length === 0">
                          <td class="px-2 py-2 text-md border-[1px] border-gray-900 text-wrap font-semibold text-center" colspan="9">
                            No Data
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
            </form>

        </div>

    </div>
</div>
