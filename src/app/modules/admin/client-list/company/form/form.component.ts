import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    AbstractControl,
    FormArray,
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { ClientListService } from '../../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { debounceTime, forkJoin, lastValueFrom, switchMap, map, of, Observable, ReplaySubject, Subject } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';
import { DateTimeToISO, DateTimeToJSDate, DateTimeToSQL } from 'app/helper';
import { catchError, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { DateTime } from 'luxon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
@Component({
    selector: 'form-company',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule,
        MatAutocompleteModule,
        MatCheckboxModule
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class CompanyFormComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;

    item1Data: any = [];
    item2Data: any = [];
    subCategory: any = [];
    itemSupplier: any = [];

    itemBrand: any = [];
    itemBrandModel: any = [];
    itemCC: any = [];
    itemColor: any = [];

    formData: FormGroup;
    formData2: FormGroup;

    files: File[] = [];
    warehouseData: any;
    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    position: any[] = [];
    companyContact: any[] = [];
    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;

    ///approver filter
    approverFilter = new FormControl('');
    filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    approval: any[] = [];
    categorys: any[] = []
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: ClientListService,
        private dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _toast: ToastrService,
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        this.categorys = this._activatedRoute.snapshot.data.categorys

    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            id: null,
            name: [''],
            building: [''],
            street: [''],
            district: [''],
            city: [''],
            postCode: [''],
            country: [''],
            phoneNumber: [''],
            faxNumber: [''],
            email: [''],
            webSite: [''],
            companyCategory: this._fb.array([]),
            active: true
        })

        if (this.Id) {
            this.addCheckboxes();
            this._Service.getClientListId(this.Id).subscribe((resp: any) => {
                const selectedCategories = resp.companyCategories.map(category => category.category.id);
                this.itemData = resp
                this.companyContact = this.itemData.contacts
                
                this.form.patchValue({
                    ...this.itemData,

                })
                this.patchSelectedCategories(selectedCategories);
            })
            // Patch Value สำหรับ FormArray

        } else {
            this.addCheckboxes();
        }

        this.approverFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._filterBrand();
            });
    }

    addCheckboxes(): void {
        this.categorys.forEach(() => {
            const control = new FormControl(false); // ค่าเริ่มต้นเป็น false
            (this.form.get('companyCategory') as FormArray).push(control);
        });
    }

    patchSelectedCategories(selectedCategories: number[]): void {
        const companyCategory = this.form.get('companyCategory') as FormArray;
        console.log(selectedCategories);

        this.categorys.forEach((category, index) => {
            if (selectedCategories.includes(category.id)) {
                companyCategory.at(index).setValue(true); // ตั้งค่า checkbox ที่ตรงกับ category ที่เลือกเป็น true
            }
        });
    }



    onCheckboxChange(index: number): void {
        const companyCategory = this.form.get('companyCategory') as FormArray;
        const selectedCategory = this.categorys[index];
        const isChecked = companyCategory.at(index).value;

        if (isChecked) {
            companyCategory.at(index).setValue(false); // ถ้าเลือกให้ยกเลิก
        } else {
            companyCategory.at(index).setValue(true); // ถ้าไม่ได้เลือกให้เลือก
        }
    }

    changeDate() {
        const date: any = DateTimeToISO(this.form.value.registerDate, 'yyyy-MM-dd');
        if (date) {
            const formatDate: DateTime = DateTime.fromISO(date);
            const jsDate: Date = formatDate.toJSDate();
            const luxonDate = DateTime.fromJSDate(jsDate);
            const probationDate = luxonDate.plus({ days: 90 });
            const formattedDate = probationDate.toFormat('yyyy-MM-dd');
            this.form.patchValue({
                passProbationDate: formattedDate
            })
        } else {
            this._toast.error('no register date')
        }
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }



    onSelect(event: any) {
        this.files.push(...event.addedFiles);
        // Trigger Image Preview
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);
    }

    onRemove(event: any) {
        this.files.splice(this.files.indexOf(event), 1);
    }

    Submit(): void {
        const selectedCategories = this.form.value.companyCategory
            .map((checked: boolean, i: number) => (checked ? this.categorys[i] : null))
            .filter((v: any) => v !== null);
        let formValue = this.form.value;
        formValue.companyCategory = selectedCategories
        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit value",
                message: "Do you want to edit value? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    this._Service.update(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['employee']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add value",
                message: "Do you want to save data ?",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    this._Service.create(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['client-list/company']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }

    ConvertFullName(data: any) {
        return `${data?.firstname} ${data?.lastname}`;
    }

    backTo() {
        this._router.navigate(['client-list/company'])
    }

               /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

    protected _filterBrand() {
        if (!this.approval) {
            return;
        }
        let search = this.approverFilter.value;
        // console.log(search, 's');

        if (!search) {
            this.filterApprover.next(this.approval.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterApprover.next(
            this.approval.filter(item =>
                (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
                item.firstname.toLowerCase().includes(search) ||
                item.lastname.toLowerCase().includes(search)
            )
        );
    }

    onSelectApprover(event: any, type: any) {
        if (!event) {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Approver Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                headId: selectedData.id,
            });
            this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
        } else {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Approver Found');
            return;
        }
    }

    CheckName() {
        this._Service.checkDuplicate(this.form.value).subscribe({
            next: (resp: any) => {
                // this._router.navigate(['client-list']);
                this._toast.success('This name is available.')
            },
            error: (err: any) => {
                this._toast.error(err.error.message)
                this.form.reset()
            }
        });
    }

    editContact(id: any) {
        this._router.navigate(['/client-list/contact/edit/' + id]);
    }



}
