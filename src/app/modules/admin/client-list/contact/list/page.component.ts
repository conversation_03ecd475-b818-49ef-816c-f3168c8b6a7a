import { CommonModule, C<PERSON><PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ClientListService } from '../../page.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { orderBy } from 'lodash';
import { CompanyAutocompleteComponent } from 'app/modules/share/company-autocomplete/company-autocomplete.component';
@Component({
    selector: 'app-page-contact',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        CompanyAutocompleteComponent
        
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class ContactListComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    category: any[] = [];
    workShift: any;
    approval: any;
    categorys: any[] = [];
    activitys: any[] = [];
    companys: any[] = [];
    data: any;
    companyName = new FormControl('');
    
    constructor(
        private _service: ClientListService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,

    ) {
        
        this.activitys = this._activatedRoute.snapshot.data.activitys
        this.companys = this._activatedRoute.snapshot.data.companys
        this.form = this._fb.group({
            companyId: '',
            activityId: '',
        })
    }
    
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        let formValue = this.form.value
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            scrollX: true,
            order: [[2, 'asc']],
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.company.id': this.form.value.companyId,
                    'filter.contactActivities.activity.id': this.form.value.activityId,
                }
                this._service.getAllContact(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                },
                {
                    title: 'No.',
                    data: null,
                    className: 'w-10 text-center',
                    render: function (data, type, row, meta) {
                        return meta.row + 1;
                    }
                },
                {
                    title: 'Firstname',
                    data: 'firstname',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Lastname',
                    data: 'lastname',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Type',
                    data: function (row: any) {
                        if (!row.type) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.type) {
                            case 'individual':
                                return 'Individual';
                            case 'business':
                                return 'Business';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                     className: 'text-left'
                },
                {
                    title: 'Company',
                    data: 'company.name',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Position',
                    data: 'position',
                    defaultContent: '-',
                    className: 'text-left'
                },
            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    opendialogAdd() {
        this._router.navigate(['client-list/contact/form'])
    }

    openDialogEdit(id: any) {
        this._router.navigate(['client-list/contact/edit/' + id])
    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to delete? ",
            message: "Please check the information. If the information is deleted, it cannot be retrieved.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('ดำเนินการลบสำเร็จ');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
    onChangeType() {
        this.rerender()
    }

    clearData() {
        this.companyName.setValue('')
        this.form.reset();
        this.rerender();
    }

    handleValue(data:any) {
        // console.log(data);
        this.form.patchValue({
            companyId: data?.id
        })
    }
}
