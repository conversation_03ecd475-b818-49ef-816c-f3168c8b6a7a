import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    AbstractControl,
    FormArray,
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { ClientListService } from '../../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { debounceTime, forkJoin, lastValueFrom, switchMap, map, of, Observable, ReplaySubject, Subject } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';
import { DateTimeToISO, DateTimeToJSDate, DateTimeToSQL } from 'app/helper';
import { catchError, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { DateTime } from 'luxon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CompanyAutocompleteComponent } from 'app/modules/share/company-autocomplete/company-autocomplete.component';
import { StaffAutocompleteComponent } from 'app/modules/share/staff-autocomplete/staff-autocomplete.component';
@Component({
    selector: 'form-company',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule,
        MatAutocompleteModule,
        MatCheckboxModule,
        CompanyAutocompleteComponent,
        StaffAutocompleteComponent
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class ContactFormComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;

    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    position: any[] = [];
    // employees: any[] = [];

    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;

    ///approver filter
    approverFilter = new FormControl('');
    filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    approval: any[] = [];
    categorys: any[] = [];
    companys: any[] = [];
    activitys: any[] = [];
    companyName = new FormControl('');
    staffName = new FormControl('');

    //=======================================================================================================
    get employee(): FormArray { return this.form.get('employeeActivity') as FormArray; }

    employeeFilter: FormArray = this._fb.array([]);
    filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    employees: any[] = [];

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: ClientListService,
        private dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _toast: ToastrService,
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        this.activitys = this._activatedRoute.snapshot.data.activitys
        this.companys = this._activatedRoute.snapshot.data.companys
        this.employees = this._activatedRoute.snapshot.data.employees
        this.categorys = this._activatedRoute.snapshot.data.categorys
        this.filterEmployee.next(this.employees.slice());

    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            id: null,
            firstname: [''],
            lastname: [''],
            position: [''],
            email: [''],
            mobileNumber: [''],
            companyId: null,
            building: [''],
            street: [''],
            district: [''],
            city: [''],
            postCode: [''],
            country: [''],
            categoryIds: this._fb.array([]),
            active: true,
            employeeActivity: this._fb.array([]),
            type: ['individual'],
        })

        if (this.Id) {
          this.addCheckboxes();

          this._Service.getContactId(this.Id).subscribe((resp: any) => {
              const selectedCategories = resp.contactCategories.map(category => category.category.id);
              this.itemData = resp;
              this.form.patchValue({
                  ...this.itemData,
                  companyId: resp?.company?.id,
                  building: resp?.building,
                  city: resp?.city,
                  country: resp?.country,
                  district: resp?.district,
                  postCode: resp?.postCode,
                  street: resp?.street
              });
              this.companyName.setValue(this.itemData?.company?.name);
              this.patchSelectedCategories(selectedCategories);

              // Handle existing employee groups and their activities
              if (resp.contactActivities && resp.contactActivities.length > 0) {
                  resp.contactActivities.forEach((group: any, index: number) => {
                      this.addGroup(group);
                      this.employeeFilter.push(new FormControl(''));
                      this.employeeFilter.at(index).setValue(`${group.employee.firstname} ${group.employee.lastname}`);
                  });
              }
          });
      } else {
            this.addCheckboxes();
        }

        this.employeeFilter.valueChanges
        .pipe(takeUntil(this._onDestroy))
        .subscribe((value) => {
            value.forEach((filterValue, index) => {
                this._filterEmployee(index, filterValue);
            });
        });

    }

    addCheckboxes(): void {
      this.categorys.forEach(() => {
          const control = new FormControl(false); // ค่าเริ่มต้นเป็น false
          (this.form.get('categoryIds') as FormArray).push(control);
      });
    }

    patchSelectedCategories(selectedCategories: number[]): void {
      const categoryIds = this.form.get('categoryIds') as FormArray;
      this.categorys.forEach((category, index) => {

        if (selectedCategories.includes(category.id)) {
          categoryIds.at(index).setValue(true); // ตั้งค่า checkbox ที่ตรงกับ category ที่เลือกเป็น true
        }
      });
    }

      onCheckboxChange(index: number): void {
        const categoryIds = this.form.get('categoryIds') as FormArray;
        const selectedCategory = this.categorys[index];
        const isChecked = categoryIds.at(index).value;

        if (isChecked) {
            categoryIds.at(index).setValue(false); // ถ้าเลือกให้ยกเลิก
        } else {
            categoryIds.at(index).setValue(true); // ถ้าไม่ได้เลือกให้เลือก
        }
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    Submit(): void {
        let formValue = { ...this.form.value };
        formValue.employeeActivity = formValue.employeeActivity.map((group: any, index: number) => {
            const selectedActivities = group.activityIds
                .map((checked: boolean, i: number) => (checked ? this.activitys[i] : null))
                .filter((v: any) => v !== null);
            for(let i = 0; i < selectedActivities.length; i++) {
                selectedActivities[i] = selectedActivities[i].id;
            }
            return {
                ...group,
                activityIds: selectedActivities
            };
        });
        const selectedCategories = this.form.value.categoryIds
            .map((checked: boolean, i: number) => (checked ? this.categorys[i] : null))
            .filter((v: any) => v !== null);
        for(let i = 0; i < selectedCategories.length; i++) {
            selectedCategories[i] = selectedCategories[i].id;
        }
        formValue.categoryIds = selectedCategories;


        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit value",
                message: "Do you want to edit value? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    this._Service.updateContact(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['client-list/contact']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add value",
                message: "Do you want to save data ?",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    this._Service.createContact(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['client-list/contact']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }

    backTo() {
        this._router.navigate(['client-list/contact'])
    }

    /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

    protected _filterEmployee(index: number, search: string) {
        if (!this.employees) {
            return;
        }

        if (!search) {
            this.filterEmployee.next(this.employees.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        this.filterEmployee.next(
            this.employees.filter(item =>
                (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
                item.firstname.toLowerCase().includes(search) ||
                item.lastname.toLowerCase().includes(search)
            )
        );
    }

    onSelectEmployee(event: any, type: any,index: number) {
        if (!event) {
            if (this.employeeFilter.at(index).invalid) {
                this.employeeFilter.at(index).markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No employee Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item
        const employeeGroup = this.form.get('employeeActivity') as FormArray;

        if (selectedData) {
          const currentEmployeeGroup = employeeGroup.at(index) as FormGroup;
          currentEmployeeGroup.patchValue({
              employeeId: selectedData.id,
              employeeFilter:(`${selectedData.firstname} ${selectedData.lastname}`)
          });
            this.employeeFilter.at(index).setValue(`${selectedData.firstname} ${selectedData.lastname}`);
        }else {
            if (this.employeeFilter.at(index).invalid) {
                this.employeeFilter.at(index).markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No employee Found');
            return;
        }
    }

    handleValue(data: any, type:any) {
        if(type === 'company') {
            this.form.patchValue({
                companyId: data?.id,
                building: data?.building,
                city: data?.city,
                country: data?.country,
                district: data?.district,
                postCode: data?.postCode,
                street: data?.street
            })
            this.form.get('categoryIds').reset();

            let selectedCategory = [];
            for(let i = 0; i < data.companyCategories.length; i++) {
              selectedCategory[i] = data.companyCategories[i].category.id;
            }
            this.patchSelectedCategories(selectedCategory);

        } else if (type === 'staff') {
            this.form.patchValue({
                employeeId: data?.id
            })
        }
    }
    //=======================================================================================================
    addGroup(existingData: any = null): void {
        const group = this._fb.group({
            employeeId: [existingData?.employee?.id || ''],
            employeeFilter: [existingData?.employeeFilter || ''],
            activityIds: this._fb.array([])
        });

        const activityArray = group.get('activityIds') as FormArray;
        this.activitys.forEach((activity) => {
            const isChecked = existingData?.activities?.some(
                (a: any) => a.id === activity.id
            ) || false;
            activityArray.push(new FormControl(isChecked));
        });

        this.employee.push(group);
        this.employeeFilter.push(new FormControl(''));
    }
    removeGroup(index: number): void {
        this.employee.removeAt(index);
        this.employeeFilter.removeAt(index);
    }

    patchEmployeeActivities(groupIndex: number, selectedActivities: number[]): void {
      const group = this.employee.at(groupIndex);
      const activityArray = group.get('activityIds') as FormArray;

      this.activitys.forEach((activity, index) => {
          if (selectedActivities.includes(activity.id)) {
              activityArray.at(index).setValue(true);
          }
      });
  }

  clickEditCompany() {
    this._router.navigate(['client-list/company/edit', this.form.value.companyId]);
  }
}
