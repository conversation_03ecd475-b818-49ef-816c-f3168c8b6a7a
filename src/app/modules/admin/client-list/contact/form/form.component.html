<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a class="whitespace-nowrap text-primary-500 cursor-pointer">Customer list</a>
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon class="text-secondary icon-size-5" [svgIcon]="'heroicons_mini:chevron-right'"></mat-icon>
                    <a class="ml-1 text-primary-500">Customer</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Create New Customer
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Edit Customer
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-2 md:p-6">
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
                [formGroup]="form">

                <!-- Type selection (Individual or Business) -->
                <div class="flex flex-col md:flex-row gap-4 justify-start w-full mb-4">
                    <div class="flex flex-col w-full md:w-full">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-col md:min-w-full px-3 mb-2 md:mb-0 gap-2"
                                [ngClass]="formFieldHelpers">
                                <mat-label>Type</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'"
                                    formControlName="type">
                                    <mat-radio-button [value]="'individual'">Individual</mat-radio-button>
                                    <mat-radio-button [value]="'business'">Business</mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start w-full"
                    *ngIf="form.get('type')?.value === 'business'">
                    <div class="flex flex-col w-full md:w-full">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row items-center justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <app-company-autocomplete [data]="companys"
                                    (selectedValue)="handleValue($event, 'company')" [employeeFilter]="companyName"
                                    class="w-full"></app-company-autocomplete>
                                    <button [disabled]="form.value.companyId == null" mat-flat-button color="primary" (click)="clickEditCompany()">Edit</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-col gap-4 justify-center mb-4">
                    <mat-label>Categories</mat-label>
                    <div class="grid grid-cols-4 w-full md:w-3/3" formArrayName="categoryIds">
                        <div class="-mx-3 md:flex mb-0" *ngFor="let item of categorys; let i = index">
                            <mat-checkbox [formControlName]="i">
                                {{ item.name }}
                            </mat-checkbox>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Firstname</mat-label>
                                    <input matInput [formControlName]="'firstname'"
                                        [placeholder]="'Please enter firstname'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Lastname</mat-label>
                                    <input matInput [formControlName]="'lastname'"
                                        [placeholder]="'Please enter lastname'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Position</mat-label>
                                    <input matInput [formControlName]="'position'"
                                        [placeholder]="'Please enter position'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Building</mat-label>
                                    <input matInput [formControlName]="'building'"
                                        [placeholder]="'Please enter building'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Street</mat-label>
                                    <input matInput [formControlName]="'street'" [placeholder]="'Please enter street'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>District</mat-label>
                                    <input matInput [formControlName]="'district'"
                                        [placeholder]="'Please enter district'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>City</mat-label>
                                    <input matInput [formControlName]="'city'" [placeholder]="'Please enter city'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Post code</mat-label>
                                    <input matInput [formControlName]="'postCode'"
                                        [placeholder]="'Please enter post code'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Country</mat-label>
                                    <input matInput [formControlName]="'country'"
                                        [placeholder]="'Please enter country'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 justify-start">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Email</mat-label>
                                    <input matInput [formControlName]="'email'" [placeholder]="'Please enter email'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Mobile Phone</mat-label>
                                    <input matInput [formControlName]="'mobileNumber'"
                                        [placeholder]="'Please enter post mobile phone'">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2 ">
                              <div class="flex flex-col w-full md:w-1/2 px-3 mb-2 md:mb-0">
                                <mat-label class="font-bold">Status</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="active">
                                  <mat-radio-button [value]="true">Active</mat-radio-button>
                                  <mat-radio-button [value]="false">Inactive </mat-radio-button>
                                </mat-radio-group>
                              </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div formArrayName="employeeActivity">
                    <div *ngFor="let employee of employee.controls; let i = index">
                        <div [formGroupName]="i" class="border-gray-900 border-2 p-4 rounded-lg mb-4">
                            <mat-card class="w-full mb-4">
                                <mat-card-content>
                                    <a class="flex justify-between pb-2"> <mat-label>{{ 'Staff ' + (i + 1)
                                            }}</mat-label>
                                        <span class="ml-auto text-right">
                                            <button mat-flat-button class="text-right text-white" [color]="'warn'"
                                                (click)="removeGroup(i)"><mat-icon>delete</mat-icon>
                                            </button>
                                        </span>
                                    </a>
                                    <div class="flex flex-row w-full space-x-2">
                                        <div class="w-full flex flex-row space-x-2">
                                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                                                <!-- <mat-label>Employee</mat-label> -->
                                                <input matInput [formControl]="employeeFilter.at(i)"
                                                    [matAutocomplete]="employeeAutoComplete"
                                                    placeholder="Search Staff" />
                                                <mat-autocomplete #employeeAutoComplete="matAutocomplete"
                                                    (optionSelected)="onSelectEmployee($event.option.value, 'manual', i)">
                                                    <mat-option *ngFor="let item of filterEmployee | async"
                                                        [value]="item">
                                                        {{item.firstname}} {{item.lastname}}
                                                    </mat-option>
                                                </mat-autocomplete>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                    <!-- Activities -->
                                    <div class="flex flex-col md:flex-col gap-4 justify-center">
                                        <mat-label>Activity</mat-label>
                                        <div class="grid grid-cols-4 w-full md:w-3/3" formArrayName="activityIds">
                                            <div class="-mx-3 md:flex mb-0"
                                                *ngFor="let item of activitys; let actIndex = index">
                                                <mat-checkbox [formControlName]="actIndex">
                                                    {{ item.name }}
                                                </mat-checkbox>
                                            </div>
                                        </div>
                                    </div>
                                </mat-card-content>
                            </mat-card>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-start">
                    <button class="px-6" mat-flat-button [color]="'primary'" (click)="addGroup();">
                        Add Staff
                    </button>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div
                        class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Save
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
