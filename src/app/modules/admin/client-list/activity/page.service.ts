import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class ActivityService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _branch: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _department: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _position: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _permission: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;
    
    return this.http.get(`${environment.apiUrl}/api/activity/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  // getAll(dataTablesParameters: any): Observable<any> {
  //   return of(data).pipe(
  //     map((branch: any) => {
  //       return branch.data;
  //     })
  //   );
  // }

  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/activity`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/activity/${id}`, data)
  }

  getTitleByid(id:any) {
    return this.http.get(`${environment.apiUrl}/api/activity/`+ id).pipe(
      tap((resp: any) => {
        this._user.next(resp.data);
      }),
    )
  }
  getTitle() {
    return this.http.get(environment.apiUrl + '/api/activity').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }


  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/activity/' + id)
  }
}
