<div class="md:max-w-lg" >
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">Create new activity</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">Edit activity</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Code</mat-label>
                            <input matInput [placeholder]="''" formControlName="code">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Name</mat-label>
                            <input matInput [placeholder]="''" formControlName="name">
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4" *ngIf="this.data.type === 'EDIT'">
                        <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="active">
                            <mat-radio-button [value]="true">Active </mat-radio-button>
                            <mat-radio-button [value]="false">Inactive </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
</div>
