import { Routes } from '@angular/router';
import { inject } from '@angular/core';
import { ClientListService } from './page.service';
import { ClientListComponent } from './client-list.component';
import { CompanyListComponent } from './company/list/page.component';
import { ContactListComponent } from './contact/list/page.component';
import { ContactFormComponent } from './contact/form/form.component';
import { CompanyFormComponent } from './company/form/form.component';
import { EmployeeService } from 'app/modules/hr/employee/page.service';
import { ActivityComponent } from './activity/page.component';
import { CategoryComponent } from './category/page.component';

export default [
    {
        path: '',
        component: ClientListComponent,
        children: [
            {
                path     : 'company',
                component: CompanyListComponent,
                resolve: {
                    categorys: () => inject(ClientListService).getCategory(),
                },

            },
            {
                path: 'company/form',
                component: CompanyFormComponent,
                resolve: {
                    categorys: () => inject(ClientListService).getCategory(),

                },
            },
            {
                path: 'company/edit/:id',
                component: CompanyFormComponent,
                resolve: {
                    categorys: () => inject(ClientListService).getCategory(),

                },
            },
            {
                path     : 'contact',
                component: ContactListComponent,
                resolve: {
                    activitys: () => inject(ClientListService).getActivity(),
                    companys: () => inject(ClientListService).getCompany(),
                    employees: () => inject(EmployeeService).getEmployee(),
                },

            },
            {
                path: 'contact/form',
                component: ContactFormComponent,
                resolve: {
                    activitys: () => inject(ClientListService).getActivity(),
                    companys: () => inject(ClientListService).getCompany(),
                    employees: () => inject(EmployeeService).getEmployee(),
                    categorys: () => inject(ClientListService).getCategory(),

                },
            },
            {
                path: 'contact/edit/:id',
                component: ContactFormComponent,
                resolve: {
                    activitys: () => inject(ClientListService).getActivity(),
                    companys: () => inject(ClientListService).getCompany(),
                    employees: () => inject(EmployeeService).getEmployee(),
                    categorys: () => inject(ClientListService).getCategory(),

                },
            },
            {
                path     : 'category',
                component: CategoryComponent,

            },
            {
                path     : 'activity',
                component: ActivityComponent,

            },
        ]
    },

] as Routes;
