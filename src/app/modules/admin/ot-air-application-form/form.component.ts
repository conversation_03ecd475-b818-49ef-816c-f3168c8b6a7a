import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatInputModule } from '@angular/material/input';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DateTime } from 'luxon';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { Mat<PERSON><PERSON>og, MatDialogModule } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { OtairService } from 'app/modules/ot-air/ot-air.service';
import { GenericAutocompleteComponent } from 'app/modules/share/generic-autocomplete/generic-autocomplete.component';

@Component({
  selector: 'ot-air-application-form-admin',
  standalone: true,
  templateUrl: './form.component.html',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    MatDialogModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatRadioModule,
    MatInputModule,
    MatAutocompleteModule,
    MatDividerModule,
    GenericAutocompleteComponent
  ]
})
export class OtairApplicationFormComponent implements OnInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  itemData: any;
  form: FormGroup;
  getform: FormGroup;
  leaveType: any[] = [];
  employee: any;
  startDate: Date | null = null;
  endDate: Date | null = null;
  dayDifference: number | null = null;
  projectFilter = new FormControl('');
  filterProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];
  leavePermission: any;
  ProjectData: any;
  errorMessage: string | null = null;
  timeOptions: string[] = [];
  selectedTime: string = '';
  duration: number | null = null;
  ZoneData: any;
  FloorData: any;
  timeEnd: string;
  timeStart: string;
  imgSelected: string;
  email: any;
  employeedata: any[]
  isEditMode: boolean = false;
  otId: string | null = null;

  employeeConfig = {
    valueKey: 'id',
    displayKeys: ['firstname', 'lastname'],
    searchKeys: ['firstname', 'lastname'],
    displaySeparator: ' '
  };

  /**
   * Constructor
   */
  constructor(
    private _service: OtairService,
    private _fb: FormBuilder,
    private _fuseConfirmationService: FuseConfirmationService,
    private imageUploadService: ImageUploadService,
    private toastr: ToastrService,
    private _router: Router,
    private _activateRoute: ActivatedRoute,
    private _matDialog: MatDialog,
    public dialog: MatDialog,
  ) {
    // ตรวจสอบว่าเป็น edit mode หรือไม่
    this.otId = this._activateRoute.snapshot.paramMap.get('id');
    this.isEditMode = !!this.otId;

    this.leaveType = this._activateRoute.snapshot.data.leaveType
    this.ProjectData = this._activateRoute.snapshot.data.ProjectData;
    this.filterProject.next(this.ProjectData.slice());
    this.approval = this._activateRoute.snapshot.data.approval
    this.ZoneData = this._activateRoute.snapshot.data.ZoneData
    this.FloorData = this._activateRoute.snapshot.data.FloorData
    this.employeedata = this._activateRoute.snapshot.data.employee
    this.employee = JSON.parse(localStorage.getItem('user'))
    this.email = this._activateRoute.snapshot.data.email
  }

  ngOnInit(): void {
    this.imgSelected = this.FloorData[0].pic
    this.form = this._fb.group({
      date: DateTime.now(),
      timeStart: null,
      timeEnd: null,
      detail: null,
      projectId: null,
      zoneId: null,
      floorId: this.FloorData[0].id,  // ค่าเริ่มต้นของชั้น
      employeeId: null,
      headId: null,
      email: this.email.value,
    });
    this.getform = this._fb.group({
      date: DateTime.now(),
      employeeId: this.employee.id,
    });

    // ถ้าเป็น edit mode ให้โหลดข้อมูล
    if (this.isEditMode && this.otId) {
      this.loadOTData();
    } else {
      //
    }
    this.projectFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterProject();
      });
    // console.log(this.employee);
    this.generateTimeOptions()

  }

  Submit() {
    let data = this.form.value;

    if (this.isEditMode && this.otId) {
      // Update mode
      if (data.date) {
        data.date = DateTime.fromISO(data.date).toFormat('yyyy-MM-dd');
      }
      this._service.updateOT(data, this.otId).subscribe({
        error: (err) => {
          this.toastr.error(err.error.message[0])
        },
        complete: () => {
          this.toastr.success('Updated Successfully')
          this._router.navigate(['/admin-ot-air/ot-air'])
        },
      });
    } else {
      // Create mode
      data.date = DateTime.now().toFormat('yyyy-MM-dd')
      this._service.createOT(data).subscribe({
        error: (err) => {
          this.toastr.error(err.error.message[0])
        },
        complete: () => {
          this.toastr.success('Created Successfully')
          this._router.navigate(['/admin-ot-air/ot-air'])
        },
      });
    }
  }

  loadOTData() {
    this._service.getOTById(this.otId).subscribe({
      next: (resp: any) => {
        this.itemData = resp;
        this.populateForm(resp);
      },
      error: (err) => {
        this.toastr.error('Failed to load OT data');
        console.error(err);
      }
    });
  }

  populateForm(data: any) {
    // แปลงวันที่
    const date = data.date ? DateTime.fromISO(data.date) : DateTime.now();

    const timeStart = data.timeStart ? data.timeStart.replace(/:00$/, '') : data.timeStart;
    const timeEnd = data.timeEnd ? data.timeEnd.replace(/:00$/, '') : data.timeEnd;

    this.form.patchValue({
      date: date,
      timeStart: timeStart,
      timeEnd: timeEnd,
      detail: data.detail,
      projectId: data?.project?.id,
      zoneId: data?.zone?.id,
      floorId: data?.floor?.id,
      employeeId: data?.employee?.id,
      headId: data?.head?.id,
      email: this.email.value,
    });

    // ตั้งค่า project filter
    if (data.project) {
      this.projectFilter.setValue(`(${data.project.code}) ${data.project.name}`);
    }

    // ตั้งค่า floor image
    if (data.floor && data.floor.pic) {
      this.imgSelected = data.floor.pic;
    }

    // ตั้งค่า getform สำหรับ employee
    this.getform.patchValue({
      date: date.toFormat('yyyy-MM-dd'),
      employeeId: data.employeeId,
    });

    // คำนวณ duration
    this.calculateDuration();
  }

  checkLeave(leaveType: string, startDate: Date) {
    const currentDate = new Date();
    const differenceInDays = Math.floor(
      (new Date(startDate).getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    if (leaveType === 'Annual' && differenceInDays >= 7) {
      return { status: true, text: 'Valid' };
    }

    if (leaveType === 'Casual' && differenceInDays >= 3) {
      return { status: true, text: 'Valid' };
    }

    if (leaveType === 'Annual' && differenceInDays < 7) {
      return { status: false, text: 'Annual ot must be requested at least 7 days in advance.' };
    }

    if (leaveType === 'Casual' && differenceInDays < 3) {
      return { status: false, text: 'Casual ot must be requested at least 3 days in advance.' };
    }
    // If no conditions match, return true (no restrictions)
    return { status: true, text: 'No specific conditions for ot.' };
  }

  backTo() {
    this._router.navigate(['/admin-ot-air/ot-air'])
  }

  files: File[] = [];
  onSelect(event, input: any) {
    if (input === 'addfile') {

      this.form.patchValue({
        file: event[0],
        file_name: event[0].name,
      });
    }
  }

  uploadSuccess(event): void {
    this.imageUploadService.upload(event).subscribe({
      next: (resp: any) => {
        // console.log(resp);

        this.form.patchValue({
          file: resp.pathUrl
        });
        // console.log(this.form.value);

      },
      error: (err) => {
        this.toastr.error(JSON.stringify(err))
      },
    })
  }

  CalculateLeave(qty: any, used: any) {
    const remain = qty - used;
    return remain
  }

  onSelectionChange(event: any): void {

    const selectedId = event.value;
    const selectedItem = this.leavePermission.find(item => item.leaveType.id === selectedId);
    if (selectedItem && this.CalculateLeave(selectedItem.qtyDay, selectedItem.usedDay) === 0) {
      // แจ้ง Error ว่าคุณไม่สามารถเลือกตัวเลือกนี้ได้
      alert('You cannot select this ot type because the remaining days are 0.');
      this.toastr.error('You cannot select this ot type because the remaining days are 0.')
      // รีเซ็ตค่าในฟอร์มให้เป็น null
      this.form.get('leaveTypeId')?.setValue(null);
    }
  }

  calculateDays() {
    const dateStart: Date = this.form.get('dateStart')?.value;
    const dateEnd: Date = this.form.get('dateEnd')?.value;
    const timeOption = this.form.get('type')?.value;

    if (!dateStart || !dateEnd || !timeOption) {
      this.errorMessage = null;
      return;
    }

    const daysDifference =
      (new Date(dateEnd).getTime() - new Date(dateStart).getTime()) /
      (1000 * 60 * 60 * 24) +
      1;

    switch (timeOption) {
      case 'half_day_morning':
      case 'half_day_afternoon':
        if (daysDifference !== 1) {
          this.errorMessage = 'Half Day options can only be selected for 1 day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'full_day':
        if (daysDifference < 1) {
          this.errorMessage = 'Full Day requires at least 1 day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'consecutive_full_day_and_morning':
      case 'half_afternoon_consecutive':
        if (daysDifference <= 1) {
          this.errorMessage =
            'This option requires more than 1 consecutive day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'consecutive_full_day_and_both_half':
        if (daysDifference < 3) {
          this.errorMessage =
            'This option requires at least 3 consecutive days.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      default:
        this.errorMessage = 'Invalid option selected.';
        this.toastr.error(this.errorMessage)
        this.form.patchValue({
          dateEnd: null,
          dateStart: null,
        })
        return;
    }
    // หากไม่มีปัญหา
    this.errorMessage = null;
  }

                 /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();


  protected _filterProject() {
    if (!this.ProjectData) {
      return;
    }
    let search = this.projectFilter.value;
    console.log(search, 's');

    if (!search) {
      this.filterProject.next(this.ProjectData.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterProject.next(
      this.ProjectData.filter(item =>
        (item.code.toLowerCase() + ' ' + item.name.toLowerCase()).includes(search)
      )
    );
  }

  onSelectProject(event: any, type: any) {
    if (!event) {
      if (this.projectFilter.invalid) {
        this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Project Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        projectId: selectedData.id,
      });
      this.projectFilter.setValue(`(${selectedData.code}) ${selectedData.name}`);
    } else {
      if (this.projectFilter.invalid) {
        this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Project Found');
      return;
    }
  }
  private generateTimeOptions(): void {
    const start = 0; // 00:00
    const end = 24 * 60; // 24:00
    const step = 30; // 30 minutes
    for (let i = start; i < end; i += step) {
      const hours = Math.floor(i / 60).toString().padStart(2, '0');
      const minutes = (i % 60).toString().padStart(2, '0');
      this.timeOptions.push(`${hours}:${minutes}`);
    }
  }
  onStartTimeChange(): void {
    const startTime = this.form.controls['timeStart'].value;
    const endTime = this.form.controls['timeEnd'].value;

    if (startTime && endTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      const startInMinutes = startHours * 60 + startMinutes;
      const endInMinutes = endHours * 60 + endMinutes;

      if (startInMinutes >= endInMinutes) {
        this.form.controls['timeEnd'].reset(); // Clear End Time if invalid
      }
    }

    // Recalculate duration after resetting if necessary
    this.calculateDuration();
  }

  calculateDuration(): void {
    const startTime = this.form.controls['timeStart'].value;
    const endTime = this.form.controls['timeEnd'].value;

    if (startTime && endTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      const startInMinutes = startHours * 60 + startMinutes;
      const endInMinutes = endHours * 60 + endMinutes;

      // หากสิ้นสุดน้อยกว่าหรือเท่ากับเริ่มต้น แสดงว่าเป็นเวลาข้ามวัน เช่น 22:00 - 02:00
      if (endInMinutes <= startInMinutes) {
        this.duration = ((1440 - startInMinutes) + endInMinutes) / 60; // 1440 = นาทีใน 1 วัน
      } else {
        this.duration = (endInMinutes - startInMinutes) / 60;
      }
    } else {
      this.duration = null; // ยังเลือกเวลาไม่ครบ
    }
  }
  getOTtime() {
    let data = this.getform.value;
    data.date = DateTime.now().toFormat('yyyy-MM-dd')
    this._service.getOTtime(this.getform.value).subscribe((resp: any) => {

      const timeIn = resp.timeIn ? resp.timeIn.replace(/:00$/, '') : resp.timeIn;
      const timeOut = resp.timeOut ? resp.timeOut.replace(/:00$/, '') : resp.timeOut;
      this.timeStart = timeIn
      this.timeEnd = timeOut
      this.form.patchValue({
        timeStart: timeIn,
        timeEnd: timeOut,
      });
      console.log(this.form.value)
      this.calculateDuration();

    })
  }
  isTimeDisabled(time: string): boolean {
    const startTime = this.timeStart;

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentInMinutes = currentHours * 60 + currentMinutes;

    const [timeHours, timeMinutes] = time.split(':').map(Number);
    const currentTimeInMinutes = timeHours * 60 + timeMinutes;

    if (startTime && time === startTime) {
      return false;
    }

    if (currentTimeInMinutes < currentInMinutes) {
      return true;
    }

    if (startTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const startInMinutes = startHours * 60 + startMinutes;
      if (currentTimeInMinutes <= startInMinutes) {
        return true;
      }
    }

    return false;
  }
  isTimeEndDisabled(time: string): boolean {
    const timeEnd = this.timeEnd;

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentInMinutes = currentHours * 60 + currentMinutes;

    const [timeHours, timeMinutes] = time.split(':').map(Number);
    const currentTimeInMinutes = timeHours * 60 + timeMinutes;

    if (timeEnd && time === timeEnd) {
      return false;
    }

    if (currentTimeInMinutes < currentInMinutes) {
      return true;
    }

    if (timeEnd) {
      const [startHours, startMinutes] = timeEnd.split(':').map(Number);
      const startInMinutes = startHours * 60 + startMinutes;
      if (currentTimeInMinutes <= startInMinutes) {
        return true;
      }
    }

    return false;
  }
  selectFloor(imgObject: any, floorId: number): void {
    this.imgSelected = imgObject
    this.form.controls['floorId'].setValue(floorId);

    // this.dialog
    // .open(PictureComponent, {
    //     autoFocus: false,
    //     data: {
    //         imgSelected: imgObject,
    //     },
    // })
    // .afterClosed()
    // .subscribe(() => {
    // });
  }

  onSelectEmployer(data) {
    this.form.patchValue({
      headId: data?.head?.id,
    });
  }
}
