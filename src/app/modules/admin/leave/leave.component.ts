import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DateTime } from 'luxon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { DateTimeToSQL } from 'app/helper';
import { ActivatedRoute, Router } from '@angular/router';
import { LeaveService } from 'app/modules/leave/leave.service';
import { DialogViewComponent } from 'app/modules/leave/dialog-view/dialog-view.component';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';

@Component({
    selector: 'admin-leave',
    standalone: true,
    templateUrl: './leave.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        MatSelectModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule
    ]
})
export class AdminLeaveComponent implements OnInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild('fullName') fullName: any;
    @ViewChild('date') date: any;
    @ViewChild('dateStart') dateStart: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    itemData: any;
    itemStatus: any;
    form: FormGroup;
    leavePermission: any;
    employee: any;
    datePeriod: string = '';
    start: string = '';
    end: string = '';
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    leaveType: any[] = [];
    status: any[] = [
        {
            value: 'open',
            name: 'On process'
        },
        {
            value: 'approved',
            name: 'Approved'
        },
        {
            value: 'cancel',
            name: 'Canceled'
        },
        {
            value: 'reject',
            name: 'Rejected'
        },

    ]
    /**
     * Constructor
     */
    constructor(
        private _service: LeaveService,
        private toastr: ToastrService,
        private fb: FormBuilder,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activated: ActivatedRoute
    ) {
        this.leavePermission = this._activated.snapshot.data.leavePermission;
        this.leaveType = this._activated.snapshot.data.leaveType;
        this.leaveType.sort((a, b) => a.code.localeCompare(b.code));
        this.employee = JSON.parse(localStorage.getItem('user'))
        this.form = this.fb.group({
            status: [],
            leaveTypeId: [],
            date: [],
            dateStart: [],
            dateEnd: [],
        })
    }


    ngOnInit(): void {
        // this.GetLeaveRemainning()
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.status': this.form.value.status,
                    'filter.leaveType.id': this.form.value.leaveTypeId,
                    'filter.date': this.form.value.date,
                    'filter.dateStart': this.start,
                    'filter.dateEnd': this.end
                    // 'filter.employee': this.employee?.id
                },
                    // dataTablesParameters.employeeId = this.employee.id
                    this._service.getAllLeave(dataTablesParameters).subscribe({
                        next: (resp: any) => {
                            this.itemStatus = resp.data;
                            callback({
                                recordsTotal: resp.meta.totalItems,
                                recordsFiltered: resp.meta.totalItems,
                                data: resp.data
                            });
                        }, error: () => {
                            this.toastr.error('Invalid')
                        }
                    })
            },
            columns: [
                {
                    title: 'Leave No.',
                    data: 'code',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.leaveNo,
                    },
                    className: 'h-8 w-30 text-center'
                },
                {
                    title: 'First Name',
                    data: 'employee.firstname',
                    defaultContent: '-',

                    className: 'w-40 text-left'
                },
                {
                    title: 'Last Name',
                    data: 'employee.lastname',
                    defaultContent: '-',

                    className: 'w-40 text-left'
                },
                {
                    title: 'Leave Type',
                    data: 'leaveType.name',
                    defaultContent: '-',
                    className: 'w-30 text-left'
                },
                {
                    title: 'Request Date',
                    data: 'date',
                    defaultContent: '-',
                      ngTemplateRef: {
                        ref: this.date,
                    },
                    className: 'text-left'
                },
                {
                    title: 'Time',
                    data: function (row: any) {
                        if (!row.type) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }

                        switch (row.type) {
                            case 'half_day_morning':
                                return 'Half day morning';
                            case 'half_day_afternoon':
                                return 'Half day afternoon';
                            case 'full_day':
                                return 'Full day';
                            case 'consecutive_full_day_and_morning':
                                return 'Consecutive Full Day and Half Day Morning';
                            case 'half_afternoon_consecutive':
                                return 'Half Day Afternoon and Consecutive Full Day';
                            case 'consecutive_full_day_and_both_half':
                                return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Start Date',
                    data: 'dateStart',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.dateStart,
                    },
                    className: 'text-left'
                },

                {
                    title: 'End Date',
                    data: function (row: any) {
                        return DateTimeToSQL(row.dateEnd, 'dd/MM/yyyy')
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Taken',
                    data: 'qtyDay',
                    defaultContent: '-',
                    className: 'text-left'
                },
                // {
                //     title: 'Remain',
                //     data: 'remain',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
                {
                    title: 'Status',
                    data: function (row: any) {
                        if (!row.status) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.status) {
                            case 'open':
                                return 'On process';
                            case 'reject':
                                return 'Rejected';
                            case 'approved':
                                return 'Approved';
                            case 'cancel':
                                return 'Canceled';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left'
                }
                // {
                //     title: 'Is Lap ',
                //     data: 'isLap',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
            ]
        }
    }

    get ids(): FormArray {
        return this.form.get('ids') as FormArray;
    }

    onCheckboxChange(event: any, id: number) {
        if (event.target.checked) {
            this.ids.push(this.fb.control(id));
        } else {
            const index = this.ids.controls.findIndex(x => x.value === id);
            if (index !== -1) {
                this.ids.removeAt(index);
            }
        }
    }

    isAllSelected = false;

    toggleSelectAll(event: any) {
        this.isAllSelected = event.target.checked;
        this.itemStatus.forEach((row: any) => {
            row.selected = this.isAllSelected;
        });
    }


    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    GetLeaveRemainning() {
        this._service.getAll().subscribe((resp: any) => {
            this.itemData = resp

        })
    }

    ViewLeave(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: '700px',
            data: {
                itemid: itemId,
                url: this._router.url,
                period: this.datePeriod,
                type: 'single'
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            this.rerender();
            // this._changeDetectorRef.markForCheck();
        });
    }


    CalculateLeave(qty: any, used: any) {
        const remain = qty - used;
        return remain
    }

    clearData() {
        this.form.reset();
        this.rerender();
    }

    dateChange() {

        let formValue = this.form.value
        if (formValue.date) {
            formValue.date = DateTime.fromISO(this.form.value.date).toFormat('yyyy-MM-dd');
            this.form.patchValue({
                date: formValue.date
            })
        }
        if (formValue.dateStart && formValue.dateEnd) {
            formValue.dateStart = DateTime.fromISO(this.form.value.dateStart).toFormat('yyyy-MM-dd');
            formValue.dateEnd = DateTime.fromISO(this.form.value.dateEnd).toFormat('yyyy-MM-dd');
            this.start = `$gte:${formValue.dateStart}`,
                this.end = `$lte:${formValue.dateEnd}`,
                this.form.patchValue({
                    dateStart: formValue.dateStart,
                    dateEnd: formValue.dateEnd
                })
        }
    }

    gotoleaverequest(){
      this._router.navigate(['/admin-leave/admin-leave-application-form'])
    }
}
