import { Routes } from '@angular/router';
import { AdminLeaveComponent } from './leave.component';
import { inject } from '@angular/core';
import { EmployeeService } from 'app/modules/hr/employee/page.service';
import { LeaveApplicationFormComponent } from '../leave-application-form/form.component';
import { LeaveService } from 'app/modules/leave/leave.service';


export default [
    {
        path     : 'leave',
        component: AdminLeaveComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),

        },
      },
      {
                path     : 'admin-leave-application-form',
                component: LeaveApplicationFormComponent,
                resolve: {
                    leaveType: () => inject(EmployeeService).getLeaveType(),
                    employee: () => inject(EmployeeService).getEmployee(),
                    approval: () => inject(EmployeeService).getApproval(),

                }
        },


] as Routes;
