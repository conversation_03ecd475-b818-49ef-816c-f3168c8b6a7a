import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-carousel',
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.scss'],
  standalone: true,
  imports:[
    CommonModule,
    MatIconModule
  ]
  
})
export class CarouselComponent implements OnInit, OnDestroy {
  @Input() images: { url: string; alt: string }[] = [];

  currentIndex = 0;
  private autoSlideInterval: any;

  ngOnInit() {
    // เริ่มการเลื่อนสไลด์อัตโนมัติเมื่อ component ถูกสร้าง
    this.startAutoSlide();
  }

  ngOnDestroy() {
    // หยุดการเลื่อนอัตโนมัติเมื่อ component ถูกทำลาย
    this.stopAutoSlide();
  }

  next() {
    this.currentIndex = (this.currentIndex + 1) % this.images.length;
  }

  prev() {
    this.currentIndex = 
      (this.currentIndex - 1 + this.images.length) % this.images.length;
  }

  goToSlide(index: number) {
    this.currentIndex = index;
  }

  startAutoSlide() {
    // ตั้งค่าให้เลื่อนไปยังสไลด์ถัดไปทุกๆ 3 วินาที
    this.autoSlideInterval = setInterval(() => {
      this.next();
    }, 3000); // 3000 มิลลิวินาที = 3 วินาที
  }

  stopAutoSlide() {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }
  }
}