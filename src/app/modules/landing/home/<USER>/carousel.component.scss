/* carousel.component.css */

.fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.75); /* พื้นหลังโปร่งแสง */
  }
  
  img {
    width: 50%; /* กำหนดให้รูปแสดงขนาด 50% ของความกว้างหน้าจอ */
    height: auto; /* ป้องกันการบิดเบี้ยวของภาพ */
    transition: transform 0.2s;
    object-fit: contain; /* ให้ภาพคงอัตราส่วนและถูกจัดให้อยู่ภายใน container */
  }
  
  // img:hover {
  //   transform: scale(1.05); /* ขยายเล็กน้อยเมื่อ hover */
  // }
  
  .cursor-pointer {
    cursor: pointer;
  }
  