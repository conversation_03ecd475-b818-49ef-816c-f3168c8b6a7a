<div class="relative overflow-hidden">
  <!-- Carousel Images -->
  <div class="flex transition-transform duration-500" [style.transform]="'translateX(-' + currentIndex * 100 + '%)'">
    <div *ngFor="let image of images" class="min-w-full">
      <img [src]="image" [alt]="image" class="w-full h-auto object-cover cursor-pointer rounded-md"
        (click)="zoomImage(image)">
    </div>
  </div>

  <!-- Navigation Arrows -->
  <button (click)="prev()"
    class="absolute left-4 top-1/2 transform -translate-y-1/2  text-white p-2 rounded-full">
    <mat-icon svgIcon="feather:chevron-left" class="text-gray-600 icon-size-10 bg-gray-100 bg-opacity-50 rounded-full"></mat-icon>
  </button>
  <button (click)="next()"
    class="absolute right-4 top-1/2 transform -translate-y-1/2  text-white p-2 rounded-full">
   <mat-icon svgIcon="feather:chevron-right" class="text-gray-600 icon-size-10 bg-gray-100 bg-opacity-50 rounded-full"></mat-icon>
  </button>


  <!-- Indicators -->
  <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 pb-12">
    <span *ngFor="let image of images; let i = index" (click)="goToSlide(i)" [class.bg-white]="i === currentIndex"
      class="w-12 h-1 bg-gray-400 cursor-pointer "></span>
  </div>

  <!-- Zoom Modal -->
  <!-- <div *ngIf="zoomedImage" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center"
    (click)="closeZoom()">
    <img [src]="zoomedImage.url" [alt]="zoomedImage.alt" class="max-w-full max-h-full">
  </div> -->
</div>
