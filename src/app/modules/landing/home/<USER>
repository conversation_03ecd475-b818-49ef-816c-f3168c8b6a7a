<div class="flex h-screen w-full flex-col items-start justify-between">
  <div class="flex justify-end mr-8 md:mr-16 lg:mr-56 mb-12 mt-14">
    <img class="w-1/3 sm:w-1/4 md:w-1/6" src="images/logo/mein<PERSON>-logo.png" /> <span
      class="justify-items-center ml-2 font-bold text-lg text-[#1e293b]">{{version}}</span>
  </div>

  <div class="flex flex-wrap justify-start space-x-6 md:space-x-10 pl-4 md:pl-16 lg:pl-80">

    <div class="relative">
      <a href="#" class="text-xl hover:underline">
        Careers</a>
    </div>

    <div class="relative">
      <a class="text-xl hover:underline" [routerLink]="['/leave/leave-application-form']">
        Human Resources
      </a>
      <!-- <div *ngIf="dropdowns.hr" class="dropdown-content bg-white text-gray-800 shadow-lg rounded-md mt-2">
          <a [routerLink]="['/leave/leave-application-form']" class="block px-4 py-2 hover:bg-gray-200">Leave Request</a>
          <a [routerLink]="['/sign-in']" class="block px-4 py-2 hover:bg-gray-200">OT Request</a>
          <a [routerLink]="['']" class="block px-4 py-2 hover:bg-gray-200">Time Attendance</a>
          <a [routerLink]="['/sign-in/admin']" class="block px-4 py-2 hover:bg-gray-200">HR Management</a>
          <a href="http://192.168.10.40/sdr/" class="block px-4 py-2 hover:bg-gray-200">Staff Directory (In office)</a>
        </div> -->
    </div>

    <div class="relative">
      <a class="text-xl hover:underline" (click)="toggleDropdown('design')">
        Design Review
      </a>
      <div *ngIf="dropdowns.design" class="dropdown-content bg-white text-gray-800 shadow-lg rounded-md mt-2">
        <a href="http://192.168.10.40/designreview_new/index.aspx"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">C&S (In office)</a>
        <a href="http://192.168.10.40/designreview/index.aspx"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">M&E (In office)</a>
        <a href="https://apps.meinhardt.net/designreview_mft/"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">Façade (In office)</a>
        <a href="https://apps.meinhardt.net/designreview_lmt/"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">Lighting (In office)</a>
        <a href="https://apps.meinhardt.net/designreview_tmt/"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">Transport (In office)</a>
      </div>
    </div>

    <div class="relative">
      <a class="text-xl hover:underline" (click)="toggleDropdown('misc')">
        Others
      </a>
      <div *ngIf="dropdowns.misc" class="dropdown-content bg-white text-gray-800 shadow-lg rounded-md mt-2">
        <a href="http://app.meinhardt.com.sg/mtpl/login.aspx"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">Time Sheet Online (SG)</a>
        <a href="https://e-learning.meinhardt.net/"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">E-Learning</a>
        <a href="http://192.168.20.31/mrbs" class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">Meeting Room
          Booking (In office)</a>
        <a href="http://192.168.10.40/spec_index/specification.aspx"
          class="block px-4 py-2 hover:bg-gray-200 whitespace-nowrap">Specification Index (In office)</a>
        <a href="http://192.168.10.40/sdr/" class="block px-4 py-2 hover:bg-gray-200">Staff Directory (In office)</a>
      </div>
    </div>
  </div>


  <div class="w-full flex-grow h-full">
    <div class="prose prose-sm mx-auto max-w-none p-4 h-full">
      <app-carousel [images]="images" class="w-full max-w-full max-h-full"></app-carousel>
    </div>
  </div>



  <footer class="w-full bg-gray-800 text-white py-10 mt-10">
    <div class="container mx-auto px-4 flex flex-col md:flex-row justify-center items-center">
      <div class="flex items-center space-x-4">
        <div class="mt-4 md:mt-0">
          <span class="text-sm">&copy; 2024 Meinhardt Thailand.</span>
        </div>
      </div>
    </div>
  </footer>
</div>