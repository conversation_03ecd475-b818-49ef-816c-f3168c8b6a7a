import { Component, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { CarouselComponent } from './image-slide/carousel.component';
import { CommonModule } from '@angular/common';
import { environment } from 'environments/environment';

@Component({
    selector: 'landing-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: true,
    imports: [MatButtonModule, RouterLink, MatIconModule, CarouselComponent, CommonModule],
})
export class LandingHomeComponent {
    dropdowns = {
        careers: false,
        hr: false,
        design: false,
        misc: false
      };

      toggleDropdown(menu: string) {
        for (let key in this.dropdowns) {
            if (key !== menu) {
              this.dropdowns[key] = false;
            }
          }
        this.dropdowns[menu] = !this.dropdowns[menu];
      }
    images: any[] = [
        'images/banner/2.jpg',
        'images/banner/4.jpg',
        'images/banner/5.jpg',
    ]
   version: any = environment.version
    /**
     * Constructor
     */
    constructor() {
      console.log(import.meta.env);
      console.log(this.version);
      
      localStorage.clear()
    }
}
