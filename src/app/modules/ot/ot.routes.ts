import { Routes } from '@angular/router';

import { EmployeeService } from '../hr/employee/page.service';
import { inject } from '@angular/core';
import { ReportService } from '../report/page.service';
import { OtComponent } from './ot.component';
import { OtApplicationFormComponent } from './ot-application-form/form.component';
import { OtService } from './ot.service';
import { OtApprovalComponent } from './ot-approval/ot-approval.component';
import { OtListComponent } from './ot-list/ot-list.component';
import { ProjectService } from '../hr/project/page.service';
import { OtReviewComponent } from './ot-review/ot-review.component';


export default [
    {
        path: '',
        component: OtComponent,
    },
    {
        path: 'ot-list',
        component: OtListComponent,
    },
    {
        path: 'ot-application-form',
        component: OtApplicationFormComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            ProjectData: () => inject(ProjectService).getProject(),
        }
    },
    {
        path: 'ot-application-edit/:id',
        component: OtApplicationFormComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            ProjectData: () => inject(ProjectService).getProject(),
        },
        data: {
            action: 'UPDATE'
        }
    },
    {
        path: 'ot-approval',
        component: OtApprovalComponent,
    },
    {
        path: 'ot-review',
        component: OtReviewComponent,
    },

] as Routes;
