import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { OtService } from '../ot.service';
import { CommonModule } from '@angular/common';
import { DateTimeToSQL } from 'app/helper';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { DialogRejectComponent } from '../dialog-reject/dialog-reject.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dialog-view-ot',
  standalone: true,
  imports: [
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './dialog-view.component.html',
  styleUrls: ['./dialog-view.component.scss']
})
export class DialogViewComponent implements OnInit {
  form: FormGroup;
  flashErrorMessage: string;
  flashMessage: 'success' | 'error' | null = null;
  isLoading: boolean = false;
  searchInputControl: FormControl = new FormControl();
  selectedProduct: any | null = null;
  filterForm: FormGroup;
  tagsEditMode: boolean = false;
  totalPrice: number;
  itemData: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  head: any
  role: any
  constructor(
    public dialogRef: MatDialogRef<DialogViewComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _changeDetectorRef: ChangeDetectorRef,
    private _service: OtService,
    private toastr: ToastrService,
    private fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder,
    private _matDialog: MatDialog,
    private router: Router,
  ) {
    this.head = JSON.parse(localStorage.getItem('user'))
    this.role = localStorage.getItem('role')
    this.itemData = this._service.getOTById(this.data.itemid.id).subscribe((resp: any) => {
      this.itemData = resp
    })
    this.form = this._fb.group({
      headId: '',
      adminId: '',
      statusRemark: [
        ''
      ],
      status: null
    })

  }

  ngOnInit(): void {
    if(this.data?.url === '/admin-ot/ot'){
      this.form.patchValue({
        adminId: this.head.id
      })
    } else {
      this.form.patchValue({
        headId: this.head.id
      })
    }
  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

  onClose() {
    this.dialogRef.close();
  }

  showFlashMessage(type: 'success' | 'error'): void {
    // Show the message
    this.flashMessage = type;

    // Mark for check
    this._changeDetectorRef.markForCheck();

    // Hide it after 3 seconds
    setTimeout(() => {
      this.flashMessage = null;
      // Mark for check
      this._changeDetectorRef.markForCheck();
    }, 3000);
  }

  formatDate(data: any) {
    const dateFormat = DateTimeToSQL(data, 'dd/MM/yyyy')
    return dateFormat
  }

  Submit(status: string) {
    this.form.patchValue({
      status: status,
    })
    if (status === 'reject' || status === 'cancel') {
      let formValue = this.form.value
      const dialogRef = this._matDialog.open(DialogRejectComponent, {
        width: '500px',
        height: 'auto',
        data: {
          form: formValue,
          leaveId: this.data?.itemid?.id,
          url: this.data?.url
        }
      });
      dialogRef.afterClosed().subscribe(item => {
        if (item) {
          this.toastr.success('Successed')
          this.dialogRef.close(true)
        }
      });

    } else {
      let formValue = this.form.value
      const confirmation = this.fuseConfirmationService.open({
        title: "Do you want to save data ?",
        icon: {
          show: true,
          name: "heroicons_outline:exclamation-triangle",
          color: "primary"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: false
      })

      confirmation.afterClosed().subscribe(
        result => {
          if (result == 'confirmed') {
            this._service.updateStatus(this.data.itemid.id, formValue).subscribe({
              error: (err) => {
                this.toastr.error(err.error.message)
              },
              complete: () => {
                this.toastr.success('Successed')
                this.dialogRef.close(true)
              },
            });
          }
        }
      )
    }
  }

  reasonRequiredIfRejected(statusControlName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const formGroup = control.parent;
      if (!formGroup) return null; // ต้องแน่ใจว่าเป็นฟอร์มกลุ่ม
      const statusControl = formGroup.get(statusControlName);
      if (statusControl?.value === 'reject' && !control.value) {
        return { reasonRequired: true };
      }
      return null;
    };
  }

  changeType(data: any) {
    switch (data) {
      case 'half_day_morning':
        return 'Half day morning';
      case 'half_day_afternoon':
        return 'Half day afternoon';
      case 'full_day':
        return 'Full day';
      case 'consecutive_full_day_and_morning':
        return 'Consecutive Full Day and Half Day Morning';
      case 'half_afternoon_consecutive':
        return 'Half Day Afternoon and Consecutive Full Day';
      case 'consecutive_full_day_and_both_half':
        return 'Half Day Afternoon And Consecutive Full Day And Half Day Morning';
      default:
        return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
    }
  }

  Edit() {
    this.router.navigate([`/ot/ot-application-edit/${this.data.itemid.id}`])
    this.dialogRef.close(true)
  }
  EditAdmin() {
    this.router.navigate([`/admin-ot/ot-application-edit/${this.data.itemid.id}`])
    this.dialogRef.close(true)
  }
}
