import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { OtService } from '../ot.service';
import { CommonModule } from '@angular/common';
import { DateTimeToSQL } from 'app/helper';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-dialog-view-ot-rejected',
  standalone: true,
  imports: [
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './dialog-reject.component.html',
  styleUrls: ['./dialog-reject.component.scss']
})
export class DialogRejectComponent implements OnInit {
  form: FormGroup;
  flashErrorMessage: string;
  flashMessage: 'success' | 'error' | null = null;
  isLoading: boolean = false;
  searchInputControl: FormControl = new FormControl();
  selectedProduct: any | null = null;
  filterForm: FormGroup;
  tagsEditMode: boolean = false;
  totalPrice: number;
  itemData: any;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  head: any
  constructor(
    public dialogRef: MatDialogRef<DialogRejectComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _changeDetectorRef: ChangeDetectorRef,
    private _service: OtService,
    private toastr: ToastrService,
    private fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder
  ) {
    this.head = JSON.parse(localStorage.getItem('user')); 
    this.form = this._fb.group({
      headId: this.head.id,
      statusRemark: null,
      hrRemark: null,
      status: null
    })

  }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy(): void {

  }

  onClose() {
    this.dialogRef.close();
  }

  showFlashMessage(type: 'success' | 'error'): void {
    // Show the message
    this.flashMessage = type;

    // Mark for check
    this._changeDetectorRef.markForCheck();

    // Hide it after 3 seconds
    setTimeout(() => {
      this.flashMessage = null;
      // Mark for check
      this._changeDetectorRef.markForCheck();
    }, 3000);
  }

  formatDate(data: any) {
    const dateFormat = DateTimeToSQL(data, 'dd/MM/yyyy')
    return dateFormat
  }

  Submit(status: string) {


    if (this.data?.url === '/ot/ot-list' && this.form.value.statusRemark === null) {
      this.toastr.error('Reason is required')
      return;
    } else if (this.data?.url === '/admin-leave/ot' && this.form.value.hrRemark === null) {
      this.toastr.error('Reason is required')
      return;
    } else if (this.data?.url === '/ot/ot-review' && this.form.value.statusRemark === null) {
      this.toastr.error('Reason is required')
      return;
    }

    if (this.data.type === 'multi') {
      this.dialogRef.close(this.form.value.statusRemark)
    } else {
      this.form.patchValue({
        status: status
      })
      console.log(this.form.value);
      let formValue = this.form.value
      const confirmation = this.fuseConfirmationService.open({
        title: "Do you want to save data ?",
        icon: {
          show: true,
          name: "heroicons_outline:exclamation-triangle",
          color: "primary"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: false
      })
      confirmation.afterClosed().subscribe(
        result => {
          if (result == 'confirmed') {
            this._service.updateStatus(this.data.leaveId, formValue).subscribe({
              error: (err) => {
                this.toastr.error(err.error.message)
              },
              complete: () => {
                this.dialogRef.close(true)
              },
            });
          }
        }
      )
    }
  }
}
