.animate-border {
    border: 2px solid rgb(255, 0, 0); /* เริ่มที่สีแดง */
    animation: borderAnimation 3s infinite;
}

@keyframes borderAnimation {
    0% {
        border-color: rgb(255, 0, 0); /* สีแดง */
    }
    16% {
        border-color: rgb(255, 127, 0); /* สีส้ม */
    }
    33% {
        border-color: rgb(255, 255, 0); /* สีเหลือง */
    }
    50% {
        border-color: rgb(0, 255, 0); /* สีเขียว */
    }
    66% {
        border-color: rgb(0, 0, 255); /* สีน้ำเงิน */
    }
    83% {
        border-color: rgb(75, 0, 130); /* สีคราม */
    }
    100% {
        border-color: rgb(148, 0, 211); /* สีม่วง */
    }
}
