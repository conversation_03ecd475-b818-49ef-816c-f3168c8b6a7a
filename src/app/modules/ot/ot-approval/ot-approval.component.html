<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">

        <div class="flex flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl mx-2">
                    Pending Request
                </h2>
            </div>
        </div>
        <div class="flex flex-row mt-2 mb-2 justify-start gap-2">
            <div
                class="flex flex-row items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate gap-2">
                <button class="" mat-flat-button [color]="'accent'" (click)="toggleSelectAll(!isAllSelected)">
                    {{ isAllSelected ? 'Unselect All' : 'Select All' }}
                </button>
                <button class="" mat-flat-button [color]="'warn'" (click)="Submit('reject')"
                    [disabled]="this.multiSelect.length === 0"
                    [matTooltip]="multiSelect.length === 0 ? 'Please select items before rejecting' : ''"
                    matTooltipPosition="above">
                    Reject
                </button>

                <button class="border-[1px] border-gray-200" mat-flat-button [color]="'primary'"
                    (click)="Submit('approved')" [disabled]="this.multiSelect.length === 0"
                    [matTooltip]="multiSelect.length === 0 ? 'Please select items before approving' : ''"
                    matTooltipPosition="above">
                    Approve
                </button>
            </div>
        </div>
        <div class="overflow-auto">
            <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full"></table>
        </div>
    </div>

</div>

<ng-template #btNg let-data="adtData">
    <mat-checkbox [checked]="data.selected" (change)="onCheckboxChange($event, data.id)"></mat-checkbox>
</ng-template>

<ng-template #leaveNo let-data="adtData">
    <p (click)="ViewLeave(data)" class="underline cursor-pointer text-blue-500">{{data.code}}</p>
</ng-template>

<ng-template #selectAll let-data="adtData">
    <button class="px-6 ml-3" mat-flat-button [color]="'warn'"> Reject</button>
</ng-template>

<ng-template #fullName let-data="adtData">
    <p class=" ">{{data.employee.firstname}} {{data.employee.lastname}}</p>
</ng-template>