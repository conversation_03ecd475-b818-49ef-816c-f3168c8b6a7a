import { CommonModule, DatePipe } from '@angular/common';
import {
    ChangeDetectorRef,
    Component,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormArray,
    FormBuilder,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { DateTime } from 'luxon';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { DialogViewComponent } from '../dialog-view/dialog-view.component';
import { OtService } from '../ot.service';

@Component({
    selector: 'ot-list',
    standalone: true,
    templateUrl: './ot-list.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        MatSelectModule,
        MatFormFieldModule,
        MatInputModule,
        MatCheckboxModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
    ],
})
export class OtListComponent implements OnInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild('fullName') fullName: any;
    @ViewChild('date') date: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    itemData: any;
    itemStatus: any;
    form: FormGroup;
    leavePermission: any;
    employee: any;
    datePeriod: string = '';
    start: string = '';
    end: string = '';
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    status: any[] = [
        {
            value: 'open',
            name: 'On process',
        },
        {
            value: 'approved',
            name: 'Approved',
        },
        {
            value: 'head_approved',
            name: 'Reviewed',
        },
        {
            value: 'cancel',
            name: 'Cancel',
        },
        {
            value: 'reject',
            name: 'Reject',
        },
    ];
    /**
     * Constructor
     */
    constructor(
        private _service: OtService,
        private toastr: ToastrService,
        private fb: FormBuilder,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activated: ActivatedRoute,
        private _changeDetectorRef: ChangeDetectorRef
    ) {
        this.employee = JSON.parse(localStorage.getItem('user'));
        this.form = this.fb.group({
            date: '',
            dateStart: [],
            dateEnd: [],
            status: [[]],
        });
    }

    ngOnInit(): void {
        setTimeout(() => this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        const datePipe = new DatePipe('en-US');
        // const date = datePipe.transform(this.form.value.date, 'yyyy-MM-dd');
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true, // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                (dataTablesParameters.filter = {
                    ...(this.form.value.status.length > 0 && { 'filter.status': '$in:' + this.form.value.status.join(',') }),
                    'filter.date': this.form.value.date,
                    'filter.employee.id': this.employee?.id,
                }),
                    (dataTablesParameters.employeeId = this.employee.id);
                this._service.getOtPage(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        this.itemStatus = resp.data;
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data,
                        });
                    },
                    error: () => {
                        this.toastr.error('Invalid');
                    },
                });
            },
            columns: [
                // {
                //     title: '<input type="checkbox" (click)="toggleSelectAll($event)" />',
                //     data: null,
                //     defaultContent: '',
                //     ngTemplateRef: {
                //         ref: this.btNg,
                //     },
                //     className: 'w-15 text-center h-10',
                //     orderable: false
                // },
                {
                    title: 'OT No.',
                    data: 'code',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.leaveNo,
                    },
                    className: 'h-8 w-30 text-center',
                },
                {
                    title: 'First Name',
                    data: 'employee.firstname',
                    defaultContent: '-',
                    className: 'w-40 text-left',
                },
                {
                    title: 'Last Name',
                    data: 'employee.lastname',
                    defaultContent: '-',
                    className: 'w-40 text-left',
                },
                {
                    title: 'Code Project',
                    data: 'project.code',
                    defaultContent: '-',
                    className: 'w-30 text-left',
                },
                {
                    title: 'Project',
                    data: 'project.name',
                    defaultContent: '-',
                    className: 'w-30 text-left',
                },
                {
                    title: 'Request Date',
                    data: 'date',
                    defaultContent: '-',
                    ngTemplateRef: {
                        ref: this.date,
                    },
                    className: 'text-left',
                },

                {
                    title: 'Start Time',
                    data: function (row: any) {
                        return row.timeStart;
                    },
                    defaultContent: '-',
                    className: 'text-left',
                },

                {
                    title: 'End Time',
                    data: function (row: any) {
                        return row.timeEnd;
                    },
                    defaultContent: '-',
                    className: 'text-left',
                },
                {
                    title: 'Hours',
                    data: 'qtyHour',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Status',
                    data: function (row: any) {
                        if (!row.status) {
                            return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
                        }
                        switch (row.status) {
                            case 'open':
                                return 'On process';
                            case 'reject':
                                return 'Rejected';
                            case 'cancel':
                                return 'Canceled';
                            case 'head_approved':
                                return 'Reviewed';
                            case 'approved':
                                return 'Approved';
                            default:
                                return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
                        }
                    },
                    defaultContent: '-',
                    className: 'text-left',
                },
                // {
                //     title: 'Is Lap ',
                //     data: 'isLap',
                //     defaultContent: '-',
                //     className: 'text-left'
                // },
            ],
        };
    }

    get ids(): FormArray {
        return this.form.get('ids') as FormArray;
    }

    onCheckboxChange(event: any, id: number) {
        if (event.target.checked) {
            this.ids.push(this.fb.control(id));
        } else {
            const index = this.ids.controls.findIndex((x) => x.value === id);
            if (index !== -1) {
                this.ids.removeAt(index);
            }
        }
    }

    isAllSelected = false;

    toggleSelectAll(event: any) {
        this.isAllSelected = event.target.checked;
        this.itemStatus.forEach((row: any) => {
            row.selected = this.isAllSelected;
        });
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance) => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    GetLeaveRemainning() {
        this._service.getAll().subscribe((resp: any) => {
            this.itemData = resp;
        });
    }

    ViewLeave(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: '700px',
            data: {
                itemid: itemId,
                url: this._router.url,
                period: this.datePeriod,
            },
        });
        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            // this.GetLeavePermission();
            // this.GetLeaveRemainning();
            this._changeDetectorRef.markForCheck();
        });
    }

    CalculateLeave(qty: any, used: any) {
        const remain = qty - used;
        return remain;
    }

    GetLeavePermission() {
        this._service.getLaevePermission().subscribe((resp: any) => {
            this.leavePermission = resp;
        });
    }
    clearData() {
        this.form.reset();
        this.form.patchValue({
            status: []
        })
        this.rerender();
    }
    dateChange() {
        let formValue = this.form.value;
        if (formValue.date) {
            formValue.date = DateTime.fromISO(this.form.value.date).toFormat(
                'yyyy-MM-dd'
            );
            this.form.patchValue({
                date: formValue.date,
            });
        }
        if (formValue.dateStart && formValue.dateEnd) {
            formValue.dateStart = DateTime.fromISO(
                this.form.value.dateStart
            ).toFormat('yyyy-MM-dd');
            formValue.dateEnd = DateTime.fromISO(
                this.form.value.dateEnd
            ).toFormat('yyyy-MM-dd');
            (this.start = `$gte:${formValue.dateStart}`),
                (this.end = `$lte:${formValue.dateEnd}`),
                this.form.patchValue({
                    dateStart: formValue.dateStart,
                    dateEnd: formValue.dateEnd,
                });
        }
    }
}
