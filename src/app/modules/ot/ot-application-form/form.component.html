<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto bg-card m-4 p-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
      <div>
        <h2 class=" mb-2 truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Overtime Request
        </h2>

      </div>
      <div class="flex flex-row justify-end items-center">
        <p class="text-sm text-gray-500 mx-2 items-end">Please submit your overtime (OT) requests for the month of
          {{getMonth()}} no later
          than the 10th of this month to ensure timely processing.</p>
      </div>
    </div>
    <div class="flex flex-row justify-center pb-2 my-5 w-full">
      <form class="overflow-hidden px-0 md:px-8  mb-4 flex flex-col w-full" [formGroup]="form">
        <div class="flex flex-col md:flex-row gap-4 justify-center w-full">
          <div class="flex flex-col w-full md:w-full">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field class="w-full">
                  <mat-label>Overtime Date</mat-label>
                  <input matInput [matDatepicker]="picker" placeholder="Select Date" formControlName="date"
                    (dateChange)="changeDateFormat($event)">
                  <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <div class="flex items-center gap-2">
                  <mat-form-field class="w-full">
                    <mat-label>Time From</mat-label>
                    <mat-select placeholder="Please select Start Time" [formControl]="form.controls['timeStart']"
                      (selectionChange)="onStartTimeChange()">
                      <!-- <mat-option *ngFor="let time of timeOptions" [value]="time" [disabled]="isTimeDisabled(time)"> -->
                      <mat-option *ngFor="let time of timeOptions" [value]="time">
                        {{ time }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <!-- End Time -->
                  <mat-form-field class="w-full">
                    <mat-label>To</mat-label>
                    <mat-select placeholder="Please select End Time" [formControl]="form.controls['timeEnd']"
                      (selectionChange)="calculateDuration()">
                      <!-- <mat-option *ngFor="let time of timeOptions" [value]="time"  [disabled]="isTimeDisabled(time)" > -->
                      <mat-option *ngFor="let time of timeOptions" [value]="time">
                        {{ time }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Total Hours</mat-label>
                  <input matInput [value]="duration" placeholder="" readonly />
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-0 mt-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                  <mat-label>Review By</mat-label>
                  <input matInput [formControl]="approverFilter" [matAutocomplete]="approverAutoComplete"
                    placeholder="Search Approver" />
                  <mat-autocomplete #approverAutoComplete="matAutocomplete"
                    (optionSelected)="onSelectApprover($event.option.value, 'manual')">
                    <mat-option *ngFor="let item of filterApprover | async" [value]="item">
                      {{item.firstname}} {{item.lastname}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-full">

            <div class="-mx-3 md:flex mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Project</mat-label>
                  <input matInput [formControl]="projectFilter" [matAutocomplete]="projectAutoComplete"
                    placeholder="Search Project" />
                  <mat-autocomplete #projectAutoComplete="matAutocomplete"
                    (optionSelected)="onSelectProject($event.option.value, 'manual')">
                    <mat-option *ngFor="let item of filterProject | async" [value]="item">
                      ({{item.code}}) {{item.name}}
                    </mat-option>
                  </mat-autocomplete>
                </mat-form-field>
              </div>
            </div>
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Detail of work</mat-label>
                  <textarea matInput [rows]="5" formControlName="detail"></textarea>
                  <mat-hint>(Use 200 character maximum.)</mat-hint>
                </mat-form-field>
              </div>
            </div>

            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4 mt-1">
              <mat-label>Default Approver</mat-label>
              <input matInput [formControl]="approverProjectFilter" [matAutocomplete]="approverProjectAutoComplete"
                placeholder="Search Approver" />
              <mat-autocomplete #approverProjectAutoComplete="matAutocomplete"
                (optionSelected)="onSelectAutocomplete($event.option.value, 'approverId', approverProjectFilter)">
                <mat-option *ngFor="let item of filterApproverProject | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>


            <!-- <div class="-mx-3 md:flex mb-0 mt-2" *ngIf="errorMessage" class="error">
                          <div class="md:min-w-full px-3 mb-2 md:mb-0">
                              <p>{{ errorMessage }}</p>
                          </div>
                      </div> -->
          </div>
        </div>
        <div class="flex flex-row mt-2 justify-center">
          <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
              Process
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
              Cancel
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>