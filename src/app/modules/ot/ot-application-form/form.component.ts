import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { OtService } from '../ot.service';
import { CommonModule } from '@angular/common';
import { MatDatepickerInputEvent, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatInputModule } from '@angular/material/input';
import { difference } from 'lodash';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DateTime } from 'luxon';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { DateTimeToISO, DateTimeToJSDate } from 'app/helper';
import { MatDialog } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'ot-request-form',
  standalone: true,
  templateUrl: './form.component.html',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatRadioModule,
    MatInputModule,
    ImageUploadComponent,
    MatAutocompleteModule,
  ]
})
export class OtApplicationFormComponent implements OnInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  itemData: any;
  form: FormGroup;
  leaveType: any[] = [];
  employee: any;
  startDate: Date | null = null;
  endDate: Date | null = null;
  dayDifference: number | null = null;
  ///approver filter
  approverFilter = new FormControl('');
  projectFilter = new FormControl('');
  approverProjectFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  filterProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  filterApproverProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];
  leavePermission: any;
  ProjectData: any;
  errorMessage: string | null = null;
  timeOptions: string[] = [];
  selectedTime: string = '';
  duration: number | null = null;
  timeStart: string;
  timeEnd: string;
  id: any;

  action: 'CREATE' | 'UPDATE' = 'CREATE';
  /**
   * Constructor
   */
  constructor(
    private _service: OtService,
    private _fb: FormBuilder,
    private _fuseConfirmationService: FuseConfirmationService,
    private imageUploadService: ImageUploadService,
    private toastr: ToastrService,
    private _router: Router,
    private _activateRoute: ActivatedRoute,
    private _matDialog: MatDialog
  ) {
    this.leaveType = this._activateRoute.snapshot.data.leaveType
    this.ProjectData = this._activateRoute.snapshot.data.ProjectData;
    this.filterProject.next(this.ProjectData.slice());
    this.approval = this._activateRoute.snapshot.data.approval
    this.id = this._activateRoute.snapshot.params.id
    this.filterApprover.next(this.approval.slice());
    this.filterApproverProject.next(this.approval.slice());
    this.employee = JSON.parse(localStorage.getItem('user'))

    this.action = this._activateRoute.snapshot.data.action;

    this.generateTimeList();
  }

  ngOnInit(): void {
    this.form = this._fb.group({
      employeeId: this.employee?.id,
      date: DateTime.now().toFormat('yyyy-MM-dd'),
      timeStart: null,
      timeEnd: null,
      detail: null,
      projectId: null,
      headId: this.employee?.head?.toString(),
      approverId: null,
    });

    // this.getOTtime()

    const head = this.approval.find(item => item.id === this.employee?.head)
    if (head) {
      this.approverFilter.setValue(`${head.firstname} ${head.lastname}`);
    }
    if (this.id) {
      this._service.getOTById(this.id).subscribe((resp: any) => {
        const projectdata = this.ProjectData.find(item => item.id === resp.project?.id)
        if (projectdata) {
          this.projectFilter.setValue(`(${projectdata.code}) ${projectdata.name}`);
        }
        const headgetbyid = this.approval.find(item => item.id === resp.head?.id)
        if (headgetbyid) {
          this.approverFilter.setValue(`${headgetbyid.firstname} ${headgetbyid.lastname}`);
        }

        if (resp?.approver) {
          this.approverProjectFilter.setValue(`${resp?.approver?.firstname} ${resp?.approver?.lastname}`);
        }

        // const approver = this.approval.find(item => item.id === resp.head?.id)

        this.duration = resp.qtyHour
        const timeIn = resp.timeStart ? resp.timeStart.replace(/:00$/, '') : resp.timeIn;
        const timeOut = resp.timeEnd ? resp.timeEnd.replace(/:00$/, '') : resp.timeOut;
        this.timeStart = timeIn
        this.timeEnd = timeOut
        this.form.patchValue({
          ...resp,
          date: resp.date,
          projectId: +resp?.project?.id,
          headId: resp?.head?.id,
          timeStart: timeIn,
          timeEnd: timeOut,
          employeeId: resp?.employee?.id,
          approverId: resp?.approver?.id,
        }, { emitEvent: false });

        this.form.get('date').disable();
        this.form.get('timeStart').disable();
        this.form.get('timeEnd').disable();
      })
    }

    this.approverFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterBrand();
      });
    this.projectFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterProject();
      });

    this.approverProjectFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterapproverProject();
      });

  }

  private _filterapproverProject() {
    if (!this.approval) {
      return;
    }
    let search = this.approverProjectFilter.value;

    if (!search) {
      this.filterApproverProject.next(this.approval.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }


    this.filterApproverProject.next(
      this.approval.filter(item =>
        (item.code.toLowerCase() + ' ' + item.name.toLowerCase()).includes(search)
      )
    );
  }

  Submit() {
    let data = this.form.getRawValue();

    if (!data.timeStart || !data.timeEnd) {
      this.toastr.error('Please select time start and time end.')
      return;
    }

    if (this.duration < 1 || this.duration === null || this.duration === undefined) {
      this.toastr.error('Please select time start and time end.')
      return;
    }
    if (!this.id) {
      data.date = DateTime.fromISO(data.date).toFormat('yyyy-MM-dd')
      this._service.createOT(data).subscribe({
        error: (err) => {
          this.toastr.error(err.error.message)
        },
        complete: () => {
          this.toastr.success('Successed')
          this._router.navigate(['/ot/ot-list'])
        },
      });
    } else {
      this._service.updateOT(data, this.id).subscribe({
        error: (err) => {
          this.toastr.error(err.error.message)
        },
        complete: () => {
          this.toastr.success('Successed')
          this._router.navigate(['/ot/ot-list'])
        },
      });
    }
  }


  getOTtime() {
    this._service.getOTtime(this.form.value).subscribe((resp: any) => {
      const timeIn = resp.timeIn ? resp.timeIn.replace(/:00$/, '') : resp.timeIn;
      const timeOut = resp.timeOut ? resp.timeOut.replace(/:00$/, '') : resp.timeOut;
      this.timeEnd = timeOut
      this.timeStart = timeIn
      this.form.patchValue({
        timeStart: timeIn,
        timeEnd: timeOut,
      });
      this.isTimeDisabled(this.timeStart)
      this.calculateDuration();

    })
  }
  checkLeave(leaveType: string, startDate: Date) {
    const currentDate = new Date();
    const differenceInDays = Math.floor(
      (new Date(startDate).getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    if (leaveType === 'Annual' && differenceInDays >= 7) {
      return { status: true, text: 'Valid' };
    }

    if (leaveType === 'Casual' && differenceInDays >= 3) {
      return { status: true, text: 'Valid' };
    }

    if (leaveType === 'Annual' && differenceInDays < 7) {
      return { status: false, text: 'Annual ot must be requested at least 7 days in advance.' };
    }

    if (leaveType === 'Casual' && differenceInDays < 3) {
      return { status: false, text: 'Casual ot must be requested at least 3 days in advance.' };
    }
    // If no conditions match, return true (no restrictions)
    return { status: true, text: 'No specific conditions for ot.' };
  }

  backTo() {
    this._router.navigate(['/ot/ot-list'])
  }

  files: File[] = [];
  onSelect(event, input: any) {
    if (input === 'addfile') {

      this.form.patchValue({
        file: event[0],
        file_name: event[0].name,
      });
    }
  }

  uploadSuccess(event): void {
    this.imageUploadService.upload(event).subscribe({
      next: (resp: any) => {
        // console.log(resp);

        this.form.patchValue({
          file: resp.pathUrl
        });
        // console.log(this.form.value);

      },
      error: (err) => {
        this.toastr.error(JSON.stringify(err))
      },
    })
  }

  CalculateLeave(qty: any, used: any) {
    const remain = qty - used;
    return remain
  }

  onSelectionChange(event: any): void {

    const selectedId = event.value;
    const selectedItem = this.leavePermission.find(item => item.leaveType.id === selectedId);
    if (selectedItem && this.CalculateLeave(selectedItem.qtyDay, selectedItem.usedDay) === 0) {
      // แจ้ง Error ว่าคุณไม่สามารถเลือกตัวเลือกนี้ได้
      this.toastr.error('You cannot select this ot type because the remaining days are 0.')
      // รีเซ็ตค่าในฟอร์มให้เป็น null
      this.form.get('leaveTypeId')?.setValue(null);
    }
  }

  calculateDays() {
    const dateStart: Date = this.form.get('dateStart')?.value;
    const dateEnd: Date = this.form.get('dateEnd')?.value;
    const timeOption = this.form.get('type')?.value;

    if (!dateStart || !dateEnd || !timeOption) {
      this.errorMessage = null;
      return;
    }

    const daysDifference =
      (new Date(dateEnd).getTime() - new Date(dateStart).getTime()) /
      (1000 * 60 * 60 * 24) +
      1;

    switch (timeOption) {
      case 'half_day_morning':
      case 'half_day_afternoon':
        if (daysDifference !== 1) {
          this.errorMessage = 'Half Day options can only be selected for 1 day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'full_day':
        if (daysDifference < 1) {
          this.errorMessage = 'Full Day requires at least 1 day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'consecutive_full_day_and_morning':
      case 'half_afternoon_consecutive':
        if (daysDifference <= 1) {
          this.errorMessage =
            'This option requires more than 1 consecutive day.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      case 'consecutive_full_day_and_both_half':
        if (daysDifference < 3) {
          this.errorMessage =
            'This option requires at least 3 consecutive days.';
          this.toastr.error(this.errorMessage);
          this.form.patchValue({
            dateEnd: null,
            dateStart: null,
          })
          return;
        }
        break;

      default:
        this.errorMessage = 'Invalid option selected.';
        this.toastr.error(this.errorMessage)
        this.form.patchValue({
          dateEnd: null,
          dateStart: null,
        })
        return;
    }
    // หากไม่มีปัญหา
    this.errorMessage = null;
  }

                 /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

  protected _filterBrand() {
    if (!this.approval) {
      return;
    }
    let search = this.approverFilter.value;

    if (!search) {
      this.filterApprover.next(this.approval.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterApprover.next(
      this.approval.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }
  protected _filterProject() {
    if (!this.ProjectData) {
      return;
    }
    let search = this.projectFilter.value;
    console.log(search, 's');

    if (!search) {
      this.filterProject.next(this.ProjectData.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }


    this.filterProject.next(
      this.ProjectData.filter(item =>
        (item.code.toLowerCase() + ' ' + item.name.toLowerCase()).includes(search)
      )
    );
  }
  onSelectApprover(event: any, type: any) {
    if (!event) {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        headId: selectedData.id,
      });
      this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Found');
      return;
    }
  }

  onSelectAutocomplete(event: any, key: string, control: FormControl) {
    if (!event) {
      if (control.invalid) {
        control.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.get(key)?.setValue(selectedData.id);
      control.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (control.invalid) {
        control.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      return;
    }
  }

  onSelectProject(event: any, type: any) {
    if (!event) {
      if (this.projectFilter.invalid) {
        this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        projectId: selectedData.id,
        approverId: selectedData?.employee?.id,
      });
      this.projectFilter.setValue(`(${selectedData.code}) ${selectedData.name}`);

      if (selectedData?.employee?.id) {
        this.approverProjectFilter.setValue(`${selectedData?.employee?.firstname} ${selectedData.employee?.lastname}`);
      }
    } else {
      if (this.projectFilter.invalid) {
        this.projectFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      return;
    }
  }
  generateTimeList(): void {
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const formattedHour = String(hour).padStart(2, '0');
        const formattedMinute = String(minute).padStart(2, '0');
        this.timeOptions.push(`${formattedHour}:${formattedMinute}`);
      }
    }
  }

  onStartTimeChange(): void {
    const startTime = this.form.controls['timeStart'].value;
    const endTime = this.form.controls['timeEnd'].value;

    if (startTime && endTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      const startInMinutes = startHours * 60 + startMinutes;
      const endInMinutes = endHours * 60 + endMinutes;

      if (startInMinutes >= endInMinutes) {
        this.form.controls['timeEnd'].reset(); // Clear End Time if invalid
      }
    }

    // Recalculate duration after resetting if necessary
    this.calculateDuration();
  }

  calculateDuration(): void {
    const startTime = this.form.controls['timeStart'].value;
    const endTime = this.form.controls['timeEnd'].value;

    if (startTime && endTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      const startInMinutes = startHours * 60 + startMinutes;
      const endInMinutes = endHours * 60 + endMinutes;

      let durationInMinutes = endInMinutes - startInMinutes;

      // ถ้า endTime < startTime แสดงว่าข้ามวัน ให้บวก 24 ชั่วโมง (1440 นาที)
      if (durationInMinutes < 0) {
        durationInMinutes += 1440;
      }

      this.duration = durationInMinutes / 60; // แสดงผลเป็นชั่วโมง
    } else {
      this.duration = null;
    }
  }

  isTimeDisabled(time: string): boolean {
    const startTime = this.timeStart;

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentInMinutes = currentHours * 60 + currentMinutes;

    const [timeHours, timeMinutes] = time.split(':').map(Number);
    const currentTimeInMinutes = timeHours * 60 + timeMinutes;

    if (startTime && time === startTime) {
      return false;
    }

    if (currentTimeInMinutes < currentInMinutes) {
      return true;
    }

    if (startTime) {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const startInMinutes = startHours * 60 + startMinutes;
      if (currentTimeInMinutes <= startInMinutes) {
        return true;
      }
    }

    return false;
  }
  isTimeEndDisabled(time: string): boolean {
    const timeEnd = this.timeEnd;

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentInMinutes = currentHours * 60 + currentMinutes;

    const [timeHours, timeMinutes] = time.split(':').map(Number);
    const currentTimeInMinutes = timeHours * 60 + timeMinutes;

    if (timeEnd && time === timeEnd) {
      return false;
    }

    if (currentTimeInMinutes < currentInMinutes) {
      return true;
    }

    if (timeEnd) {
      const [startHours, startMinutes] = timeEnd.split(':').map(Number);
      const startInMinutes = startHours * 60 + startMinutes;
      if (currentTimeInMinutes <= startInMinutes) {
        return true;
      }
    }

    return false;
  }
  changeDateFormat(event: MatDatepickerInputEvent<Date>) {
    if (!event.value) return;
    const selectedDate = event.value; // วันที่ที่ผู้ใช้เลือก
    const today = DateTime.now(); // วันที่ปัจจุบัน
    // const cutoffDate = today.set({ day: 10 }).startOf('day'); // วันที่ 10 ของเดือนปัจจุบัน
    const cutoffDate = today.set({ day: 10 }).endOf('day'); // 👉 รวมวันที่ 10
    let minDate, maxDate;
    if (today <= cutoffDate) {
      // วันนี้ "ถึงวันที่ 10" → ย้อนถึงต้นเดือนที่แล้วได้
      minDate = today.minus({ months: 1 }).startOf('month');
    } else {
      // วันนี้ "หลังวันที่ 10" → ย้อนแค่ต้นเดือนนี้
      minDate = today.startOf('month');
    }

    if (!(selectedDate >= minDate)) {
      this.toastr.error(`You can only select a date no later than ${minDate.toFormat('dd/MM/yyyy')}`);
      const currentDate = new Date();
      this.form.controls['date'].setValue(currentDate); // รีเซ็ตค่า
      this.form.patchValue({
        date: DateTime.fromJSDate(currentDate).toFormat('yyyy-MM-dd')
      });
      this.getOTtime();
    } else {
      const date =
        this.form.patchValue({
          date: DateTime.fromISO(this.form.value.date).toFormat('yyyy-MM-dd')
        });
      this.getOTtime();
    }
  }

  getMonth() {
    const currentDate = new Date();
    let previousMonth = currentDate.getMonth();

    if (previousMonth === 0) {
      previousMonth = 11; // ธันวาคม
    } else {
      previousMonth -= 1;
    }

    // สร้างวันที่เป็นเดือนที่แล้ว
    const previousMonthDate = new Date(currentDate.getFullYear(), previousMonth, 1);

    // ดึงชื่อเดือนภาษาอังกฤษ
    const previousMonthName = previousMonthDate.toLocaleString('en-US', { month: 'long' });

    // console.log(previousMonthName); // ตัวอย่าง: "February"
    return previousMonthName
  }

}

