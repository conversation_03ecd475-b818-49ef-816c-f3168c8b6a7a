


export const leave_remainning = {
    leave_period: {
        start: '2024-04-01',
        end: '2025-03-31',
    },
    leave_summary: [
        {
            leave_type: 'Annual',
            entitlement: 14,
            taken: 0,
            remain: 14,
            excess: 0
        },
        {
            leave_type: 'Casual',
            entitlement: 3,
            taken: 0,
            remain: 3,
            excess: 0
        },
        {
            leave_type: 'Sick',
            entitlement: 30,
            taken: 0,
            remain: 30,
            excess: 0
        },
        {
            leave_type: 'Without Pay',
            entitlement: 0,
            taken: 0,
            remain: 0,
            excess: 0
        },
    ]
};

export const leave_status = {
    code: "200",
    data:
    {
        current_page: 1,
        data: [
            {
                id: '1',
                leaveNo: 'L20240101',
                name: '<PERSON>ir<PERSON> Thachu<PERSON>',
                leaveType: 'Annual',
                applyDate: '2024-11-04',
                time: 'Full Day',
                startDate: '2024-11-05',
                endDate: '2024-11-05',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Onprocess',
                isLap: false,
                Detail: 'Go abroad',
            },
            {
                id: '2',
                leaveNo: 'L20240102',
                name: '<PERSON><PERSON><PERSON> T<PERSON>chu<PERSON>',
                leaveType: 'Annual',
                applyDate: '2024-11-07',
                time: 'Full Day',
                startDate: '2024-11-08',
                endDate: '2024-11-08',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Onprocess',
                isLap: false,
                Detail: 'Go abroad',
            },
            
        ],
        first_page_url: "",
        from: 1,
        last_page: 1,
        last_page_url: "",
        links: "",
        next_page_url: "",
        path: "",
        per_page: 10,
        prev_page_url: "",
        to: 10,
        total: 10,
    },
    message: "Successful",
    status: true

};

export const leave_history = {
    code: "200",
    data:
    {
        current_page: 1,
        data: [
            {
                id: '1',
                leaveNo: 'L20240101',
                name: 'Anirut Thachuen',
                leaveType: 'Annual',
                applyDate: '2024-11-04',
                time: 'Full Day',
                startDate: '2024-11-05',
                endDate: '2024-11-05',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Approved',
                isLap: false,
                Detail: 'Go abroad',
            },
            {
                id: '2',
                leaveNo: 'L20240102',
                name: 'Anirut Thachuen',
                leaveType: 'Annual',
                applyDate: '2024-11-07',
                time: 'Full Day',
                startDate: '2024-11-08',
                endDate: '2024-11-08',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Approved',
                isLap: false,
                Detail: 'Go abroad',
            },
            {
                id: '2',
                leaveNo: 'L20240103',
                name: 'Anirut Thachuen',
                leaveType: 'Annual',
                applyDate: '2024-11-07',
                time: 'Full Day',
                startDate: '2024-11-08',
                endDate: '2024-11-08',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Approved',
                isLap: false,
                Detail: 'Go abroad',
            },
            {
                id: '2',
                leaveNo: 'L20240104',
                name: 'Anirut Thachuen',
                leaveType: 'Annual',
                applyDate: '2024-11-07',
                time: 'Full Day',
                startDate: '2024-11-08',
                endDate: '2024-11-08',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Approved',
                isLap: false,
                Detail: 'Go abroad',
            },
            {
                id: '2',
                leaveNo: 'L20240105',
                name: 'Anirut Thachuen',
                leaveType: 'Annual',
                applyDate: '2024-11-07',
                time: 'Full Day',
                startDate: '2024-11-08',
                endDate: '2024-11-08',
                total: 1,
                taken: 1,
                remain: 1,
                status: 'Approved',
                isLap: false,
                Detail: 'Go abroad',
            },
            
        ],
        first_page_url: "",
        from: 1,
        last_page: 1,
        last_page_url: "",
        links: "",
        next_page_url: "",
        path: "",
        per_page: 10,
        prev_page_url: "",
        to: 10,
        total: 10,
    },
    message: "Successful",
    status: true

};

