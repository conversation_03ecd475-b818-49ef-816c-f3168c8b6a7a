import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class PersonnelRequestService {

  private _categories: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _roles: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _branch: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _leave: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  get categories$() {
    return this._categories.asObservable();
  }

  constructor(private http: HttpClient) { }

  // datatable(dataTablesParameters: any) {

  //   return this.http.get('/api/leave/datatables',dataTablesParameters ).pipe(
  //     map((resp: any) => {
  //       return resp;
  //     })
  //   );
  // }

  getAllLeave(dataTablesParameters: any): Observable<any> {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;
    return this.http
      .get<any>(`${environment.apiUrl}/api/ot-air/datatables`, {
        params: {
          page: page,
          limit: length,
          sortBy: sortBy,
          search: search.value,
          ...removeEmpty(filter)
        }
      })
      // .get<any>(`/api/user_page`)
      .pipe(
        map((data: any) => {
          return data;
        }));
  }

  getOtPage(dataTablesParameters: any): Observable<any> {
    const { columns, order, search, start, length, filter, employeeId } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;
    return this.http
      .get<any>(`${environment.apiUrl}/api/ot-air/datatables`, {
        params: {
          page: page,
          limit: length,
          sortBy: sortBy,
          search: search.value,
          ...removeEmpty(filter)
        }
      })
      // .get<any>(`/api/user_page`)
      .pipe(
        map((data: any) => {
          return data;
        }));
  }

  getLeaveApprove(dataTablesParameters: any): Observable<any> {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;
    return this.http
      .get<any>(`${environment.apiUrl}/api/ot-air/wait-approve`, {
        params: {
          page: page,
          limit: length,
          sortBy: sortBy,
          search: search.value,
          ...removeEmpty(filter)
        }
      })
      // .get<any>(`/api/user_page`)
      .pipe(
        map((data: any) => {
          return data;
        }));
  }



  createOT(data: any) {
    return this.http.post(`${environment.apiUrl}/api/ot-air`, data)
  }

  getLeaveQty(data:any) {
    return this.http.post(`${environment.apiUrl}/api/leave/qty-day`,data).pipe(
      tap((resp: any) => {
        this._leave.next(resp.data);
      }),
    )
  }

  getOTById(id:any) {
    return this.http.get(`${environment.apiUrl}/api/ot-air/`+ id).pipe(
      tap((resp: any) => {
        this._leave.next(resp.data);
      }),
    )
  }

  getLaevePermission() {
    return this.http.get(`${environment.apiUrl}/api/employee/leave-permission`).pipe(
      tap((resp: any) => {
        this._leave.next(resp);
      }),
    )
  }
  getApproval() {
    return this.http.get(`${environment.apiUrl}/api/approve-list`).pipe(
      tap((resp: any) => {
        this._leave.next(resp);
      }),
    )
  }

  updateStatus(id: number, data: any) {
    return this.http.put(`${environment.apiUrl}/api/ot-air/${id}/approve`, data).pipe(
      tap((resp: any) => {
        this._leave.next(resp);
      }),
    )
  }
  getOTtime(data: any) {
    return this.http.post(`${environment.apiUrl}/api/ot/get_ot_time`, data)
  }
}
