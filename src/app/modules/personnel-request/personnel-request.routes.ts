import { Routes } from '@angular/router';

import { EmployeeService } from '../hr/employee/page.service';
import { inject } from '@angular/core';
import { ReportService } from '../report/page.service';
import { ZoneService } from '../hr/zone/page.service';
import { FloorService } from '../hr/floor/page.service';
import { ProjectService } from '../hr/project/page.service';
import { PersonnelRequestFormComponent } from './personnel-request-form/form.component';


export default [

    {
        path     : 'form',
        component: PersonnelRequestFormComponent,
        resolve: {
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            ProjectData: () => inject(ProjectService).getProject(),
            ZoneData: () => inject(ZoneService).getZone(),
            FloorData: () => inject(FloorService).getFloor(),
        }
    },


] as Routes;
