<mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
    <mat-label>Staff</mat-label>
    <input 
        matInput 
        [formControl]="employeeFilter" 
        [matAutocomplete]="AutoComplete" 
        placeholder="Search Staff" 
    />
    <mat-autocomplete 
        #AutoComplete="matAutocomplete" 
        (optionSelected)="onSelect($event.option.value, 'manual')">
        <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
            {{item.firstname}} {{item.lastname}}
        </mat-option>
    </mat-autocomplete>
</mat-form-field>