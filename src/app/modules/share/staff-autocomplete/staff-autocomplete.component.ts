import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { DateRange } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-staff-autocomplete',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        MatButtonModule,
        MatFormFieldModule,
        MatSelectModule,
        ReactiveFormsModule,
        MatAutocompleteModule,
        MatInputModule
    ],
    templateUrl: './staff-autocomplete.component.html'
})
export class StaffAutocompleteComponent implements OnInit, OnChanges {

    formFieldHelpers: string[] = ['fuse-mat-dense'];

    @Input() employeeFilter: FormControl = new FormControl('');
    @Input() filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    @Input() employee: any[] = [];
    @Output() selectedValue = new EventEmitter<any>();
    @Input() title: string = ''
    @Input() filter: any;
    @Input() items: any[] = []
    @Input() departmentId: string | null = null; // รับค่า departmentId จาก Parent

    constructor(
        private _fb: FormBuilder,
        private _activated: ActivatedRoute
    ) {
        this.filterEmployee.next(this.employee.slice());
    }

    ngOnInit(): void {


        this.employeeFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._filterBrand();
            });

    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['employeeFilter'] && changes['employeeFilter'].currentValue) {
            this.employeeFilter.setValue(changes['employeeFilter'].currentValue.value);
        }
    }

    filterStaff() {
        // Logic สำหรับกรองข้อมูล staff ตาม departmentId
        console.log(`Filtering staff by departmentId: ${this.departmentId}`);
    }

    protected _filterBrand() {
        if (!this.employee) {
            return;
        }

        const search = this.employeeFilter?.value
            ? String(this.employeeFilter.value).toLowerCase()
            : '';

        if (!search) {
            this.filterEmployee.next(this.employee.slice());
            return;
        }

        this.filterEmployee.next(
            this.employee.filter(item =>
                (item.firstname + ' ' + item.lastname).toLowerCase().includes(search) ||
                item.firstname.toLowerCase().includes(search) ||
                item.lastname.toLowerCase().includes(search)
            )
        );
    }


                   /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();


    onSelect(event: any, type: any) {
        if (!event) {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Selected');
            return;
        }
        const selectedData = event; // event จะเป็นออบเจ็กต์ item
        if (selectedData) {
            // this.form.patchValue({
            //     headId: selectedData.id,
            // });
            this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
            this.selectedValue.emit(selectedData)
        } else {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Found');
            return;
        }
    }
}
