<mat-form-field [ngClass]="formFieldHelpers" class="w-full">
    <mat-label>Company</mat-label>
    <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoComplete" placeholder="Search Company" />
    <mat-autocomplete #AutoComplete="matAutocomplete" (optionSelected)="onSelect($event.option.value, 'manual')">
        <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
            {{item.name ?? ''}}
        </mat-option>
    </mat-autocomplete>
</mat-form-field>