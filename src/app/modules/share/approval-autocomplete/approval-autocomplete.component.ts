import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { DateRange } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-approval-autocomplete',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        RouterOutlet,
        MatIcon,
        MatButtonModule,
        MatFormFieldModule,
        MatSelectModule,
        ReactiveFormsModule,
        MatAutocompleteModule,
        MatInputModule
    ],
    templateUrl: './approval-autocomplete.component.html'
})
export class ApprovalAutocompleteComponent implements OnInit {

    formFieldHelpers: string[] = ['fuse-mat-dense'];
    @Output() selectedValue = new EventEmitter<any>();
    @Input() title: string = ''
    @Input() filter: any;
    @Input() items: any[] = []
    ///approver filter
    approverFilter = new FormControl('');
    filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    approval: any[] = [];

    constructor(
        private _fb: FormBuilder,
        private _activated: ActivatedRoute
    ) {
        this.approval = this._activated.snapshot.data.approval;
        this.filterApprover.next(this.approval.slice());
    }

    ngOnInit(): void {

        this.approverFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._filterBrand();
            });

    }

    protected _filterBrand() {
        if (!this.approval) {
            return;
        }
        let search = this.approverFilter.value;
        if (!search) {
            this.filterApprover.next(this.approval.slice());
            return;
        } else {
            search
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterApprover.next(
            this.approval.filter(item =>
                (item.firstname + ' ' + item.lastname).includes(search) ||
                item.firstname.includes(search) ||
                item.lastname.includes(search)
            )
        );
    }

                   /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();


    onSelect(event: any, type: any) {
        if (!event) {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Selected');
            return;
        }
        const selectedData = event; // event จะเป็นออบเจ็กต์ item
        if (selectedData) {
            // this.form.patchValue({
            //     headId: selectedData.id,
            // });
            this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
            this.selectedValue.emit(selectedData)
        } else {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Found');
            return;
        }
    }
}
