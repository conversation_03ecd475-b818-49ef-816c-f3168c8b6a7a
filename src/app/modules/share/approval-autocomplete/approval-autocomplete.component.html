<mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
    <!-- <mat-label>Staff</mat-label> -->
    <input 
        matInput 
        [formControl]="approverFilter" 
        [matAutocomplete]="AutoComplete" 
        placeholder="Search Teamlead" 
    />
    <mat-autocomplete 
        #AutoComplete="matAutocomplete" 
        (optionSelected)="onSelect($event.option.value, 'manual')">
        <mat-option *ngFor="let item of filterApprover | async" [value]="item">
            {{item.firstname}} {{item.lastname}}
        </mat-option>
    </mat-autocomplete>
</mat-form-field>