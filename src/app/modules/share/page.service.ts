import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';

import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class ShareService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  constructor(private http: HttpClient) { }


}
