// generic-autocomplete.component.ts
import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, OnDestroy, TemplateRef, Output, EventEmitter } from '@angular/core';
import { FormControl, ControlValueAccessor, NG_VALUE_ACCESSOR, Validators, AbstractControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged, startWith } from 'rxjs/operators';

export interface AutocompleteConfig {
  valueKey: string;        // key สำหรับ value ที่ต้องการ return (เช่น 'id')
  displayKeys: string[];   // keys สำหรับแสดงผล (เช่น ['firstname', 'lastname'])
  searchKeys: string[];    // keys สำหรับใช้ในการค้นหา (เช่น ['firstname', 'lastname', 'email'])
  displaySeparator?: string; // ตัวคั่นสำหรับแสดงผล (default: ' ')
}

@Component({
  selector: 'app-generic-autocomplete',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatInputModule,
    MatIconModule,
  ],
  template: `
    <mat-form-field [ngClass]="formFieldClass" [class]="fieldClass">
      <mat-label>{{ label }}</mat-label>
      <input 
        matInput 
        [formControl]="searchControl" 
        [matAutocomplete]="autoComplete"
        [placeholder]="placeholder"
        (blur)="onBlur()"
        (focus)="onFocus()" />
      
      <!-- Clear button -->
      <button 
        *ngIf="showClearButton && searchControl.value && !readonly" 
        matSuffix 
        mat-icon-button 
        type="button"
        [readonly]="readonly"
        (click)="clearSelection()"
        [attr.aria-label]="'Clear selection'">
        <mat-icon>close</mat-icon>
      </button>

      <mat-autocomplete 
        #autoComplete="matAutocomplete"
        (optionSelected)="onSelectOption($event.option.value)">
        
        <!-- Custom template ถ้ามี -->
        <mat-option 
          *ngFor="let item of filteredItems | async" 
          [value]="item">
          <ng-container *ngIf="optionTemplate; else defaultOption">
            <ng-container *ngTemplateOutlet="optionTemplate; context: { $implicit: item, item: item }">
            </ng-container>
          </ng-container>
          <ng-template #defaultOption>
            {{ getDisplayText(item) }}
          </ng-template>
        </mat-option>
        
        <!-- แสดงเมื่อไม่พบข้อมูล -->
        <mat-option *ngIf="(filteredItems | async)?.length === 0" disabled>
          {{ noDataText }}
        </mat-option>
      </mat-autocomplete>
      
      <!-- Error messages -->
      <mat-error *ngIf="searchControl.invalid && (searchControl.dirty || searchControl.touched)">
        <span *ngIf="searchControl.errors?.['required']">Please select {{ label }}</span>
        <span *ngIf="searchControl.errors?.['invalidSelection']">Please select from the list</span>
        <ng-container *ngFor="let error of customErrors">
          <span *ngIf="searchControl.errors?.[error.key]">{{ error.message }}</span>
        </ng-container>
      </mat-error>
    </mat-form-field>
  `,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: GenericAutocompleteComponent,
      multi: true
    }
  ]
})
export class GenericAutocompleteComponent<T = any> implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() label: string = 'Select';
  @Input() placeholder: string = 'Search...';
  @Input() formFieldClass: string = '';
  @Input() fieldClass: string = 'w-full';
  @Input() noDataText: string = 'No data found';
  @Input() required: boolean = false;
  @Input() readonly: boolean = false;
  @Input() debounceTime: number = 300;
  @Input() showClearButton: boolean = true;

  // Configuration สำหรับกำหนดวิธีการแสดงผลและค้นหา
  @Input() config!: AutocompleteConfig;

  // ข้อมูลทั้งหมด
  @Input() items: T[] = [];

  // Custom template สำหรับ option
  @Input() optionTemplate?: TemplateRef<any>;

  // Custom error messages
  @Input() customErrors: Array<{ key: string, message: string }> = [];

  // Custom validators
  @Input() validators: any[] = [];

  @Output() selected = new EventEmitter<T>();

  searchControl = new FormControl('');
  filteredItems = new ReplaySubject<T[]>(1);

  private destroy$ = new Subject<void>();
  private selectedItem: T | null = null;

  // ControlValueAccessor properties
  private onChange = (value: any) => {};
  private onTouched = () => { };
  private disabled = false;

  ngOnInit(): void {
    this.validateConfig();
    this.setupValidation();
    this.setupFiltering();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private validateConfig(): void {
    if (!this.config) {
      throw new Error('GenericAutocompleteComponent: config is required');
    }
    if (!this.config.valueKey) {
      throw new Error('GenericAutocompleteComponent: config.valueKey is required');
    }
    if (!this.config.displayKeys || this.config.displayKeys.length === 0) {
      throw new Error('GenericAutocompleteComponent: config.displayKeys is required');
    }
    if (!this.config.searchKeys || this.config.searchKeys.length === 0) {
      this.config.searchKeys = this.config.displayKeys;
    }
    if (!this.config.displaySeparator) {
      this.config.displaySeparator = ' ';
    }
  }

  private setupValidation(): void {
    const validators = [...this.validators];

    if (this.required) {
      validators.push(Validators.required);
    }
    validators.push(this.itemValidator.bind(this));

    this.searchControl.setValidators(validators);
  }

  private setupFiltering(): void {
    this.searchControl.valueChanges.pipe(
      startWith(''),
      debounceTime(this.debounceTime),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(value => {
      // ถ้าเป็น object (selected item) ไม่ต้อง filter
      if (typeof value === 'object' && value !== null) {
        return;
      }
      this.filterItems(value);
    });

    // Initial load
    this.filteredItems.next(this.items);
  }

  private filterItems(searchValue: string | T): void {
    let filterValue = '';

    if (typeof searchValue === 'string') {
      filterValue = searchValue.toLowerCase();
    } else if (searchValue && typeof searchValue === 'object') {
      // ถ้าเป็น object (เลือกจาก dropdown) ไม่ต้อง filter
      return;
    }

    if (!filterValue) {
      this.filteredItems.next(this.items);
      return;
    }

    const filtered = this.items.filter(item => {
      return this.config.searchKeys.some(key => {
        const value = this.getNestedValue(item, key);
        return value && value.toString().toLowerCase().includes(filterValue);
      });
    });

    this.filteredItems.next(filtered);
  }

  onSelectOption(selectedItem: T): void {
    if (!selectedItem) {
      console.log('No item selected');
      return;
    }

    this.selectedItem = selectedItem;

    // Set display text ก่อน emit value
    const displayText = this.getDisplayText(selectedItem);
    this.searchControl.setValue(displayText, { emitEvent: false });

    // Clear any validation errors
    this.searchControl.setErrors(null);

    // Emit value change
    const value = this.getNestedValue(selectedItem, this.config.valueKey);
    this.onChange(value);

    this.selected.emit(selectedItem);
  }

  clearSelection(): void {
    this.selectedItem = null;
    this.searchControl.setValue('');
    this.onChange(null);
    this.filteredItems.next(this.items);
    this.searchControl.markAsTouched();
  }

  getDisplayText = (item: T): string => {
    if (!item) return '';

    const displayParts = this.config.displayKeys.map(key => {
      const value = this.getNestedValue(item, key);
      return value ? value.toString() : '';
    }).filter(part => part);

    return displayParts.join(this.config.displaySeparator);
  }

  // displayFn = (item: T): string => {
  //   return this.getDisplayText(item);
  // }

  onBlur(): void {
    this.onTouched();

    // ตรวจสอบว่าค่าที่พิมพ์ตรงกับรายการที่มีหรือไม่
    const currentValue = this.searchControl.value;

    // ถ้ามี selectedItem อยู่แล้ว และ display text ตรงกัน ไม่ต้อง validate
    if (this.selectedItem && currentValue === this.getDisplayText(this.selectedItem)) {
      return;
    }

    if (typeof currentValue === 'string' && currentValue.trim()) {
      const matchedItem = this.items.find(item =>
        this.getDisplayText(item).toLowerCase() === currentValue.toLowerCase()
      );

      if (!matchedItem) {
        this.searchControl.setErrors({ invalidSelection: true });
        this.selectedItem = null;
        this.onChange(null);
      } else {
        // ถ้าพบ item ที่ตรงกัน ให้ set เป็น selected item
        this.selectedItem = matchedItem;
        const value = this.getNestedValue(matchedItem, this.config.valueKey);
        this.onChange(value);
      }
    } else if (!currentValue || currentValue.trim() === '') {
      // ถ้าไม่มีค่า
      this.selectedItem = null;
      this.onChange(null);
    }
  }

  onFocus(): void {
    // Reset filter when focused
    this.filteredItems.next(this.items);
  }

  // Custom validator
  private itemValidator(control: AbstractControl): { [key: string]: any } | null {
    const value = control.value;

    if (!value || (typeof value === 'string' && !value.trim())) {
      return null; // จะถูก handle โดย required validator
    }

    // ถ้ามี selectedItem และ display text ตรงกัน ถือว่า valid
    if (this.selectedItem && value === this.getDisplayText(this.selectedItem)) {
      return null;
    }

    if (typeof value === 'string') {
      const matchedItem = this.items.find(item =>
        this.getDisplayText(item).toLowerCase() === value.toLowerCase()
      );

      if (!matchedItem) {
        return { invalidSelection: true };
      }
    }

    return null;
  }

  // Helper method สำหรับเข้าถึง nested property
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    if (value !== undefined && value !== null) {
      const item = this.items.find(item =>
        this.getNestedValue(item, this.config.valueKey) === value
      );

      if (item) {
        this.selectedItem = item;
        this.searchControl.setValue(this.getDisplayText(item), { emitEvent: false });
      }
    } else {
      this.selectedItem = null;
      this.searchControl.setValue('', { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (isDisabled) {
      this.searchControl.disable();
    } else {
      this.searchControl.enable();
    }
  }
}