import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { ShareService } from '../page.service';
@Component({
    selector: 'app-filter',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule
    ],
    templateUrl: './page.component.html',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FilterComponent implements OnInit, AfterViewInit {

    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    data: any;
    constructor(
        private _service: ShareService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        private _router: Router,

    ) {


    }
    ngOnInit(): void {
     
    }

    ngAfterViewInit() {
  
    }

    ngOnDestroy(): void {

    }

  







}
