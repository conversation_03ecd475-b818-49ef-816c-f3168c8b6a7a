import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from 'environments/environment';

@Injectable({
  providedIn: 'root'
})
export class LongdoMapService {

  constructor(private http: HttpClient) { }

  getAddress(lat: number, lon: number) {
    return this.http.get('https://api.longdo.com/map/services/address', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      params: {
        lat: lat,
        lon: lon,
        noelevation: 1,
        noaoi: 0,
        key: environment.longdo_key,
        lang: 'en'
      }
    }).pipe(
      map((resp: any) => {
        let subdistrict = resp?.subdistrict ?? ''
        let district = resp?.district ?? ''
        let province = resp?.province ?? ''
        let country = resp?.country ?? ''
        let postcode = resp?.postcode ?? ''

        const address = ''.concat(subdistrict, ' ', district, ' ', province, ' ', postcode, ' ', country)
        return { ...resp, lat, lon, address }
      }),
    )
  }
}
