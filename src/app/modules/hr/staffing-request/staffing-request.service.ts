import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { removeEmpty } from 'app/helper';
import { param } from 'jquery';

@Injectable({
  providedIn: 'root'
})
export class StaffingRequestService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _title: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _level: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _department: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _leaveType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _employeeType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _workShift: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _position: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _permission: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }



  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/personal-form/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }
  getById(id: number) {
    return this.http.get(`${environment.apiUrl}/api/personal-form/${id}`)
  }

  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/personal-form`, data)
  }

  update(id: number, data: any) {
    return this.http.put(`${environment.apiUrl}/api/personal-form/${id}`, data)
  }

  delete(id: number) {
    return this.http.delete(`${environment.apiUrl}/api/personal-form/${id}`)
  }

  getDepartment() {
    return this.http.get(environment.apiUrl + '/api/department')
  }
  // getPosition() {
  //   return this.http.get(environment.apiUrl + '/api/get_position');
  // }
  getTitle() {
    return this.http.get(environment.apiUrl + '/api/title')
  }

  updateStatus(id: number, data: any) {
    return this.http.post(`${environment.apiUrl}/api/personal-form/${id}/approve`, data)
  }
}
