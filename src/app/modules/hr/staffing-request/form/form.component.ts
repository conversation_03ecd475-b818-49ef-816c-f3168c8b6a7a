import { user } from './../../../../mock-api/common/user/data';
import { data } from './../../zone/mock-data';
import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule, Location, NgClass } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { QuillModule } from 'ngx-quill';
import { ToastrService } from 'ngx-toastr';
import { StaffingRequestService } from '../staffing-request.service';
import { MatDividerModule } from '@angular/material/divider';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
@Component({
  selector: 'form-user',
  templateUrl: './form.component.html',
  standalone: true,
  imports: [
    MatIconModule,
    FormsModule,
    MatFormFieldModule,
    NgClass,
    MatInputModule,
    TextFieldModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    MatChipsModule,
    MatDatepickerModule,
    CommonModule,
    NgxDropzoneModule,
    MatRadioModule,
    MatAutocompleteModule,
    QuillModule,
    MatTabsModule,
    MatDividerModule,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];

  status = [
    { value: 'request', name: 'Request' },
    { value: 'approved', name: 'Approved' },
    { value: 'reject', name: 'Reject' },
    { value: 'cancel', name: 'Cancel' },
  ];

  Id: number;
  userId: number;
  form: FormGroup;
  user: any;

  protected _onDestroy = new Subject<void>();

  departmentsFilter = new FormControl('');
  filterdepartments: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  departments: any[] = [];

  titlesFilter = new FormControl('');
  filtertitles: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  titles: any[] = [];

  approverFilter = new FormControl('');
  filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  approval: any[] = [];

  url: string;
  /**
   * Constructor
   */
  constructor(
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder,
    private _Service: StaffingRequestService,
    private dialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _toast: ToastrService,
    public location: Location,
  ) {

    this.url = this._router.url;

    this.user = JSON.parse(localStorage.getItem('user'))
    this.userId = this.user ? this.user.id : 0;

    this.Id = this._activatedRoute.snapshot.params.id;
    this.titles = this._activatedRoute.snapshot.data.title;
    this.departments = this._activatedRoute.snapshot.data.department;
    this.approval = this._activatedRoute.snapshot.data.approval

    if (this.url == '/personnel-request-emp/form') {
      this.filterApprover.next([this.approval.find(e => e.id == this.user.id)]);
    } else {
      this.filterApprover.next(this.approval.slice());
    }

    this.filtertitles.next(this.titles.slice());
    this.filterdepartments.next(this.departments.slice());

    const currentDate = new Date().toISOString().split('T')[0];

    this.form = this._fb.group({
      dateToStart: [null, Validators.required],
      requestDate: [currentDate, Validators.required],
      approveDate: [null],
      positionId: [0, Validators.required],
      departmentId: [0, Validators.required],
      numberOfRequests: [1, [Validators.required, Validators.min(1)]],
      sex: ['',],
      education: ['', Validators.required],
      major: [''],
      experience: [''],
      skillLanguage: [''],
      skillTyping: [''],
      skillOther: [''],
      trainingCourse: [''],
      otherRequirement: [''],
      staffDetail: [''],
      remark: [''],
      requesterId: [this.userId, Validators.required],
      approverId: [null],
      status: ['request']
    });

  }
  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  ngOnInit() {
    this.loadData();
    this.titlesFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filtertitles();
      });
    this.departmentsFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterdepartments();
      });

    this.approverFilter.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this._filterApprover();
      });
  }

  /**
   * After view init
   */
  ngAfterViewInit(): void { }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
  }
  //================================================================================================

  loadData(): void {
    const applicantId = this.Id;

    // ตรวจสอบว่ามี applicantId หรือไม่
    if (!applicantId) {
      return;
    }
    //ประวัตส่วนตัว
    this._Service.getById(applicantId).subscribe({
      next: (data: any) => {
        console.log('Data loaded:', data);
        this.form.patchValue({
          ...data,
          departmentId: data?.department.id,
          positionId: data?.position.id,
        });
        this.titlesFilter.setValue(data.position.name);
        this.departmentsFilter.setValue(data.department.name);
        this._changeDetectorRef.markForCheck();
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this._toast.error('Failed to load data.');
      },
    });
  }


  private transformDate(dateString: string): string | null {
    if (!dateString) return null;
    const date = new Date(dateString); // แปลง string เป็น Date object
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  Submit(): void {

    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this._toast.error('Please check value.');
      return;
    }
    let formValue = this.form.value;
    formValue.dateToStart = this.transformDate(formValue.dateToStart);
    if (this.Id) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Edit value",
        message: "Do you want to edit value? ",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });
      // Subscribe to the confirmation dialog closed action
      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          this._Service.update(this.Id, formValue).subscribe({
            next: (resp: any) => {
              if (this.url == '') {
                this._router.navigate(['personnel-request-emp']);
              } else {
                this._router.navigate(['personnel-request']);
              }

              this._toast.success('Successed')
            },
            error: (err: any) => {
              this.form.enable();
              this._fuseConfirmationService.open({
                title: "Please check value.",
                message: err.error.message,
                icon: {
                  show: true,
                  name: "heroicons_outline:exclamation",
                  color: "warning"
                },
                actions: {
                  confirm: {
                    show: false,
                    label: "Close",
                    color: "primary"
                  },
                  cancel: {
                    show: false,
                    label: "Cancel"
                  }
                },
                dismissible: true
              });
            }
          });
        }
      });
    } else {
      const confirmation = this._fuseConfirmationService.open({
        title: "Add value",
        message: "Do you want to save data ?",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });
      // Subscribe to the confirmation dialog closed action
      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          this._Service.create(formValue).subscribe({
            next: (resp: any) => {
              this._router.navigate(['personnel-request']);
              this._toast.success('Successed')
            },
            error: (err: any) => {
              this.form.enable();
              this._fuseConfirmationService.open({
                title: "Please check value.",
                message: err.error.message,
                icon: {
                  show: true,
                  name: "heroicons_outline:exclamation",
                  color: "warning"
                },
                actions: {
                  confirm: {
                    show: false,
                    label: "Save",
                    color: "primary"
                  },
                  cancel: {
                    show: false,
                    label: "Cancel"
                  }
                },
                dismissible: true
              });
            }
          });
        }
      });
    }
  }


  onCancel(): void {
    // const email = this.accountForm.get('email')?.value;
    // this.accountForm.reset({ email }); //
    // alert('Form has been reset.');
    // this._router.navigate(['staffing-request']);

    this.location.back();
  }

  protected _filtertitles() {
    if (!this.titles) {
      return;
    }
    let search = this.titlesFilter.value;
    // console.log(search, 's');

    if (!search) {
      this.filtertitles.next(this.titles.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filtertitles.next(
      this.titles.filter(item =>
        item.name.toLowerCase().includes(search)
      )
    );
  }
  onSelecttitles(event: any, type: any) {
    if (!event) {
      if (this.titlesFilter.invalid) {
        this.titlesFilter.markAsTouched();
      }
      console.log('No titles Selected');
      return;
    }

    const selectedData = event;

    if (selectedData) {
      this.form.patchValue({
        positionId: selectedData.id,
      });
      this.titlesFilter.setValue(`${selectedData.name}`);
    } else {
      if (this.titlesFilter.invalid) {
        this.titlesFilter.markAsTouched();
      }
      console.log('No titles Found');
      return;
    }
  }

  protected _filterdepartments() {
    if (!this.departments) {
      return;
    }
    let search = this.departmentsFilter.value;
    if (!search) {
      this.filterdepartments.next(this.departments.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }
    this.filterdepartments.next(
      this.departments.filter(item =>
        item.name.toLowerCase().includes(search)
      )
    );
  }

  onSelectdepartments(event: any, type: any) {
    if (!event) {
      if (this.departmentsFilter.invalid) {
        this.departmentsFilter.markAsTouched();
      }
      console.log('No departments Selected');
      return;
    }

    const selectedData = event;

    if (selectedData) {
      this.form.patchValue({
        departmentId: selectedData.id,
      });
      this.departmentsFilter.setValue(`${selectedData.name}`);
    } else {
      if (this.departmentsFilter.invalid) {
        this.departmentsFilter.markAsTouched();
      }
      console.log('No departments Found');
      return;
    }
  }

  onSelectApprover(event: any, type: any) {
    if (!event) {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Selected');
      return;
    }

    const selectedData = event; // event จะเป็นออบเจ็กต์ item

    if (selectedData) {
      this.form.patchValue({
        requesterId: selectedData.id,
        approverId: selectedData.head?.id
      });

      this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
    } else {
      if (this.approverFilter.invalid) {
        this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
      }
      console.log('No Approver Found');
      return;
    }
  }

  protected _filterApprover() {
    if (!this.approval) {
      return;
    }
    let search = this.approverFilter.value;

    if (!search) {
      this.filterApprover.next(this.approval.slice());
      return;
    } else {
      search = search.toString().toLowerCase();
    }

    // กรองข้อมูลโดยค้นหาใน firstname และ lastname
    this.filterApprover.next(
      this.approval.filter(item =>
        (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
        item.firstname.toLowerCase().includes(search) ||
        item.lastname.toLowerCase().includes(search)
      )
    );
  }
}
