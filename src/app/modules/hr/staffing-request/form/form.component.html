<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto px-4 sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between pb-2 my-5 gap-4 border-b-2 border-gray-300">
      <div class="px-4 md:px-0">
        <h2 *ngIf="this.Id == null"
          class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Personnel Request Form
        </h2>
        <h2 *ngIf="this.Id != null"
          class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Edit Personnel Request Form
        </h2>
      </div>
    </div>
    <!-- Add mat-tab-group with 9 tabs -->

    <form [formGroup]="form">
      <div class="w-full">
        <div class="mt-4 grid gap-4 sm:grid-cols-12">
          <!-- Position & Department -->
          <div class="sm:col-span-6">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Position</mat-label>
              <input matInput [formControl]="titlesFilter" [matAutocomplete]="titlesAutoComplete"
                placeholder="Search Position" />
              <mat-autocomplete #titlesAutoComplete="matAutocomplete"
                (optionSelected)="onSelecttitles($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filtertitles | async" [value]="item">
                  {{item.name}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Department</mat-label>
              <input matInput [formControl]="departmentsFilter" [matAutocomplete]="departmentsAutoComplete"
                placeholder="Search Department" />
              <mat-autocomplete #departmentsAutoComplete="matAutocomplete"
                (optionSelected)="onSelectdepartments($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterdepartments | async" [value]="item">
                  {{item.name}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>

          <!-- Date to Start & Number of Requests -->
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Date to Start</mat-label>
              <input matInput [matDatepicker]="startDate" formControlName="dateToStart">
              <mat-datepicker-toggle matSuffix [for]="startDate"></mat-datepicker-toggle>
              <mat-datepicker #startDate></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Number of Requests</mat-label>
              <input matInput type="number" formControlName="numberOfRequests" min="1">
            </mat-form-field>
          </div>

          <!-- Sex & Education -->
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Sex</mat-label>
              <mat-select formControlName="sex">
                <mat-option value="male">Male</mat-option>
                <mat-option value="female">Female</mat-option>
                <mat-option value="other">Other</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Education</mat-label>
              <input matInput formControlName="education" placeholder="Enter Education">
            </mat-form-field>
          </div>

          <!-- Major & Experience -->
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Major</mat-label>
              <input matInput formControlName="major" placeholder="Enter Major">
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Experience</mat-label>
              <input matInput formControlName="experience" placeholder="Enter Experience">
            </mat-form-field>
          </div>

          <!-- Skills -->
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Language Skills</mat-label>
              <input matInput formControlName="skillLanguage" placeholder="Enter Language Skills">
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Typing Skills</mat-label>
              <input matInput formControlName="skillTyping" placeholder="Enter Typing Skills">
            </mat-form-field>
          </div>
          <div class="sm:col-span-12">
            <mat-form-field class="w-full">
              <mat-label>Other Skills</mat-label>
              <input matInput formControlName="skillOther" placeholder="Enter Other Skills">
            </mat-form-field>
          </div>

          <!-- Training Course & Other Requirements -->
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Training Course</mat-label>
              <input matInput formControlName="trainingCourse" placeholder="Enter Training Course">
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Other Requirements</mat-label>
              <input matInput formControlName="otherRequirement" placeholder="Enter Other Requirements">
            </mat-form-field>
          </div>

          <!-- Staff Details & Remark -->
          <div class="sm:col-span-12">
            <mat-form-field class="w-full">
              <mat-label>Staff Details</mat-label>
              <textarea matInput formControlName="staffDetail" rows="3"></textarea>
            </mat-form-field>
          </div>
          <div class="sm:col-span-12">
            <mat-form-field class="w-full">
              <mat-label>Remark</mat-label>
              <textarea matInput formControlName="remark" rows="3"></textarea>
            </mat-form-field>
          </div>

          <!-- Approval Section -->
          <div class="sm:col-span-6">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
              <mat-label>Request By</mat-label>
              <input matInput [formControl]="approverFilter" [matAutocomplete]="approverAutoComplete"
                placeholder="Search Approver" />
              <mat-autocomplete #approverAutoComplete="matAutocomplete"
                (optionSelected)="onSelectApprover($event.option.value, 'manual')">
                <mat-option *ngFor="let item of filterApprover | async" [value]="item">
                  {{item.firstname}} {{item.lastname}}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>

          <mat-form-field [ngClass]="formFieldHelpers" class="w-full sm:col-span-6">
            <mat-label>Approver</mat-label>
            <mat-select [formControlName]="'status'">
              <mat-option *ngFor="let item of status" [value]="item.value">
                {{item.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Requested by (ID)</mat-label>
              <input matInput formControlName="requesterId" type="number">
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="requestDate" formControlName="requestDate">
              <mat-datepicker-toggle matSuffix [for]="requestDate"></mat-datepicker-toggle>
              <mat-datepicker #requestDate></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Approved by (ID)</mat-label>
              <input matInput formControlName="approverId" type="number">
            </mat-form-field>
          </div>
          <div class="sm:col-span-6">
            <mat-form-field class="w-full">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="approveDate" formControlName="approveDate">
              <mat-datepicker-toggle matSuffix [for]="approveDate"></mat-datepicker-toggle>
              <mat-datepicker #approveDate></mat-datepicker>
            </mat-form-field>
          </div> -->
        </div>

        <!-- Actions -->
        <div class="flex justify-end mt-6">
          <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
          <button class="ml-4" mat-flat-button color="primary" type="submit" (click)="Submit()">Submit</button>
        </div>
      </div>
    </form>

  </div>
</div>
