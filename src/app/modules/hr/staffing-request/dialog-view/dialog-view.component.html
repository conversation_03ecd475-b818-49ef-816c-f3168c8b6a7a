<div>
  <div class="flex flex-row justify-between border-b-2 border-gray-300">
    <div class="my-2 text-lg text-black font-bold">
      <h4>Preview</h4>
    </div>

    @if (this.itemData?.status == 'request' && userRole == 'EMP') {
      <div class="flex flex-row mb-2 justify-end">
        <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
          <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit('approved')">
            Approve
          </button>
          <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="Submit('reject')">
            Reject
          </button>
        </div>
      </div>
    }
  </div>
  <div class="grid grid-cols-1 my-2 border-b-2 border-gray-300">
    <div class="flex flex-col gap-2 mb-2 mx-2">
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>No. </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.code ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Position </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.position?.name ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Department </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.department?.name ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Request By </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.requestBy?.firstname}} {{this.itemData?.requestBy?.lastname}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Request Date </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.requestDate | date:'dd/MM/yyyy'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Number Of Requests</h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.numberOfRequests}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Sex </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.sex ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Major </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.major ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Experience </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.experience ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Education </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.education ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Training Course </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.trainingCourse ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Skills </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.skillLanguage ?? '-'}}, {{this.itemData?.skillTyping ?? '-'}}, {{this.itemData?.skillOther ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Staff Detail</h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.staffDetail ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Other Requirement </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.otherRequirement ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2">
        <div class="w-2/6">
          <h4>Remark </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.remark ?? '-'}}</span>
        </div>
      </div>
      <div class="flex flex-row mt-2 gap-2" *ngIf="this.itemData?.approveBy != null">
        <div class="w-2/6">
          <h4>Approve By </h4>
        </div>
        <div class="w-4/6">
          <span class="font-bold text-gray-700">: {{this.itemData?.approveBy?.firstname}} {{this.itemData?.approveBy?.lastname}}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="flex flex-row mt-2 justify-center">
    <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
      <button class="px-6 ml-3 mb-2" mat-flat-button [color]="'accent'" (click)="onClose()">
        Close
      </button>
    </div>
  </div>
</div>
