import { Routes } from '@angular/router';
import { StaffingRequestComponent } from './staffing-request.component';
import { FormComponent } from './form/form.component';
import { inject } from '@angular/core';
import { StaffingRequestService } from './staffing-request.service';
import { EmployeeService } from '../employee/page.service';

export default [
  {
    path: '',
    component: StaffingRequestComponent,
  },
  {
    path: 'form',
    component: FormComponent,
    resolve: {
        department: () => inject(StaffingRequestService).getDepartment(),
        title: () => inject(StaffingRequestService).getTitle(),
        approval: () => inject(EmployeeService).getApproval(),
    },
  },
  {
    path: 'edit/:id',
    component: FormComponent,
    resolve: {
      department: () => inject(StaffingRequestService).getDepartment(),
      title: () => inject(StaffingRequestService).getTitle(),
      approval: () => inject(EmployeeService).getApproval(),
    },
  },

] as Routes;
