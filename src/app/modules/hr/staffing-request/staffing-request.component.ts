import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { StaffingRequestService } from './staffing-request.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { DialogViewComponent } from './dialog-view/dialog-view.component';
import { environment } from 'environments/environment';

@Component({
    selector: 'app-applicant-employee',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        RouterLink,
    ],
    templateUrl: './staffing-request.component.html',
    styleUrl: './staffing-request.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class StaffingRequestComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('position') position: any;
    @ViewChild('department') department: any;
    @ViewChild('date') date: any;
    @ViewChild('requestBy') requestBy: any;
    @ViewChild('No') No: any;
    @ViewChild('approve') approve: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    title: any;
    level: any;
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;

    data: any;

    user: any;

    constructor(
        private _service: StaffingRequestService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,
        private _matDialog: MatDialog,
        private _changeDetectorRef: ChangeDetectorRef
    ) {
      this.user = JSON.parse(localStorage.getItem('user'))
      console.log(this.user);

    }
    ngOnInit(): void {
      setTimeout(() => this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
          console.log('this.dtOptions',this.dtOptions);

            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        // let formValue = this.form.value

        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            // scrollX: true,
            order: [[1, 'desc']],
            ajax: (dataTablesParameters: any, callback) => {
                console.log('dataTablesParameters', dataTablesParameters);

                dataTablesParameters.filter = {}

                if (this._router.url == '/personnel-request-emp') {
                  dataTablesParameters.filter = {
                    'filter.requestBy.id': '$or:$eq:' + this.user.id,
                    'filter.approveBy.id': '$or:$eq:' + this.user.id,
                  }
                }

                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                      console.log('resp',resp);

                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: 'No.',
                    data: null,
                    className: 'w-10 text-center',
                    orderable: false,
                    render: function (data, type, row, meta) {
                        return meta.row + 1;
                    }
                },
                {
                    title: 'Code',
                    data: 'code',
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.No,
                  },
                },
                {
                    title: 'Position',
                    data: 'position',
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.position,
                  },
                },
                {
                    title: 'Department',
                    data: 'department',
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.department,
                  },
                },
                {
                    title: 'Date Start',
                    data: 'dateToStart',
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.date,
                  },
                },
                {
                    title: 'Request By',
                    data: 'requestBy',
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.requestBy,
                    },
                },
                {
                    title: 'Request',
                    data: 'numberOfRequests',
                    defaultContent: '-',
                    className: 'text-left',
                },
                {
                    title: 'Sex',
                    data: 'sex',
                    defaultContent: '-',
                    className: 'text-left',
                },
                {
                    title: 'Approve By',
                    data: null,
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.approve,
                    },
                },
                {
                  title: 'Status',
                  data: 'status',
                  defaultContent: '-',
                  className: 'text-left',
                  render: function (data, type, row, meta) {
                    return data == 'request'
                      ? '<span class="badge badge-warning">Request</span>'
                      : data == 'approved'
                        ? '<span class="badge badge-success">Approved</span>'
                        : '<span class="badge badge-danger">Rejected</span>'
                }
              },
                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    orderable: false,
                    className: 'w-15 text-center'
                }
            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    // opendialogAdd() {
    //     this._router.navigate(['staffing-request/form'])
    // }

    openDialogEdit(id: any) {
        // this._router.navigate(['/edit', id])
        this._router.navigateByUrl('edit/' + id)
    }

    printPDF(id: any) {
      window.open(environment.apiUrl + `/api/report/redirect-report?id=${id}&type=personal_form`)
    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to delete? ",
            message: "Please check the information. If the information is deleted, it cannot be retrieved.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('Successed');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
    onChangeType() {
        this.rerender()
    }

    clearData() {
        this.form.reset();
        this.rerender();
    }

    View(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewComponent, {
            width: '500px',
            height: 'auto',
            maxHeight: '90vh',
            data: {
                itemid: itemId,
            },
        });
        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            // this.GetLeavePermission();
            // this.GetLeaveRemainning();
            this._changeDetectorRef.markForCheck();
        });
    }
}
