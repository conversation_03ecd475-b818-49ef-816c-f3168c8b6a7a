import { Routes } from '@angular/router';
import { WorkShiftComponent } from './page.component';
import { FormComponent } from './form/form.component';
import { inject } from '@angular/core';
import { WorkShiftService } from './page.service';

export default [
    {
        path: '',
        children: [
            {
                path     : 'list',
                component: WorkShiftComponent,
            },
            {
                path     : 'form',
                component: FormComponent,
                resolve: {
                    // branch: () => inject(EmployeeService).getBranch(),
                    // permission: () => inject(EmployeeService).getPermission(),
                    // department: () => inject(EmployeeService).getDepartment(),
                    // position: () => inject(EmployeeService).getPosition(),
                }, 
            },
            {
                path     : 'edit/:id',
                component: FormComponent,
                resolve: {
                    // branch: () => inject(EmployeeService).getBranch(),
                    // permission: () => inject(EmployeeService).getPermission(),
                    // department: () => inject(EmployeeService).getDepartment(),
                    // position: () => inject(EmployeeService).getPosition(),
                }, 
            },
        ]
    }
    
] as Routes;
