import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormArray,
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { WorkShiftService } from '../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { forkJoin, lastValueFrom } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';

@Component({
    selector: 'form-work-shift',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;


    warehouseData: any;
    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    department: any[] = [];
    position: any[] = [];

    daysOfWeek = [
        { full: 'Monday', short: '1' , time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: true},
        { full: 'Tuesday', short: '2' , time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: true},
        { full: 'Wednesday', short: '3', time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: true},
        { full: 'Thursday', short: '4' , time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: true},
        { full: 'Friday', short: '5' , time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: true},
        { full: 'Saturday', short: '6' , time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: false},
        { full: 'Sunday', short: '7' , time_in: '08:00' , time_out: '17:00', break_in: '12:00',break_out: '13:00', active: false},
    ]
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: WorkShiftService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        // this.branch = this._activatedRoute.snapshot.data.branch.data;
        // this.department = this._activatedRoute.snapshot.data.department.data;
        // this.position = this._activatedRoute.snapshot.data.position.data;
        // this.permission = this._activatedRoute.snapshot.data.permission.data;


    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            code: [''],
            name: [''],
            active: true,
            time: this._fb.array([]),
        });

        if (this.Id) {
            this._Service.getWorkShiftById(this.Id).subscribe((resp: any) => {
                this.itemData = resp.data
                this.form.patchValue({
                    name: resp.name,
                    code: resp.code
                })
                for (const item of resp.workShiftTimes) {
                    this.addShiftbyid(item)
                }
            })
        } else {
            for (const day of this.daysOfWeek) {
                this.addShift(day)
            }
        }

    }

    addShift(shift?: any) {
        const shiftGroup = this._fb.group({
            day: [shift?.short || ''],
            fullname: [shift?.full || ''],
            time_in: [shift?.time_in || ''],
            break_in: [shift?.break_in || ''],
            break_out: [shift?.break_out || ''],
            time_out: [shift?.time_out || ''],
            active: [shift?.active]
        });
        this.Time.push(shiftGroup);
    }
    addShiftbyid(shift?: any) {
        const filterday = this.daysOfWeek.find(day => day.short === shift?.day);

        const shiftGroup = this._fb.group({
            day: [shift?.day || ''],
            fullname: [filterday.full || ''],
            time_in: [shift?.time_in || ''],
            break_in: [shift?.break_in || ''],
            break_out: [shift?.break_out || ''],
            time_out: [shift?.time_out || ''],
            active: [shift?.active === true]
        });
        this.Time.push(shiftGroup);
    }

    get Time() {
        return this.form.get("time") as FormArray;
    }

    removeEmployee(index: any) {
        this.Time.removeAt(index);
    }

    private createShiftFormGroup(day: { full: string; short: string }): FormGroup {
        return this._fb.group({
            day: [day.short],
            fullname: [day.full],
            time_in: [''],
            break_in: [''],
            break_out: [''],
            time_out: [''],
            active: [false]
        });
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }




    Submit(): void {
        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit data",
                message: "Do you want edit data ? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value


                    this._Service.update(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['work-shift/list']);
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check vale",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "ยกเลิก"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add data",
                message: "Do you want to save data",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value;

                    this._Service.create(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['work-shift/list']);

                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "ยืนยัน",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "ยกเลิก"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }

    backTo() {
        this._router.navigate(['work-shift/list'])
    }

}
