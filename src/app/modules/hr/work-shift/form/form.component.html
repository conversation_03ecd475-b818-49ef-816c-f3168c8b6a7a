<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a class="whitespace-nowrap text-primary-500 cursor-pointer" 
                        >Work Shift list</a
                    >
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon
                        class="text-secondary icon-size-5"
                        [svgIcon]="'heroicons_mini:chevron-right'"
                    ></mat-icon>
                    <a class="ml-1 text-primary-500" *ngIf="!this.Id">Create new work shift</a>
                    <a class="ml-1 text-primary-500" *ngIf="this.Id">Edit work shift</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Create New work shift
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Edit work shift
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-6 sm:p-10">
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
                [formGroup]="form">
                <div class="flex flex-col gap-4 justify-center">
                    <div class="flex flex-col md:flex-row w-full md:w-1/3 gap-2">
                        <div class="-mx-3 md:flex mb-0 w-full">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Group Code</mat-label>
                                    <input matInput [formControlName]="'code'"
                                        [placeholder]="''">
                                </mat-form-field>
                            </div>
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Group Name</mat-label>
                                    <input matInput [formControlName]="'name'"
                                        [placeholder]="''">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-full">
                        <div class="overflow-x-auto">
                            <table class="w-full table-auto text-left border-collapse">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-2">Day</th>
                                        <th class="px-4 py-2">Time In</th>
                                        <th class="px-4 py-2">Break In</th>
                                        <th class="px-4 py-2">Break out</th>
                                        <th class="px-4 py-2">Time Out</th>
                                        <th class="px-4 py-2">Status</th>
                                    </tr>
                                </thead>
                                <tbody formArrayName="time">
                                    <tr *ngFor="let shift of Time.controls; let i = index" [formGroupName]="i">
                                        <td class="px-4 py-2">{{ shift.value.fullname }}</td>
                                        <td class="px-4 py-2">
                                            <input matInput type="time" formControlName="time_in" [readonly]="shift.value.active === false"
                                            class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                        </td>
                                       
                                        <td class="px-4 py-2">
                                            <input matInput type="time" formControlName="break_in" [readonly]="shift.value.active === false"
                                            class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                        </td>
                                        <td class="px-4 py-2">
                                            <input matInput type="time" formControlName="break_out" [readonly]="shift.value.active === false"
                                            class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                        </td>
                                        <td class="px-4 py-2">
                                            <input matInput type="time" formControlName="time_out" [readonly]="shift.value.active === false"
                                            class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                        </td>
                                        <td class="px-4 py-2 text-start">
                                            <div class="relative inline-flex items-center cursor-pointer" (click)="shift.patchValue({ active: !shift.value.active })">
                                                <span class="mr-3 text-sm font-medium"
                                                    [ngClass]="{'text-green-500': shift.value.active, 'text-red-400': !shift.value.active}">
                                                    {{ shift.value.active ? true : false }}
                                                </span>
                                                <div class="relative rounded-xl inline-block w-10 h-6 transition duration-200 ease-in"
                                                    [ngClass]="{'bg-green-500': shift.value.active, 'bg-gray-300': !shift.value.active}">
                                                    <span class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform transform"
                                                        [ngClass]="{'translate-x-4': shift.value.active, 'translate-x-0': !shift.value.active}"></span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Save
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>