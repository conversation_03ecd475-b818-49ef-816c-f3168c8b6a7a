import { Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import {MatRadioModule} from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FloorService } from '../page.service';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';

@Component({
    selector: 'app-floor-dialog',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        MatSlideToggleModule,
        ImageUploadComponent,
    ]
})
export class DialogForm implements OnInit {

    form: FormGroup;
    branch: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: any = {};
    addForm: FormGroup;
    roles: any[] = [
        { id: 2, name: 'Admin'},
        { id: 3, name: 'Supervisor'},
        { id: 4, name: 'Cashier'},
     ];
    constructor(
        private dialogRef: MatDialogRef<DialogForm>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        private fuseConfirmationService: FuseConfirmationService,
        private _service: FloorService,
        private toastr: ToastrService,
        private imageUploadService: ImageUploadService,
      )
    {
        this.form = this.FormBuilder.group({
            id: null,
            name: null,
            code: null,
            pic: null,
            picUrl: null,
            active: 1,
         });

         console.log(this.data.value);

    }

    ngOnInit(): void {

         if (this.data.type === 'EDIT') {
            this.form.patchValue({
                ...this.data.value,
                pic: this.getPathFromUrl(this.data.value?.pic),
                picUrl: this.data.value?.pic
            })
        } else {
            console.log('New');
        }
    }

    Submit() {
        let formValue = this.form.value
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to save data?",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {

                  const { picUrl, ...body } = formValue

                    if (this.data.type === 'NEW') {
                        this._service.create(body).subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message[0])
                            },
                            complete: () => {
                                this.toastr.success('Scuccess')
                                this.dialogRef.close(true)
                            },
                        });
                    } else {
                        this._service.update(this.data.value.id ,body).subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message[0])
                            },
                            complete: () => {
                                this.toastr.success('Successed')
                                this.dialogRef.close(true)
                            },
                        });
                    }
                }
            }
        )
    }

    onClose() {
        this.dialogRef.close()
    }

    uploadSuccess(event): void {
      this.imageUploadService.upload(event).subscribe({
        next: (resp: any) => {
          // console.log(resp);

          this.form.patchValue({
            pic: resp.path
          });
          // console.log(this.form.value);

        },
        error: (err) => {
          this.toastr.error(JSON.stringify(err))
        },
      })
    }

    getPathFromUrl(url) {
      try {
          let path = new URL(url).pathname;
          return path.replace(/^\/+/, ''); // ลบ `/` ด้านหน้าสุดออก
      } catch (e) {
          console.error("Invalid URL", e);
          return null;
      }
  }
}
