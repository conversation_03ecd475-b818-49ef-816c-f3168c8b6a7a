<div class="md:max-w-lg" >
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">Create New Leave Type</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">Edit Leave Type</h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Code</mat-label>
                            <input matInput [placeholder]="''" formControlName="code">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                            <mat-label>Name</mat-label>
                            <input matInput [placeholder]="''" formControlName="name">
                        </mat-form-field>
                    </div>
                    <div class="md:w-full">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                            <mat-label >Sex</mat-label>
                            <mat-select [formControlName]="'sex'">
                                <mat-option *ngFor="let item of sex;" value="{{item}}">
                                    {{item}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="w-full mb-4">
                        <mat-slide-toggle [formControlName]="'show'" [color]="'primary'">Default</mat-slide-toggle>
                    </div>
                    <div class="w-full mb-4" *ngIf="this.data.type === 'EDIT'">
                        <mat-slide-toggle [formControlName]="'active'" [color]="'primary'">Show</mat-slide-toggle>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
</div>
