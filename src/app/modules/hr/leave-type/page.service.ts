import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { filter, toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { data } from './mock-data';
import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class LeaveTypeService {

  private _categories: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _roles: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _branch: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  get categories$() {
    return this._categories.asObservable();
  }

  constructor(private http: HttpClient) { }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/leave-type/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }


  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/leave-type`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/leave-type/` + id, data)
  }
  getData() {
    return this.http.get(`${environment.apiUrl}/api/leave-type`).pipe(
      tap((resp: any) => {
        this._branch.next(resp.data);
      }),
    )
  }
  getLeaveTypeById(id:any) {
    return this.http.get(`${environment.apiUrl}/api/leave-type/`+ id).pipe(
      tap((resp: any) => {
        this._branch.next(resp.data);
      }),
    )
  }


  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/leave-type/' + id)
  }
}
