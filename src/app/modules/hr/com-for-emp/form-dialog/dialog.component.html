<div class="md:max-w-lg">
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'NEW'">Create Company For
        Employee</h1>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4" *ngIf="this.data?.type === 'EDIT'">Edit Company For Employee
    </h1>
    <div mat-dialog-content class="overflow-y-auto md:max-h-180">
        <form [formGroup]="form">
            <div class="flex-auto">
                <div class="flex flex-col md:flex mb-6">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Code</mat-label>
                        <input matInput [placeholder]="''" formControlName="initial">
                    </mat-form-field>
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Name</mat-label>
                        <input matInput [placeholder]="''" formControlName="name">
                    </mat-form-field>
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label>Address</mat-label>
                        <textarea matInput rows="5" formControlName="address"></textarea>
                    </mat-form-field>
                    @if (this.data?.type === 'EDIT') {
                    <mat-slide-toggle [formControlName]="'active'" [color]="'primary'">Active</mat-slide-toggle>
                    }
                </div>
            </div>
        </form>
    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
</div>