import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { toUpper } from 'lodash';
import { map, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ComForEmpService {

  constructor(private readonly http: HttpClient) { }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/company-for-employee/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }


  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/company-for-employee`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/company-for-employee/` + id, data)
  }
  getData() {
    return this.http.get(`${environment.apiUrl}/api/company-for-employee`).pipe(
      tap((resp: any) => {
      }),
    )
  }
  getLeaveTypeById(id:any) {
    return this.http.get(`${environment.apiUrl}/api/company-for-employee/`+ id).pipe(
      tap((resp: any) => {
      }),
    )
  }


  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/company-for-employee/' + id)
  }
}
