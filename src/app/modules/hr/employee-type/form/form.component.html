<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a class="whitespace-nowrap text-primary-500 cursor-pointer" 
                        >Employee list</a
                    >
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon
                        class="text-secondary icon-size-5"
                        [svgIcon]="'heroicons_mini:chevron-right'"
                    ></mat-icon>
                    <a class="ml-1 text-primary-500">Create new employee</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Create New Employee
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Edit Employee
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-6 sm:p-10">
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
                [formGroup]="form">
                
                <div class="flex flex-col md:flex-row gap-4 justify-center">
                    <!-- <div
                        class="flex flex-col  items-center justify-center md:w-1/3  m-3 p-5 bg-card rounded-2xl shadow-[0px_10px_20px_1px_] shadow-gray-200 overflow-hidden">
                        <ngx-dropzone (change)="onSelect($event)"
                            class="rounded-full flex items-center w-32 h-32 bg-slate-100 p-2"
                            accept="image/jpeg,image/jpg,image/png,image/gif" [maxFileSize]="310000">
                            <ngx-dropzone-label>

                                <mat-icon svgIcon="heroicons_solid:camera"></mat-icon><br>Upload
                                photo</ngx-dropzone-label>
                            <div *ngFor="let f of files">
                                <ngx-dropzone-image-preview [file]="f" [removable]="true"
                                    (removed)="onRemove(f)"></ngx-dropzone-image-preview>
                            </div>
                        </ngx-dropzone>
                        <p class="text-gray-400 text-center mt-10">
                            ไฟล์ที่รองรับ *.jpeg, *.jpg, *.png,<br>
                            สูงสุดไม่เกิน 3.1 MB
                        </p>
                    </div> -->
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>รหัสพนักงาน (Employee ID)</mat-label>
                                    <input matInput [formControlName]="'user_id'"
                                        [placeholder]="'กรุณาระบุรหัสพนักงาน'">
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>ชื่อ-นามสกุล (First name and Last name)</mat-label>
                                    <input matInput [formControlName]="'name'" [placeholder]="'กรุณาระบุชื่อ นามสกุล'">
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วัน เดือน ปีเกิด (Date of birth) </mat-label>
                                    <input matInput [matDatepicker]="birth_date" [formControlName]="'birth_date'" placeholder="วัน เดือน ปีเกิด">
                                    <mat-datepicker-toggle matSuffix [for]="birth_date"></mat-datepicker-toggle>
                                    <mat-datepicker #birth_date></mat-datepicker>
                                  </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>อีเมล (Email Address) </mat-label>
                                    <input id="email" matInput [formControlName]="'email'" />
                                    @if (form.get('email').hasError('required')) {
                                    <mat-error> Email is required </mat-error>
                                    }
                                    @if (form.get('email').hasError('email')) {
                                    <mat-error>
                                        Please enter a valid email.
                                    </mat-error>
                                    }
                                </mat-form-field>
                            </div>
                        </div>
                        
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >ประเภทพนักงาน (Employee Type)</mat-label>
                                    <mat-select [formControlName]="'employee_type_id'">
                                        <mat-option *ngFor="let item of this.branch;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >เวลาเข้า-ออกงานงาน (Roster)</mat-label>
                                    <mat-select [formControlName]="'employee_type_id'">
                                        <mat-option *ngFor="let item of this.branch;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <!-- <div class="-mx-3 md:flex mb-2 md:mb-0" *ngIf="!this.Id">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Password</mat-label>
                                    <input id="password" matInput type="password" [formControlName]="'password'"
                                        #passwordField />
                                    <button mat-icon-button type="button" (click)="
                            passwordField.type === 'password'
                                ? (passwordField.type = 'text')
                                : (passwordField.type = 'password')
                                " matSuffix>
                                        @if (passwordField.type === 'password') {
                                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                                        }
                                        @if (passwordField.type === 'text') {
                                        <mat-icon class="icon-size-5"
                                            [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                                        }
                                    </button>
                                    <mat-error> Password is required </mat-error>
                                </mat-form-field>
                            </div>
                        </div> -->
                        <div class="-mx-3 md:flex mb-2 md:mb-0" *ngIf="this.Id">
                            <div class="flex flex-col md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-label>สถานะการใช้งาน (Status)</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="status">
                                    <mat-radio-button value="Yes">เปิดการใช้งาน </mat-radio-button>
                                    <mat-radio-button value="No">ปิดการใช้งาน </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >ตำแหน่ง (Position)</mat-label>
                                    <mat-select [formControlName]="'branch_id'">
                                        <mat-option *ngFor="let item of this.branch;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >ขั้นตำแหน่ง (Level)</mat-label>
                                    <mat-select [formControlName]="'branch_id'">
                                        <mat-option *ngFor="let item of this.branch;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label>แผนก (Department)</mat-label>
                                    <mat-select [formControlName]="'permission_id'">
                                        <mat-option *ngFor="let item of this.permission;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >หัวหน้างาน หัวหน้างาน (Manager/Team Lead) </mat-label>
                                    <mat-select [formControlName]="'department_id'">
                                        <mat-option *ngFor="let item of this.department;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วันที่เริ่มงาน (Date Start)</mat-label>
                                    <input matInput [matDatepicker]="date_start" [formControlName]="'date_start'" placeholder="วัน เดือน ปีเกิด">
                                    <mat-datepicker-toggle matSuffix [for]="date_start"></mat-datepicker-toggle>
                                    <mat-datepicker #date_start></mat-datepicker>
                                  </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>วันที่ผ่านทดลองงาน (Date Passed Probation)</mat-label>
                                    <input matInput [matDatepicker]="date_passed_probation" [formControlName]="'date_passed_probation'" placeholder="วัน เดือน ปีเกิด">
                                    <mat-datepicker-toggle matSuffix [for]="date_passed_probation"></mat-datepicker-toggle>
                                    <mat-datepicker #date_passed_probation></mat-datepicker>
                                  </mat-form-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Save
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>