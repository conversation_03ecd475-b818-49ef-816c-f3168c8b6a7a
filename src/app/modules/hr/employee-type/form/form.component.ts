import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { EmployeeTypeService } from '../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { forkJoin, lastValueFrom } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';

@Component({
    selector: 'form-user',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;

    item1Data: any = [];
    item2Data: any = [];
    subCategory: any = [];
    itemSupplier: any = [];

    itemBrand: any = [];
    itemBrandModel: any = [];
    itemCC: any = [];
    itemColor: any = [];

    formData: FormGroup;
    formData2: FormGroup;

    files: File[] = [];
    warehouseData: any;
    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    department: any[] = [];
    position: any[] = [];
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: EmployeeTypeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        // this.branch = this._activatedRoute.snapshot.data.branch.data;
        // this.department = this._activatedRoute.snapshot.data.department.data;
        // this.position = this._activatedRoute.snapshot.data.position.data;
        // this.permission = this._activatedRoute.snapshot.data.permission.data;


    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            id: null,
            branch_id: null,
            permission_id: null,
            department_id: null,
            position_id: null,
            user_id: null,
            name: null,
            email: ['', [Validators.required, Validators.email]],
            password: null,
            image: null,
            signature: null,
            line_token: null,
            status: null,
            employee_type_id: null,
        })
        if (this.Id) {
            this._Service.getEmployeeTypeById(this.Id).subscribe((resp: any) => {
                this.itemData = resp.data
                this.form.patchValue({
                    ...this.itemData,
                    branch_id: this.itemData.branch_id.toString(),
                    permission_id: this.itemData.branch_id.toString(),
                    department_id: this.itemData.branch_id.toString(),
                    position_id: this.itemData.branch_id.toString(),
                    image: null,
                    signature: null,
                })
                console.log(this.form.value);
            })
        }






    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }



    onSelect(event: any) {
        this.files.push(...event.addedFiles);
        // Trigger Image Preview
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);
    }

    onRemove(event: any) {
        this.files.splice(this.files.indexOf(event), 1);
    }

    Submit(): void {
     
    
        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit data",
                message: "Do you want to save data ? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value
                    delete formValue.password
                    const formData = new FormData();
                    Object.entries(formValue).forEach(([key, value]: any[]) => {
                        formData.append(key, value !== null && value !== undefined && value !== '' ? value : '');
                    });
                    this.files.forEach((file) => {
                        formData.append('image', file ? file : '');
                    });
                    
                    this._Service.update(this.Id, formData).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['user/list']);
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please insert value",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add data",
                message: "Do you want save data",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value;
                    const formData = new FormData();
                    
                    Object.entries(formValue).forEach(([key, value]: any[]) => {
                        formData.append(key, value !== null && value !== undefined && value !== '' ? value : '');
                    });

                    this.files.forEach((file) => {
                        formData.append('image', file ? file : '');
                    });
                    
                    this._Service.create(formData).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['user/list']);

                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }


    backTo() {
        this._router.navigate(['user/list'])
    }

}
