import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { data } from './mock-data';
import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class CheckBoxService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _branch: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _department: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _position: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _permission: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }

  // getAll(dataTablesParameters: any): Observable<any> {

  //   return this.http
  //     .get<any>(`${environment.apiUrl}/api/department/datatables`, dataTablesParameters)
  //     .pipe(
  //       map((data: any) => {
  //         return data;
  //       }));
  // }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;
    

    return this.http.get(environment.apiUrl +'/api/department/datatables', {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }



  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/department`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/department/${id}`, data)
  }

  getDepartmentById(id:any) {
    return this.http.get(`${environment.apiUrl}/api/department/`+ id).pipe(
      tap((resp: any) => {
        this._user.next(resp.data);
      }),
    )
  }
  getDepartment() {
    return this.http.get(environment.apiUrl + '/api/department').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }

  getPosition() {
    return this.http.get(environment.apiUrl + '/api/get_position').pipe(
      tap((resp: any) => {
        this._position.next(resp);
      }),
    )
  }
  getBranch() {
    return this.http.get(environment.apiUrl + '/api/get_branch').pipe(
      tap((resp: any) => {
        this._branch.next(resp);
      }),
    )
  }
  getPermission() {
    return this.http.get(environment.apiUrl + '/api/get_permission').pipe(
      tap((resp: any) => {
        this._permission.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/department/' + id)
  }
}
