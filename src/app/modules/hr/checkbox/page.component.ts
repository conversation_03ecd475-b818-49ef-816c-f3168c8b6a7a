import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { CheckBoxService } from './page.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DialogForm } from './form-dialog/dialog.component';
import { GroupService } from '../group/page.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
@Component({
    selector: 'app-page-checkbox',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        MatCheckboxModule
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class CheckBoxComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('checkBox') checkBox: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    group: any
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    data: any;
    constructor(
        private _service: CheckBoxService,
        private _serviceGroup: GroupService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,

    )
    {
        this._serviceGroup.getGroup().subscribe((resp:any)=>{
            this.group = resp
        })

    }
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }
    rows: any[] = [];
    loadTable(): void {
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            ajax: (dataTablesParameters: any, callback) => {
                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        this.rows = resp.data.map(row => ({
                            ...row,
                            selected: false  // ค่าเริ่มต้นเป็น false
                          }));
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: this.rows
                        });
                    },error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: '<button type="button" (click)="toggleSelectAll($event)">Select All</button>',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.checkBox,
                    },
                    className: 'w-15 text-center h-10',
                    orderable: false
                },
                  {
                    title: 'No.',
                    data: 'no',
                    className: 'w-15 text-center'
                },
                {
                    title: 'Code',
                    data: 'code',
                    defaultContent: '-',
                    className: 'w-20 text-left'
                },
                {
                    title: 'Name',
                    data: 'name',
                    defaultContent: '-',
                    className: 'w-240 text-left'
                },
                {
                    title: 'Group',
                    data: 'group.name',
                    defaultContent: '-',
                    className: 'w-240 text-left'
                },
                {
                    title: 'Status',
                    data: 'active',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }
            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
          // Destroy the table first
          dtInstance.destroy();
          // Call the dtTrigger to rerender again
          this.dtTrigger.next(null);
        });
      }
    GetGroup() {
        this._serviceGroup.getGroup().subscribe((resp: any) =>{
            console.log(resp, 1);
        })
    }
    opendialogAdd() {

        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 200,
            exitAnimationDuration: 100,
            data: {
                type: 'NEW',
                value: '',
                group: this.group
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });
    }

    openDialogEdit(data: any) {
        const DialogRef = this.dialog.open(DialogForm, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 200,
            exitAnimationDuration: 100,
            data: {
                type: 'EDIT',
                value: data,
                group: this.group
            }
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.rerender();
            }
        });

    }



    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
             title: "Do you want to delete? ",
            message: "Please check the information. If the information is deleted, it cannot be retrieved.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('Successed');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
    onChangeType() {
        this.rerender()
    }
}
