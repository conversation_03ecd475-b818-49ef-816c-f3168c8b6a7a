<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between pb-2 my-5  gap-4 border-b-2 border-gray-300">
      <div class="px-4 md:px-0">
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Applicant data
        </h2>
      </div>
    </div>
    <!-- Add mat-tab-group with 9 tabs -->
    <!-- <form [formGroup]="form"> -->
    <mat-tab-group>
      <mat-tab label="PERSONAL DATA">
        <div class="w-full">
          <form [formGroup]="accountForm">

            <div class="mt-2 grid w-full gap-4 sm:grid-cols-12">
              <!-- Row 1: Full Name, Nickname -->
              <div class="sm:col-span-2">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Mr./Mrs./Miss</mat-label>
                  <!-- <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon> -->
                  <mat-select formControlName="prefixEn" placeholder="(Mr./Mrs./Miss)">
                    <mat-option value="mr">Mr.</mat-option>
                    <mat-option value="mrs">Mrs.</mat-option>
                    <mat-option value="miss">Miss</mat-option>
                    <mat-option value="dr">Dr.</mat-option>
                    <mat-option value="prof">Prof.</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="sm:col-span-6">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Full Name (English)</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'fullnameEn'" matInput placeholder="(Full Name English)" />
                </mat-form-field>
              </div>

              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Nickname (English)</mat-label>
                  <input [formControlName]="'nicknameEn'" matInput placeholder="(Nickname English)" />
                </mat-form-field>
              </div>

              <!-- Row 2: Thai Name, Thai Nickname -->
              <div class="sm:col-span-2">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Prefix (Thai)</mat-label>
                  <mat-select formControlName="prefixTh" placeholder="(Prefix Thai)">
                    <mat-option value="นาย">นาย</mat-option>
                    <mat-option value="นาง">นาง</mat-option>
                    <mat-option value="นางสาว">นางสาว</mat-option>
                    <mat-option value="ดร.">ดร.</mat-option>
                    <mat-option value="ศ.">ศ.</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <div class="sm:col-span-6">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Full Name (Thai)</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'fullnameTh'" matInput placeholder="(Full Name Thai)" />
                </mat-form-field>
              </div>

              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Nickname (Thai)</mat-label>
                  <input [formControlName]="'nicknameTh'" matInput placeholder="(Nickname Thai)" />
                </mat-form-field>
              </div>


              <!-- Row 3: Permanent Address -->
              <div class="sm:col-span-12">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Permanent Address</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:map-pin'" matPrefix></mat-icon>
                  <textarea matInput [formControlName]="'permanentAddress'" cdkTextareaAutosize [cdkAutosizeMinRows]="3"
                    placeholder="(Permanent Address)"></textarea>
                </mat-form-field>
              </div>


              <!-- Row 4: Present Address -->
              <!-- Present Address -->
              <div class="sm:col-span-12">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'" floatLabel="always">
                  <mat-label>Present Address</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:map-pin'" matPrefix></mat-icon>
                  <textarea matInput [formControlName]="'presentAddress'" cdkTextareaAutosize [cdkAutosizeMinRows]="3"
                    placeholder="(Present Address)"></textarea>
                </mat-form-field>
              </div>

              <!-- Row 5: Home Phone -->
              <div class="sm:col-span-6">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'" floatLabel="always">
                  <mat-label>Home Phone</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:phone'" matPrefix></mat-icon>
                  <input matInput type="tel" formControlName="homePhone" placeholder="(Home Phone)"
                    pattern="^[0-9]{9,10}$" maxlength="10" />
                </mat-form-field>
              </div>

              <!-- Mobile -->
              <div class="sm:col-span-6">
                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'" floatLabel="always">
                  <mat-label>Mobile</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:phone'" matPrefix></mat-icon>
                  <input matInput type="tel" formControlName="mobile" placeholder="(Mobile)" pattern="^[0-9]{9,10}$"
                    maxlength="10" />
                </mat-form-field>
              </div>



              <!-- Row 6: Email -->
              <div class="sm:col-span-12">
                <!-- <mat-form-field class="w-full" [subscriptSizing]="'dynamic'" floatLabel="always">
                          <mat-label>E-mail Address</mat-label>
                          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:envelope'" matPrefix></mat-icon>
                          <input matInput formControlName="email" placeholder="(E-mail Address)" />
                      </mat-form-field> -->

                <mat-form-field class="w-full" [subscriptSizing]="'dynamic'" floatLabel="always">
                  <mat-label>E-mail Address</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:envelope'" matPrefix></mat-icon>
                  <input matInput formControlName="email" placeholder="(E-mail Address)" />
                  <!-- <mat-hint>This email address was auto-filled from your login.</mat-hint> -->
                </mat-form-field>
              </div>


              <!-- Row 7: Date of Birth, Age, Place of Birth -->
              <!-- Date of Birth -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                  <mat-label>Date of Birth</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cake'" matPrefix></mat-icon>
                  <input matInput [matDatepicker]="dobPicker" formControlName="birthDate" placeholder="Date of Birth" />
                  <mat-datepicker-toggle matSuffix [for]="dobPicker"></mat-datepicker-toggle>
                  <mat-datepicker #dobPicker></mat-datepicker>
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input matInput type="number" formControlName="age" placeholder="(Age)" min="0" max="120" />
                </mat-form-field>
              </div>

              <!-- Place of Birth -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Place of Birth</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:map-pin'" matPrefix></mat-icon>
                  <input matInput formControlName="placeOfBirth" placeholder="(Place of Birth)" />
                </mat-form-field>
              </div>

              <!-- Height -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Height (cm)</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'height'" matPrefix></mat-icon>
                  <input matInput type="number" formControlName="height" placeholder="(Height cm)" min="0" max="300"
                    step="0.1" />
                </mat-form-field>
              </div>

              <!-- Weight -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Weight (kg)</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:scale'" matPrefix></mat-icon>
                  <input matInput type="number" formControlName="weight" placeholder="(Weight kg)" min="0" max="500"
                    step="0.1" />
                </mat-form-field>
              </div>


              <!-- Nationality -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Nationality</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:flag'" matPrefix></mat-icon>
                  <input matInput formControlName="nationality" placeholder="(Nationality)" />
                </mat-form-field>
              </div>

              <!-- I.D. Card No. -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>I.D. Card No.</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:identification'" matPrefix></mat-icon>
                  <input matInput formControlName="idCardNo" placeholder="(I.D. Card No.)" />
                </mat-form-field>
              </div>

              <!-- Issued by (ID Card) -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Issued by</mat-label>
                  <input matInput formControlName="idCardIssuedBy" placeholder="(Issued by)" />
                </mat-form-field>
              </div>

              <!-- Expiry Date (ID Card) -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                  <mat-label>Expiry Date</mat-label>
                  <input matInput [matDatepicker]="idCardPicker" formControlName="idCardExpiryDate"
                    placeholder="(mm/dd/yyyy)" />
                  <mat-datepicker-toggle matSuffix [for]="idCardPicker"></mat-datepicker-toggle>
                  <mat-datepicker #idCardPicker></mat-datepicker>
                </mat-form-field>
              </div>

              <!-- Passport No. -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Passport No.</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:document-text'" matPrefix></mat-icon>
                  <input matInput formControlName="passportNo" placeholder="(Passport No.)" />
                </mat-form-field>
              </div>

              <!-- Issued by (Passport) -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always">
                  <mat-label>Issued by</mat-label>
                  <input matInput formControlName="passportIssuedBy" placeholder="(Issued by)" />
                </mat-form-field>
              </div>

              <!-- Expiry Date (Passport) -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                  <mat-label>Expiry Date</mat-label>
                  <input matInput [matDatepicker]="passportPicker" formControlName="passportExpiryDate"
                    placeholder="(mm/dd/yyyy)" />
                  <mat-datepicker-toggle matSuffix [for]="passportPicker"></mat-datepicker-toggle>
                  <mat-datepicker #passportPicker></mat-datepicker>
                </mat-form-field>
              </div>

            </div>

            <!-- Divider -->
            <div class="mb-10 mt-11 border-t"></div>

            <!-- Actions -->
            <div class="flex items-center justify-end">
              <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
              <button class="ml-4" mat-flat-button type="button" [color]="'primary'" (click)="onSubmit_personaldata()">
                Save
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="FAMILY DETAILS">
        <div class="w-full">
          <!-- Form -->
          <form [formGroup]="formfamily">

            <div class=" mt-2 grid sm:grid-cols-12 gap-6">
              <!-- Marital Status -->
              <div class="sm:col-span-12">
                <label class="block text-lg font-medium text-gray-800">
                  Marital Status :
                </label>

                <!-- Divider -->
                <div class="my-4 border-t"></div>

                <div class="grid sm:grid-cols-4 gap-4 mt-4">
                  <!-- Single -->
                  <div class="flex items-center w-full">
                    <input type="radio" id="single" value="single" formControlName="maritalStatus"
                      class="w-6 h-6 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <label for="single" class="ml-3 text-lg font-medium text-gray-700">
                      Single
                      <span class="block text-sm text-gray-500">(โสด)</span>
                    </label>
                  </div>

                  <!-- Married -->
                  <div class="flex items-center w-full">
                    <input type="radio" id="married" value="married" formControlName="maritalStatus"
                      class="w-6 h-6 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <label for="married" class="ml-3 text-lg font-medium text-gray-700">
                      Married
                      <span class="block text-sm text-gray-500">(แต่งงาน)</span>
                    </label>
                  </div>

                  <!-- Divorced -->
                  <div class="flex items-center w-full">
                    <input type="radio" id="divorced" value="divorced" formControlName="maritalStatus"
                      class="w-6 h-6 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <label for="divorced" class="ml-3 text-lg font-medium text-gray-700">
                      Divorced
                      <span class="block text-sm text-gray-500">(หย่า)</span>
                    </label>
                  </div>

                  <!-- Separated -->
                  <div class="flex items-center w-full">
                    <input type="radio" id="separated" value="separated" formControlName="maritalStatus"
                      class="w-6 h-6 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <label for="separated" class="ml-3 text-lg font-medium text-gray-700">
                      Separated
                      <span class="block text-sm text-gray-500">(แยกกันอยู่)</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <!-- Divider -->
            <div class="my-5 border-t"></div>

            <div class="grid sm:grid-cols-12 mt-4 gap-4">
              <!-- Spouse's Name -->
              <div class="sm:col-span-12">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Spouse's Name (if married)</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'marriedName'" matInput placeholder="(Spouse's Name (if married))" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'marriedAge'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>

              <!-- Occupation -->
              <div class="sm:col-span-4">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Occupation</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:briefcase'" matPrefix></mat-icon>
                  <input [formControlName]="'marriedOccupation'" matInput placeholder="(Occupation)" />
                </mat-form-field>
              </div>
              <div class="sm:col-span-4 ">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Number of Children, if any</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user-group'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenQty'" matInput type="number"
                    placeholder="(Number of Children, if any)" min="0" max="6" />
                </mat-form-field>
              </div>
            </div>
            <div class="w-full text-center pt-6">
              <div class="text-secondary text-lg font-bold">
                Please specify children's name and age below !
              </div>
            </div>
            <div class="grid sm:grid-cols-12 pt-4 gap-4">
              <!-- 1 Full Name -->
              <div class="sm:col-span-9">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>1. Full Name : </mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenName1'" matInput placeholder="(Child's full name)" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-3">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenAge1'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>

              <!-- 2 Full Name -->
              <div class="sm:col-span-9">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>2. Full Name : </mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenName2'" matInput placeholder="(Child's full name)" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-3">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenAge2'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>
              <!-- 3 Full Name -->
              <div class="sm:col-span-9">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>3. Full Name : </mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenName3'" matInput placeholder="(Child's full name)" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-3">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenAge3'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>
              <!-- 4 Full Name -->
              <div class="sm:col-span-9">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>4. Full Name : </mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenName4'" matInput placeholder="(Child's full name)" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-3">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenAge4'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>
              <!-- 5 Full Name -->
              <div class="sm:col-span-9">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>5. Full Name :</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenName5'" matInput placeholder="(Child's full name)" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-3">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenAge5'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>
              <!-- 6 Full Name -->
              <div class="sm:col-span-9">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>6. Full Name : </mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenName6'" matInput placeholder="(Child's full name)" />
                </mat-form-field>
              </div>

              <!-- Age -->
              <div class="sm:col-span-3">
                <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                  <mat-label>Age</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                  <input [formControlName]="'childrenAge6'" matInput type="number" placeholder="(Age)" min="0"
                    max="120" />
                </mat-form-field>
              </div>
            </div>

            <div class="w-full text-center mt-6">
              <div class="text-secondary text-lg font-bold">
                Particular of Immediate Family
              </div>
            </div>
            <div formArrayName="applicantFamily" class="my-6 space-y-8 bg">
              <div *ngFor="let item of Family.controls; let i = index" [formGroupName]="i"
                class="space-y-4 border bg-white border-gray-300 rounded shadow p-8">
                <div class="grid sm:grid-cols-12 mt-4 gap-4">
                  <!-- Spouse's Name -->
                  <div class="sm:col-span-8">
                    <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                      <mat-label>Name</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                      <input [formControlName]="'name'" matInput placeholder="(Name)" />
                    </mat-form-field>
                  </div>
                  <div class="sm:col-span-4">
                    <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                      <mat-label>Relationship</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                      <input [formControlName]="'relationship'" matInput placeholder="(Relationship)" />
                    </mat-form-field>
                  </div>

                  <!-- Age -->
                  <div class="sm:col-span-4">
                    <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                      <mat-label>Age</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                      <input [formControlName]="'age'" matInput type="number" placeholder="(Age)" min="0" max="120" />
                    </mat-form-field>
                  </div>

                  <!-- Occupation -->
                  <div class="sm:col-span-4">
                    <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                      <mat-label>Occupation</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:briefcase'" matPrefix></mat-icon>
                      <input [formControlName]="'occupation'" matInput placeholder="(Occupation)" />
                    </mat-form-field>
                  </div>
                  <div class="sm:col-span-4 ">
                    <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                      <mat-label>Place Of Work</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user-group'" matPrefix></mat-icon>
                      <input [formControlName]="'placeOfWork'" matInput placeholder="(Place Of Work)" min="0"
                        max="120" />
                    </mat-form-field>

                  </div>
                </div>
                <div class="flex items-center justify-start">
                  <button mat-stroked-button color="warn" (click)="removeFamily(i)">Remove</button>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-end mb-10">
              <button mat-raised-button color="primary" (click)="addFamily()">
                + Family
              </button>
            </div>

            <!-- Divider -->
            <div class="my-10 border-t"></div>

            <!-- Actions -->
            <div class="flex items-center justify-end">
              <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
              <button class="ml-4" mat-flat-button type="button" [color]="'primary'" (click)="onSubmit_familydetail()">
                Save
              </button>
            </div>
          </form>
        </div>

      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="EDUCATIONAL DETAILS">
        <form class="w-full">

          <!-- Header -->
          <div class="font-bold text-lg">
            List all educational institutes attended
          </div>

          <!-- Section -->
          <div class="w-full">
            <div [formGroup]="formeducational">

              <div formArrayName="applicantEducations" class="my-6 space-y-8">
                <div *ngFor="let Institution of Institution.controls; let i = index" [formGroupName]="i"
                  class="space-y-4 border bg-white border-gray-300 rounded shadow p-8">
                  <div class="grid w-full gap-4 sm:grid-cols-12">
                    <div class="sm:col-span-12">
                      <!-- Dropdown -->
                      <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                        <mat-label class="text-lg mb-4">Select education level</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:academic-cap'" matPrefix>
                        </mat-icon>
                        <mat-select formControlName="level" placeholder="(Select education level)">
                          <mat-option value="primary">Primary/Secondary</mat-option>
                          <mat-option value="pre_university">Pre/University</mat-option>
                          <mat-option value="university">University</mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>

                  <!-- Shared Form Fields -->
                  <div class="grid w-full gap-4 sm:grid-cols-12">
                    <div class="mt-4 sm:col-span-8">
                      <mat-form-field class="w-full" floatLabel="always">
                        <mat-label>Institute Name & Location</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:map-pin'" matPrefix>
                        </mat-icon>
                        <input formControlName="name" matInput placeholder="(Institute Name & Location)" />
                      </mat-form-field>
                    </div>
                    <div class="mt-4 sm:col-span-2">
                      <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                        <mat-label>From</mat-label>
                        <input matInput [matDatepicker]="fromPicker1" formControlName="from" placeholder="(From)" />
                        <mat-datepicker-toggle matSuffix [for]="fromPicker1"></mat-datepicker-toggle>
                        <mat-datepicker #fromPicker1></mat-datepicker>
                      </mat-form-field>
                    </div>
                    <div class="mt-4 sm:col-span-2">
                      <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                        <mat-label>To</mat-label>
                        <input matInput [matDatepicker]="toPicker1" formControlName="to" placeholder="(To)" />
                        <mat-datepicker-toggle matSuffix [for]="toPicker1"></mat-datepicker-toggle>
                        <mat-datepicker #toPicker1></mat-datepicker>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="grid w-full gap-4 sm:grid-cols-12">
                    <div class="sm:col-span-8">
                      <mat-form-field class="w-full" floatLabel="always">
                        <mat-label>Major</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:academic-cap'" matPrefix>
                        </mat-icon>
                        <input formControlName="major" matInput placeholder="(Major)" />
                      </mat-form-field>
                    </div>
                    <div class="sm:col-span-4">
                      <mat-form-field class="w-full" floatLabel="always">
                        <mat-label>Degree Obtained</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:document-text'" matPrefix>
                        </mat-icon>
                        <input formControlName="degree" matInput placeholder="(Degree Obtained)" type="number" min="0"
                          max="4" step="0.01" />
                      </mat-form-field>
                    </div>
                    <!-- ปุ่มลบ -->
                    <div>
                      <button mat-stroked-button color="warn" (click)="removeInstitution(i)">Remove</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- ปุ่มเพิ่ม -->
              <div class="flex items-center justify-end mb-10">
                <button mat-raised-button color="primary" (click)="addInstitution()">
                  + Add Institution
                </button>
              </div>

              <!-- Actions -->
              <div class="flex items-center justify-end">
                <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
                <button class="ml-4" mat-flat-button type="button" color="primary"
                  (click)="onSubmit_educationnaldetail()">
                  Save
                </button>
              </div>
            </div>
          </div>
        </form>
      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="EMPLOYMENT HISTORY">
        <!-- Header -->
        <div class="font-bold text-lg">
          Employment History in reverse order with present job fust
          <!-- <span class="block text-sm">(ประวัติการทำงานทั้งหมด เริ่มตั้งแต่ปัจจุบันยัอนหลังลงไป)</span> -->
        </div>

        <!-- Reference Form -->
        <div [formGroup]="formhistory">
          <div formArrayName="applicantWork" class="my-6 space-y-8">
            <div *ngFor="let item of employment.controls; let i = index" [formGroupName]="i"
              class="space-y-4 border bg-white border-gray-300 rounded shadow p-8">
              <div class="grid gap-6 sm:grid-cols-12">
                <!-- ชื่อบริษัท -->
                <div class="sm:col-span-6">
                  <mat-form-field class="w-full" floatLabel="always">
                    <mat-label>Name</mat-label>
                    <mat-icon matPrefix class="icon-size-5">business</mat-icon>
                    <input matInput formControlName="name" placeholder="(Employer's Name)" />
                  </mat-form-field>
                </div>

                <!-- ตำแหน่ง -->
                <div class="sm:col-span-6">
                  <mat-form-field class="w-full" floatLabel="always">
                    <mat-label>Position</mat-label>
                    <mat-icon matPrefix class="icon-size-5">work</mat-icon>
                    <input matInput formControlName="position" placeholder="(Position)" />
                  </mat-form-field>
                </div>

                <!-- ระยะเวลา -->
                <div class="sm:col-span-3">
                  <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                    <mat-label>From</mat-label>
                    <input matInput [matDatepicker]="fromPicker2" formControlName="from" placeholder="(Form)" />
                    <mat-datepicker-toggle matSuffix [for]="fromPicker2"></mat-datepicker-toggle>
                    <mat-datepicker #fromPicker2></mat-datepicker>
                  </mat-form-field>
                </div>
                <div class="sm:col-span-3">
                  <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                    <mat-label>To</mat-label>
                    <input matInput [matDatepicker]="toPicker2" formControlName="to" placeholder="(To)" />
                    <mat-datepicker-toggle matSuffix [for]="toPicker2"></mat-datepicker-toggle>
                    <mat-datepicker #toPicker2></mat-datepicker>
                  </mat-form-field>
                </div>

                <!-- เงินเดือน -->
                <div class="sm:col-span-6">
                  <mat-form-field class="w-full" floatLabel="always">
                    <mat-label>Salary</mat-label>
                    <mat-icon matPrefix class="icon-size-5">attach_money</mat-icon>
                    <input matInput formControlName="salary" type="number" step="0.01" min="0" placeholder="(Salary)" />
                  </mat-form-field>
                </div>

                <!-- เหตุผลที่ออก -->
                <div class="sm:col-span-12">
                  <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                    <mat-label>Reason for leaving</mat-label>
                    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_outline:question-mark-circle'"
                      matPrefix></mat-icon>
                    <textarea matInput formControlName="reasonLeaving" cdkTextareaAutosize [cdkAutosizeMinRows]="3"
                      placeholder="(Reason for leaving)"></textarea>
                  </mat-form-field>

                </div>
              </div>
              <div class="flex items-center justify-start">
                <button mat-stroked-button color="warn" (click)="removeEmployment(i)">Remove</button>
              </div>
            </div>

            <div class="flex items-center justify-end mb-10">
              <button mat-raised-button color="primary" (click)="addEmployment()">
                + Employment
              </button>
            </div>


            <div class="mb-10 mt-11 border-t"></div>
            <!-- Actions -->
            <div class="flex items-center justify-end">
              <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
              <button class="ml-4" mat-flat-button type="button" [color]="'primary'"
                (click)="onSubmit_employmenthistory()">
                Save
              </button>
            </div>
          </div>
        </div>
      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="TRAINING HISTORY">
        <div class="w-full">
          <!-- Divider -->
          <div class="my-4 border-t"></div>

          <!-- Section -->
          <div class="w-full">
            <div [formGroup]="formtraining">
              <!-- Primary/Secondary -->
              <div class="w-full">
                <div class="text-xl">Training History</div>
              </div>
              <div formArrayName="applicantTrain" class="my-6 space-y-8">
                <div *ngFor="let reference of Training.controls; let i = index" [formGroupName]="i"
                  class="space-y-4 border bg-white border-gray-300 rounded shadow p-8">
                  <div class="grid w-full gap-4 sm:grid-cols-12">
                    <div class="sm:col-span-8">
                      <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                        <mat-label>Course Title</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:map-pin'" matPrefix>
                        </mat-icon>
                        <input [formControlName]="'name'" matInput placeholder="(Course Title)" />
                      </mat-form-field>
                    </div>
                    <div class="sm:col-span-4">
                      <mat-form-field class="w-full" appearance="fill" floatLabel="always">
                        <mat-label>Month / Year</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                        <input matInput [matDatepicker]="toPicker1" formControlName="monthYear"
                          placeholder="(Month / Year)" />
                        <mat-datepicker-toggle matSuffix [for]="toPicker1"></mat-datepicker-toggle>
                        <mat-datepicker #toPicker1></mat-datepicker>
                      </mat-form-field>
                    </div>
                  </div>

                  <div class="grid w-full gap-4 sm:grid-cols-12">
                    <div class="sm:col-span-12">
                      <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                        <mat-label>Certificate</mat-label>
                        <mat-icon class="icon-size-5" matPrefix>verified</mat-icon>
                        <input [formControlName]="'certificate'" matInput placeholder="(Certificate)" />
                      </mat-form-field>
                    </div>
                    <div class="sm:col-span-12">
                      <mat-form-field class="w-full" floatLabel="always" [subscriptSizing]="'dynamic'">
                        <mat-label>Details</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:document-text'" matPrefix>
                        </mat-icon>
                        <textarea matInput [formControlName]="'detail'" cdkTextareaAutosize [cdkAutosizeMinRows]="3"
                          placeholder="(Details)"></textarea>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="flex items-center justify-start">
                    <button mat-stroked-button color="warn" (click)="removeTraining(i)">Remove</button>
                  </div>
                </div>

                <div class="flex items-center justify-end mb-10">
                  <button mat-raised-button color="primary" (click)="addTraining()">
                    + Training
                  </button>
                </div>


                <!-- Actions -->
                <div class="mb-10 mt-11 border-t"></div>
                <div class="flex items-center justify-end">
                  <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
                  <button class="ml-4" mat-flat-button type="button" [color]="'primary'"
                    (click)="onSubmit_traininghistory()">
                    Save
                  </button>
                </div>
              </div>
            </div>

          </div>
        </div>
      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="BASIC SKILLS">
        <div class="w-full max-w-full">
          <!-- Form -->
          <form [formGroup]="formskill">

            <div class=" mt-12 grid grid-cols-1 sm:grid-cols-12 gap-6">
              <!-- Marital Status -->
              <div class="sm:col-span-12">

                <div class=" text-lg font-medium text-start mb-4">
                  Language Skills
                </div>

                <!-- Table -->
                <div class="overflow-x-auto">
                  <table class="w-full border border-gray-300">
                    <!-- Table Header -->
                    <thead>
                      <tr class="bg-gray-200 text-gray-800">
                        <th rowspan="2" class="border border-gray-300 p-2 text-left">Language</th>
                        <th colspan="5" class="border border-gray-300 p-2 text-center">Proficiency</th>
                      </tr>

                    </thead>

                    <!-- Table Body -->
                    <tbody>
                      <!-- English -->
                      <tr class="bg-white text-gray-700">
                        <td class="border border-gray-300 p-2">English</td>
                        <td colspan="5" class="border border-gray-300 p-2 text-center">
                          <mat-radio-group [formControlName]="'skillLevel1'"
                            class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0">
                            <mat-radio-button value="excellent"
                              class="flex items-stat mx-2">Excellent</mat-radio-button>
                            <mat-radio-button value="veryGood" class="flex items-stat mx-2">VeryGood</mat-radio-button>
                            <mat-radio-button value="good" class="flex items-stat mx-2">Good</mat-radio-button>
                            <mat-radio-button value="fair" class="flex items-stat mx-2">Fair</mat-radio-button>
                            <mat-radio-button value="poor" class="flex items-stat mx-2">Poor</mat-radio-button>
                          </mat-radio-group>
                        </td>
                      </tr>

                      <!-- Other Languages -->
                      <tr class="bg-white text-gray-700">
                        <td class="border border-gray-300 p-2">
                          Others, please specify below:
                          <mat-form-field class="w-full mt-2">
                            <mat-label>Specify Language</mat-label>
                            <input matInput placeholder="Specify language" formControlName="skillLanguage2" />
                          </mat-form-field>
                        </td>
                        <td colspan="5" class="border border-gray-300 p-2 text-center">
                          <mat-radio-group [formControlName]="'skillLevel2'"
                            class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0">
                            <mat-radio-button value="excellent"
                              class="flex items-stat mx-2">Excellent</mat-radio-button>
                            <mat-radio-button value="veryGood" class="flex items-stat mx-2">VeryGood</mat-radio-button>
                            <mat-radio-button value="good" class="flex items-stat mx-2">Good</mat-radio-button>
                            <mat-radio-button value="fair" class="flex items-stat mx-2">Fair</mat-radio-button>
                            <mat-radio-button value="poor" class="flex items-stat mx-2">Poor</mat-radio-button>
                          </mat-radio-group>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- Additional Fields -->
                <div class="mt-6 space-y-4">
                  <!-- Typing Speed -->
                  <div
                    class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <!-- Label -->
                    <label class="block font-medium w-full sm:w-1/3">
                      Typing Speed :
                    </label>

                    <!-- Input Fields -->
                    <div class="flex flex-row flex-1 items-center sm:space-y-0 space-x-4 sm:space-x-4">
                      <mat-form-field appearance="fill" class="w-full sm:w-1/2" floatLabel="always">
                        <mat-label>English (w.p.m.)</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                        <input matInput type="text" formControlName="typingEnSpeed" placeholder="w.p.m." />
                      </mat-form-field>
                      <mat-form-field appearance="fill" class="w-full sm:w-1/2" floatLabel="always">
                        <mat-label>Thai (w.p.m.)</mat-label>
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:clock'" matPrefix></mat-icon>
                        <input matInput type="text" formControlName="typingThSpeed" placeholder="w.p.m." />
                      </mat-form-field>
                    </div>
                  </div>


                  <!-- Computer Skills -->
                  <div>
                    <mat-form-field appearance="fill" class="w-full" floatLabel="always">
                      <mat-label>Computer (Indicate Software)</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:computer-desktop'" matPrefix></mat-icon>
                      <input matInput type="text" formControlName="computerSkill" placeholder="Specify software..." />
                    </mat-form-field>
                  </div>

                  <!-- Other Skills -->
                  <div>
                    <mat-form-field appearance="fill" class="w-full" floatLabel="always">
                      <mat-label>Other Skills and Knowledge</mat-label>
                      <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:light-bulb'" matPrefix></mat-icon>
                      <textarea matInput rows="4" formControlName="otherSkill"
                        placeholder="(Please specify)"></textarea>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end">
              <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
              <button class="ml-4" mat-flat-button type="button" [color]="'primary'" (click)="onSubmit_basicskill()">
                Save
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="REFERENCES">
        <!-- Header -->
        <div class="font-bold text-lg">
          References excluding family members or relatives
        </div>

        <!-- Reference Form -->
        <form [formGroup]="formreference">

          <div formArrayName="references" class="my-6 space-y-8">
            <div *ngFor="let reference of references.controls; let i = index" [formGroupName]="i"
              class="space-y-4 bg-white border border-gray-300 rounded shadow p-8">
              <mat-form-field appearance="fill" class="w-full" floatLabel="always">
                <mat-label>Name </mat-label>
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                <input matInput placeholder="Specify name" formControlName="name" />
              </mat-form-field>

              <mat-form-field appearance="fill" class="w-full" floatLabel="always">
                <mat-label>Company's Name & Address </mat-label>
                <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:map-pin'" matPrefix></mat-icon>
                <textarea matInput rows="3" placeholder="Specify company name and address"
                  formControlName="companyName"></textarea>
              </mat-form-field>

              <mat-form-field appearance="fill" class="w-full" floatLabel="always">
                <mat-label>Tel.</mat-label>
                <mat-icon class="icon-size-4.5" [svgIcon]="'heroicons_solid:phone'" matPrefix></mat-icon>
                <input matInput placeholder="Specify telephone number" formControlName="tel" />
              </mat-form-field>

              <button mat-stroked-button color="warn" (click)="removeReference(i)">Remove</button>

            </div>
          </div>

          <!-- ปุ่มเพิ่ม/ลบ -->
          <div class="flex items-center justify-end mb-10">

            <button *ngIf="references.length < 3" mat-raised-button color="primary" (click)="addReference()">
              + Reference
            </button>
          </div>

          <!-- Actions -->
          <div class="flex items-center justify-end">
            <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
            <button class="ml-4" mat-flat-button type="button" [color]="'primary'"
              (click)="onSubmit_references()">Save</button>
          </div>
        </form>
      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="OTHER QUESTIONS">
        <div class="mt-8 space-y-6" [formGroup]="formQuestion">
          <div formArrayName="applicantQuestion">
            <div *ngFor="let questionCtrl of applicantQuestion.controls; let i = index" [formGroupName]="i"
              class="p-4 border border-gray-300 rounded-lg shadow-sm bg-white mb-4">
              <div class="mb-2 font-semibold text-lg">
                {{ i + 1 }}. <span class="ml-4">{{ questions[i]?.name }}</span>
              </div>

              <!-- เลือกตัวเลือกได้เพียง 1 ตัวต่อคำถาม -->
              <mat-radio-group formControlName="choiceId" class="flex flex-col sm:flex-row gap-4" color="primary">
                <mat-radio-button *ngFor="let choice of questions[i]?.choices" class="mb-2" [value]="choice.id">
                  {{ choice.name }}
                </mat-radio-button>
              </mat-radio-group>

              <!-- ช่องกรอกข้อมูลเพิ่มเติม ถ้าเลือก Yes (เคย) -->
              <div class="mt-3 ml-7">
                <mat-form-field class="w-full sm:w-2/3">
                  <mat-label>If yes, please identify</mat-label>
                  <input matInput placeholder="Specify details" formControlName="remark">
                </mat-form-field>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center justify-end">
            <button mat-stroked-button type="button">Cancel</button>
            <button class="ml-4" mat-flat-button type="button" [color]="'primary'" (click)="onSubmit_questions()">
              Save
            </button>
          </div>
        </div>

      </mat-tab>

      <!-- ================================================================================================ -->

      <mat-tab label="EMERGENCY CONTACT">
        <!-- Emergency Contact Form -->
        <div class="mt-2 space-y-6">
          <!-- Section: Emergency Contacts -->

          <div class="font-bold text-lg">
            Names of the persons to notify in case of emergency.
          </div>
          <form [formGroup]="formemergency">

            <div formArrayName="emergency">
              <div *ngFor="let emergencys of emergency.controls; let i = index" [formGroupName]="i"
                class="grid gap-4 mt-4 bg-white border border-gray-300 rounded shadow p-8">
                <!-- Name -->
                <mat-form-field appearance="fill" class="col-span-1 w-full" floatLabel="always">
                  <mat-label>Name</mat-label>
                  <mat-icon class="icon-size-4.5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                  <input matInput placeholder="Specify name" formControlName="name" />
                </mat-form-field>

                <!-- Relationship -->
                <mat-form-field appearance="fill" class="col-span-1 w-full" floatLabel="always">
                  <mat-label>Relationship</mat-label>
                  <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:user-group'" matPrefix></mat-icon>
                  <input matInput placeholder="Specify relationship" formControlName="relationship" />
                </mat-form-field>

                <!-- Tel. Home -->
                <mat-form-field appearance="fill" class="col-span-1 w-full" floatLabel="always">
                  <mat-label>Tel. Home </mat-label>
                  <mat-icon class="icon-size-4" [svgIcon]="'heroicons_solid:phone'" matPrefix></mat-icon>
                  <input matInput placeholder="Specify home number" formControlName="telHome" />
                </mat-form-field>

                <!-- Tel. Mobile -->
                <mat-form-field appearance="fill" class="col-span-1 w-full" floatLabel="always">
                  <mat-label>Tel. Mobile</mat-label>
                  <mat-icon class="icon-size-4" [svgIcon]="'heroicons_solid:phone'" matPrefix></mat-icon>
                  <input matInput placeholder="Specify mobile number" formControlName="tel" />
                </mat-form-field>

                <div class="flex items-center justify-start">
                  <button mat-stroked-button color="warn" (click)="removeEmergency(i)">Remove</button>
                </div>

              </div>
            </div>

            <div class="flex items-center justify-end mt-4 mb-10">
              <button *ngIf="emergency.length < 3" mat-raised-button color="primary" (click)="addEmergency()">
                + Reference
              </button>
            </div>

            <!-- Section: Friends or Relatives -->
            <div class="mt-10">
              <div class="font-bold text-lg">
                Names of your friends or your relatives who currently/previously work(ed) with Meinhardt.
              </div>
              <mat-form-field appearance="fill" class="w-full mt-2" floatLabel="always">
                <mat-label>Specify names</mat-label>
                <mat-icon class="icon-size-4.5" [svgIcon]="'heroicons_solid:user'" matPrefix></mat-icon>
                <textarea matInput rows="3" placeholder="Specify names..." formControlName="nameOfYouFriend"></textarea>
              </mat-form-field>
            </div>

            <!-- Section: Additional Information -->
            <div class="mt-5">
              <div class="font-bold text-lg">
                Any additional information you wish to give in support of this application:
              </div>
              <mat-form-field appearance="fill" class="w-full mt-2" floatLabel="always">
                <mat-label>Additional Information</mat-label>
                <mat-icon class="icon-size-4.5" [svgIcon]="'heroicons_solid:document-text'" matPrefix></mat-icon>
                <textarea matInput rows="3" placeholder="Specify additional information..."
                  formControlName="anyAdditional"></textarea>
              </mat-form-field>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end mt-6">
              <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
              <button class="ml-4" mat-flat-button type="button" [color]="'primary'" (click)="onSubmit_emergency()">
                Save
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- ======================================================================== -->
      <mat-tab label="RELATED FILES">
        <div class="w-full">
          <form [formGroup]="formFile">
            <div formArrayName="applicantFiles" class="my-6 space-y-8">
              <div *ngFor="let fileGroup of files.controls; let i = index" [formGroupName]="i"
                class="space-y-4 border bg-white border-gray-300 rounded shadow p-8">
                <div class="w-full gap-4">
                  <div class="w-full flex flex-row">
                    <mat-form-field appearance="fill" class="col-span-1 w-full" floatLabel="always">
                      <mat-label>Name</mat-label>
                      <mat-select formControlName="name" placeholder="File Name">
                        <mat-option *ngFor="let file of namefile" [value]="file.name">{{ file.name }}</mat-option>
                      </mat-select>
                    </mat-form-field>
                    <div class="items-center justify-center flex mx-2" *ngIf="fileGroup.get('file')?.value">
                      <button mat-flat-button color="primary" (click)="opendialogfile(fileGroup.get('file')?.value)">
                          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:document-magnifying-glass'"></mat-icon>
                      </button>
                    </div>
                  </div>

                  <!-- File Upload -->
                  <div class="w-full px-3 mb-2 md:mb-0">
                    <asha-image-upload [initial]="fileGroup.get('file')?.value"
                      (uploadSuccess)="uploadSuccess($event, i)"></asha-image-upload>
                    <mat-hint class="text-sm text-gray-400">(Supports file types: JPEG, PNG, and PDF, with a maximum size of 1 MB.)</mat-hint>
                  </div>
                </div>

                <!-- Remove Button -->
                <div class="flex items-center justify-start">
                  <button mat-stroked-button color="warn" (click)="removeFile(i)">Remove</button>
                </div>
              </div>
            </div>

            <!-- Add File Button -->
            <div class="flex items-center justify-end mb-10">
              <button mat-raised-button color="primary" (click)="addFile()">+ Add File</button>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end">
              <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
              <button class="ml-4" mat-flat-button type="button" [color]="'primary'" (click)="onSubmit_files()">Upload</button>
            </div>
          </form>
        </div>
      </mat-tab>
    </mat-tab-group>
    <!-- </form> -->
  </div>
</div>
