import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule, NgClass } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import {
  Form<PERSON>rray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { QuillModule } from 'ngx-quill';
import { ToastrService } from 'ngx-toastr';
import { ApplicantService } from '../applicant.service';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
@Component({
  selector: 'form-user',
  templateUrl: './form.component.html',
  standalone: true,
  imports: [
    MatIconModule,
    FormsModule,
    MatFormFieldModule,
    NgClass,
    MatInputModule,
    TextFieldModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    MatChipsModule,
    MatDatepickerModule,
    CommonModule,
    NgxDropzoneModule,
    MatRadioModule,
    MatAutocompleteModule,
    QuillModule,
    MatTabsModule,
    ImageUploadComponent,
  ],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit {
  Id: number;
  form: FormGroup;
  accountForm: FormGroup;
  formfamily: FormGroup;
  formeducational: FormGroup;
  formhistory: FormGroup;
  formtraining: FormGroup;
  formskill: FormGroup;
  formreference: FormGroup;
  formemergency: FormGroup;
  formQuestion: FormGroup;
  formFile: FormGroup;

  //================================================================================================
  questions: any[] = [];
  selectedOptions: string[] = [];
  additionalInfo: string[] = [];
  itemData: any;
  namefile: { name: string }[] = [
    { name: 'Resume' },
    { name: 'บัตรประจำตัวประชาชน / พาสปอร์ต' },
    { name: 'ทะเบียนบ้าน' },
    { name: 'สำเนาเปลี่ยนชื่อ-สกุล' },
    { name: 'ใบแสดงผลการศึกษา' },
    { name: 'วุฒิการศึกษา' },
    { name: 'หนังสือรับรองการทำงาน' },
    { name: 'สลิปเงินเดือนล่าสุด' },
    { name: 'Portfolio' },
    { name: 'ใบรับรองการอบรม' },
    { name: 'ผลสอบวัดระดับภาษา (TOEIC, TOEFL, ฯลฯ)' },
    { name: 'ใบขับขี่' },
    { name: 'ใบผ่านงานทหาร' },
    { name: 'ใบอนุญาตประกอบวิชาชีพ' },
    { name: 'อื่นๆ' }
  ];
  /**
   * Constructor
   */
  constructor(
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder,
    private _Service: ApplicantService,
    private dialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _toast: ToastrService,
    private imageUploadService: ImageUploadService,
  ) {
    this.Id = this._activatedRoute.snapshot.params.id;
    this.form = this._fb.group({
      prefixEn: ['', Validators.required],
      fullnameEn: ['', Validators.required],
      nicknameEn: ['', Validators.required],
      prefixTh: ['', Validators.required],
      fullnameTh: ['', Validators.required],
      nicknameTh: ['', Validators.required],
      permanentAddress: ['', Validators.required],
      presentAddress: ['', Validators.required],
      homePhone: ['', [Validators.pattern('^[0-9]+$')]], // รองรับเฉพาะตัวเลข
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]+$')]],   // รองรับเฉพาะตัวเลข
      email: ['', [Validators.required, Validators.email]], // ตั้งค่าอีเมลจาก Local Storage
      birthDate: ['', Validators.required],
      age: ['', Validators.required],
      placeOfBirth: ['', Validators.required],
      height: ['', [Validators.required, Validators.min(0), Validators.max(300)]],
      weight: ['', [Validators.required, Validators.min(0), Validators.max(500)]],
      nationality: [''],
      idCardNo: [''],
      idCardIssuedBy: [''],
      idCardExpiryDate: [''],
      passportNo: [''],
      passportIssuedBy: [''],
      passportExpiryDate: [''],

      //================================================================================================

      maritalStatus: ['', Validators.required],
      marriedName: ['',],
      marriedAge: ['', [Validators.min(0), Validators.max(120)]],
      marriedOccupation: ['',],
      childrenQty: ['', [Validators.min(0), Validators.max(6)]],
      // Add children fields
      childrenName1: [''],
      childrenAge1: ['', [Validators.min(0), Validators.max(120)]],
      childrenName2: [''],
      childrenAge2: ['', [Validators.min(0), Validators.max(120)]],
      childrenName3: [''],
      childrenAge3: ['', [Validators.min(0), Validators.max(120)]],
      childrenName4: [''],
      childrenAge4: ['', [Validators.min(0), Validators.max(120)]],
      childrenName5: [''],
      childrenAge5: ['', [Validators.min(0), Validators.max(120)]],
      childrenName6: [''],
      childrenAge6: ['', [Validators.min(0), Validators.max(120)]],

      applicantFamily: this._fb.array([]), // ใช้ FormArray สำหรับข้อมูลหลายชุด

      applicantEducations: this._fb.array([]),

      applicantWork: this._fb.array([]),

      applicantTrain: this._fb.array([]),

      skillLevel1: [''],
      skillLevel2: [''],
      skillLanguage1: [''],
      skillLanguage2: [''],
      typingEnSpeed: [''],
      typingThSpeed: [''],
      computerSkill: [''],
      otherSkill: [''],

      references: this._fb.array([]),

      emergency: this._fb.array([]),
      nameOfYouFriend: [''],
      anyAdditional: [''],
    });
    this.accountForm = this._fb.group({
      prefixEn: ['', Validators.required],
      fullnameEn: ['', Validators.required],
      nicknameEn: ['', Validators.required],
      prefixTh: ['', Validators.required],
      fullnameTh: ['', Validators.required],
      nicknameTh: ['', Validators.required],
      permanentAddress: ['', Validators.required],
      presentAddress: ['', Validators.required],
      homePhone: ['', [Validators.pattern('^[0-9]+$')]], // รองรับเฉพาะตัวเลข
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]+$')]],   // รองรับเฉพาะตัวเลข
      email: ['', [Validators.required, Validators.email]], // ตั้งค่าอีเมลจาก Local Storage
      birthDate: ['', Validators.required],
      age: ['', Validators.required],
      placeOfBirth: ['', Validators.required],
      height: ['', [Validators.required, Validators.min(0), Validators.max(300)]],
      weight: ['', [Validators.required, Validators.min(0), Validators.max(500)]],
      nationality: ['', Validators.required],
      idCardNo: ['', Validators.required],
      idCardIssuedBy: ['', Validators.required],
      idCardExpiryDate: ['', Validators.required],
      passportNo: [''],
      passportIssuedBy: [''],
      passportExpiryDate: [''],
    });
    this.formfamily = this._fb.group({
      maritalStatus: ['', Validators.required],
      marriedName: ['', [Validators.required]],
      marriedAge: [0, [Validators.min(0), Validators.max(120)]],
      marriedOccupation: ['', [Validators.required]],
      childrenQty: [0, [Validators.min(0), Validators.max(6)]],
      // Add children fields
      childrenName1: [''],
      childrenAge1: [0, [Validators.min(0), Validators.max(120)]],
      childrenName2: [''],
      childrenAge2: [0, [Validators.min(0), Validators.max(120)]],
      childrenName3: [''],
      childrenAge3: [0, [Validators.min(0), Validators.max(120)]],
      childrenName4: [''],
      childrenAge4: [0, [Validators.min(0), Validators.max(120)]],
      childrenName5: [''],
      childrenAge5: [0, [Validators.min(0), Validators.max(120)]],
      childrenName6: [''],
      childrenAge6: [0, [Validators.min(0), Validators.max(120)]],

      applicantFamily: this._fb.array([]),
    });
    this.formeducational = this._fb.group({
      applicantEducations: this._fb.array([]),
    });
    this.formhistory = this._fb.group({
      applicantWork: this._fb.array([]),
    });
    this.formtraining = this._fb.group({
      applicantTrain: this._fb.array([]),
    });
    this.formskill = this._fb.group({
      skillLevel1: [''],
      skillLevel2: [''],
      skillLanguage1: [''],
      skillLanguage2: [''],
      typingEnSpeed: [''],
      typingThSpeed: [''],
      computerSkill: [''],
      otherSkill: [''],
    });
    this.formreference = this._fb.group({
      references: this._fb.array([]),
    });
    this.formemergency = this._fb.group({
      emergency: this._fb.array([]),
      nameOfYouFriend: [''],
      anyAdditional: [''],
    });

    this.formQuestion = this._fb.group({
      applicantQuestion: this._fb.array([]) // Initialize applicantQuestion as FormArray
    });

    this.formFile = this._fb.group({
      applicantFiles: this._fb.array([]),
    });
  }
  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  ngOnInit() {
    this.loadData();
    this.setQuestions(); // Ensure questions are set after loading data
  }

  /**
   * After view init
   */
  ngAfterViewInit(): void { }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
  }
  //================================================================================================

  loadData(): void {
    const applicantId = this.Id;

    // ตรวจสอบว่ามี applicantId หรือไม่
    if (!applicantId) {
      return;
    }
    //ประวัตส่วนตัว
    this._Service.getApplicantById(applicantId).subscribe({
      next: (data) => {
        console.log('Data loaded:', data);
        this.form.patchValue({
          ...data,
        });

        this.accountForm.patchValue({
          ...data,
          // email: this.accountForm.get('email')?.value, // คงค่าอีเมลเดิม
        });

        this.formfamily.patchValue({
          ...data,
        });
        const applicantFamilies = data.applicantFamilies || [];
        if (applicantFamilies.length === 0) {
          this.addFamily();
        } else {
          applicantFamilies.forEach((ref: any) => {
            const referenceGroup = this._fb.group({
              name: [ref.name || '', Validators.required],
              relationship: [ref.relationship || ''],
              age: [ref.age || ''],
              occupation: [ref.occupation || ''],
              placeOfWork: [ref.placeOfWork || ''],
            });
            this.Family.push(referenceGroup);
          });
        }

        this.formeducational.patchValue({
          ...data,
        });
        const educations = data.applicantEducations || [];
        if (educations.length === 0) {
          this.addInstitution();
        } else {
          educations.forEach((edu: any) => {
            const InstitutionGroup = this._fb.group({
              level: [edu.level || '', Validators.required],
              name: [edu.name || '', Validators.required],
              major: [edu.major || '', Validators.required],
              from: [this.transformDate(edu.from) || '', Validators.required],
              to: [this.transformDate(edu.to) || '', Validators.required],
              degree: [edu.degree || '', [Validators.min(0), Validators.max(4)]],
            });
            this.Institution.push(InstitutionGroup);
          });
        }

        this.formhistory.patchValue({
          ...data,
        });
        const employment = data.applicantWorks || [];
        if (employment.length === 0) {
          // หากไม่มีข้อมูลใน references ให้เพิ่มฟอร์มเปล่า
          this.addEmployment();
        } else {
          // เติมข้อมูลจาก API ลงใน FormArray
          employment.forEach((ref: any) => {
            const referenceGroup = this._fb.group({
              name: [ref.name || '', Validators.required],
              position: [ref.position || '', Validators.required],
              from: [ref.from || '', Validators.required],
              to: [ref.to || '', Validators.required],
              reasonLeaving: [
                ref.reasonLeaving || '',
                Validators.required,
              ],
              salary: [ref.salary || '', Validators.required],
              id: [ref.id || ''], // เพิ่ม id ให้กับฟอร์ม
            });
            this.employment.push(referenceGroup);
          });
        }

        this.formtraining.patchValue({
          ...data,
        });
        const Training = data.applicantTrains || [];
        if (Training.length === 0) {
          this.addTraining();
        } else {
          Training.forEach((ref: any) => {
            const referenceGroup = this._fb.group({
              name: [ref.name || '', Validators.required],
              detail: [ref.detail || '', Validators.required],
              monthYear: [
                ref.monthYear || '',
                Validators.required,
              ],
              certificate: [
                ref.certificate || '',
                Validators.required,
              ],
            });
            this.Training.push(referenceGroup);
          });
        }

        this.formskill.patchValue({
          ...data,
        });

        this.formreference.patchValue({
          ...data,
        });
        const references = data.applicantReferences || []; // ตรวจสอบข้อมูลจาก API
        if (references.length === 0) {
          // หากไม่มีข้อมูลใน references ให้เพิ่มฟอร์มเปล่า
          this.addReference();
        } else {
          // เติมข้อมูลจาก API ลงใน FormArray
          references.forEach((ref: any) => {
            const referenceGroup = this._fb.group({
              name: [ref.name || '', Validators.required],
              companyName: [ref.companyName || '', Validators.required],
              tel: [ref.tel || '', Validators.required],
            });
            this.references.push(referenceGroup);
          });
        }

        this.formemergency.patchValue({
          ...data,
        });
        const emergencyContacts = data.applicantContacts || []; // กำหนดข้อมูล emergency contacts
        if (emergencyContacts.length === 0) {
          this.addEmergency(); // เพิ่มฟอร์มเปล่าหากไม่มีข้อมูล
        } else {
          emergencyContacts.forEach((contact) => this.addEmergency(contact));
        }

        this._Service.getQuestion().subscribe(
          (data) => {
            this.questions = data;
            this.setQuestions();

            this.questions = this.questions.map(question => {
              const answer = data.applicantQuestions.find(ans => ans.question.id === question.id);

              return {
                ...question,
                answered: answer ? {
                  choiceId: answer.choice.id,
                  remark: answer.remark
                } : null
              };
            });


          },
          (error) => {
            console.error('Error fetching questions:', error);
          }
        );

        this.formFile.patchValue({
          ...data,
        });
        const file = data.applicantFiles || []; // ตรวจสอบข้อมูลจาก API
        if (file.length === 0) {
          this.addFile();
        } else {
          file.forEach((ref: any) => {
            const fileGroup = this._fb.group({
              name: [ref.name || ''],
              file: [ref.file || ''],
            });
            this.files.push(fileGroup);
          });
        }

        this._changeDetectorRef.markForCheck();
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this._toast.error('Failed to load data.');
      },
    });
  }

  setQuestions() {
    this.questions.forEach(q => {
      this.applicantQuestion.push(this.createQuestionForm(q));
    });
  }


  private transformDate(dateString: string): string | null {
    if (!dateString) return null;
    const date = new Date(dateString); // แปลง string เป็น Date object
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }


  //================================================================================================
  get Family(): FormArray {
    return this.formfamily.get('applicantFamily') as FormArray;
  }

  addFamily(): void {
    const FamilyGroup = this._fb.group({
      name: [''],
      relationship: [''],
      age: [0],
      occupation: [''],
      placeOfWork: [''],
    });

    this.Family.push(FamilyGroup);
  }

  removeFamily(index: number): void {
    this.Family.removeAt(index);
  }

  //================================================================================================

  get Institution(): FormArray {
    return this.formeducational.get('applicantEducations') as FormArray;
  }

  addInstitution(): void {
    const InstitutionGroup = this._fb.group({
      level: ['', Validators.required],
      name: ['', Validators.required],
      major: [''],
      from: ['', Validators.required],
      to: ['', Validators.required],
      degree: ['', [Validators.min(0), Validators.max(4)]],
    });
    this.Institution.push(InstitutionGroup); // เพิ่มฟอร์มกลุ่มลงใน FormArray
  }

  removeInstitution(index: number): void {
    this.Institution.removeAt(index);
  }

  //================================================================================================

  get employment(): FormArray {
    return this.formhistory.get('applicantWork') as FormArray;
  }

  // เพิ่มฟอร์มกลุ่มใหม่ใน FormArray
  addEmployment(): void {
    const employmentGroup = this._fb.group({
      name: ['', Validators.required],
      position: ['', Validators.required],
      salary: ['', Validators.required],
      from: ['', Validators.required],
      to: ['', Validators.required],
      reasonLeaving: ['', Validators.required],
    });

    this.employment.push(employmentGroup);
  }

  // ลบฟอร์มกลุ่ม
  removeEmployment(index: number): void {
    this.employment.removeAt(index);
  }

  //================================================================================================

  get Training(): FormArray {
    return this.formtraining.get('applicantTrain') as FormArray;
  }

  addTraining(): void {
    const referenceGroup = this._fb.group({
      name: ['', Validators.required],
      monthYear: ['', Validators.required],
      certificate: ['', Validators.required],
      detail: ['', Validators.required],
    });

    this.Training.push(referenceGroup);
  }

  // ลบฟอร์มกลุ่ม
  removeTraining(index: number): void {
    this.Training.removeAt(index);
  }

  //================================================================================================

  get references(): FormArray {
    return this.formreference.get('references') as FormArray;
  }

  // เพิ่มฟอร์มกลุ่มใหม่ใน FormArray
  addReference(): void {
    if (this.references.length < 3) {
      // ตรวจสอบเงื่อนไขจำนวนสูงสุด
      const referenceGroup = this._fb.group({
        name: ['', Validators.required], // เพิ่มฟิลด์ Name
        companyName: ['', Validators.required], // เพิ่มฟิลด์ Company Name
        tel: ['', Validators.required], // เพิ่มฟิลด์ Telephone
      });

      this.references.push(referenceGroup); // เพิ่มฟอร์มกลุ่มลงใน FormArray
    } else {
      this._toast.error('You can only add up to 3 references.'); // แจ้งเตือนถ้าถึงขีดจำกัด
    }
  }

  removeReference(index: number): void {
    this.references.removeAt(index);
  }

  //================================================================================================

  // คืนค่าฟอร์มอาร์เรย์
  get emergency(): FormArray {
    return this.formemergency.get('emergency') as FormArray;
  }

  // เพิ่ม Emergency Contact
  addEmergency(contact?: any): void {
    const contactGroup = this._fb.group({
      name: [contact?.name || '', Validators.required],
      relationship: [contact?.relationship || '', Validators.required],
      telHome: [
        contact?.telHome === 'null' ? '' : contact?.telHome || '-',
      ], // แปลง "null" เป็น ""
      tel: [contact?.tel || '', Validators.required],
    });
    this.emergency.push(contactGroup);
  }

  // ลบฟอร์มกลุ่ม
  removeEmergency(index: number): void {
    this.emergency.removeAt(index);
  }

  //================================================================================================

  // คืนค่าฟอร์มอาร์เรย์
  get applicantQuestion() {
    return this.formQuestion.get('applicantQuestion') as FormArray;
  }

  createQuestionForm(question: any): FormGroup {
    return this._fb.group({
      questionId: [question?.id],
      choiceId: [question?.answered?.choiceId || ''], // เติมค่า choiceId ถ้ามีคำตอบเดิม
      remark: [question?.answered?.remark || ''] // เติมค่า remark ถ้ามีคำตอบเดิม
    });
  }

  //================================================================================================

  get files(): FormArray {
    return this.formFile.get('applicantFiles') as FormArray;
  }

  addFile(): void {
    const fileGroup = this._fb.group({
      name: [''],
      file: [null],
    });
    this.files.push(fileGroup);
  }

  removeFile(index: number): void {
    this.files.removeAt(index);
  }

  // uploadSuccess(event, index): void {
  //   this.files.at(index).patchValue({
  //     file: event.path
  //   });
  // }
  uploadSuccess(event, index): void {
    console.log('Event:', event);
    console.log('Index:', index);


    this.imageUploadService.upload(event).subscribe({
      next: (resp: any) => {
        console.log('resp:',resp);

        // this.form.patchValue({
        //   image: resp.pathUrl
        // });
        this.files.at(index).patchValue({
          file: resp.pathUrl
        });
        console.log(this.files.value);

      },
      error: (err) => {
        this._toast.error(JSON.stringify(err))
      },
    })
  }

  opendialogfile(fileUrl: string): void {
    window.open(fileUrl, '_blank');
  }

  //================================================================================================

  Submit(): void {
    // if (this.form.invalid) {
    //     return;
    // }
    // let formValue = this.form.value;
    // if (this.Id) {
    //     const confirmation = this._fuseConfirmationService.open({
    //         title: "Edit value",
    //         message: "Do you want to edit value? ",
    //         icon: {
    //             show: false,
    //             name: "heroicons_outline:exclamation",
    //             color: "warning"
    //         },
    //         actions: {
    //             confirm: {
    //                 show: true,
    //                 label: "Save",
    //                 color: "primary"
    //             },
    //             cancel: {
    //                 show: true,
    //                 label: "Cancel"
    //             }
    //         },
    //         dismissible: true
    //     });
    //     // Subscribe to the confirmation dialog closed action
    //     confirmation.afterClosed().subscribe((result) => {
    //         if (result === 'confirmed') {
    //             this._Service.update(this.Id, formValue).subscribe({
    //                 next: (resp: any) => {
    //                     this._router.navigate(['recruitment/job']);
    //                     this._toast.success('Successed')
    //                 },
    //                 error: (err: any) => {
    //                     this.form.enable();
    //                     this._fuseConfirmationService.open({
    //                         title: "Please check value.",
    //                         message: err.error.message,
    //                         icon: {
    //                             show: true,
    //                             name: "heroicons_outline:exclamation",
    //                             color: "warning"
    //                         },
    //                         actions: {
    //                             confirm: {
    //                                 show: false,
    //                                 label: "Close",
    //                                 color: "primary"
    //                             },
    //                             cancel: {
    //                                 show: false,
    //                                 label: "Cancel"
    //                             }
    //                         },
    //                         dismissible: true
    //                     });
    //                 }
    //             });
    //         }
    //     });
    // } else {
    //     const confirmation = this._fuseConfirmationService.open({
    //         title: "Add value",
    //         message: "Do you want to save data ?",
    //         icon: {
    //             show: false,
    //             name: "heroicons_outline:exclamation",
    //             color: "warning"
    //         },
    //         actions: {
    //             confirm: {
    //                 show: true,
    //                 label: "Save",
    //                 color: "primary"
    //             },
    //             cancel: {
    //                 show: true,
    //                 label: "Cancel"
    //             }
    //         },
    //         dismissible: true
    //     });
    //     // Subscribe to the confirmation dialog closed action
    //     confirmation.afterClosed().subscribe((result) => {
    //         if (result === 'confirmed') {
    //             this._Service.create(formValue).subscribe({
    //                 next: (resp: any) => {
    //                     this._router.navigate(['recruitment/job']);
    //                     this._toast.success('Successed')
    //                 },
    //                 error: (err: any) => {
    //                     this.form.enable();
    //                     this._fuseConfirmationService.open({
    //                         title: "Please check value.",
    //                         message: err.error.message,
    //                         icon: {
    //                             show: true,
    //                             name: "heroicons_outline:exclamation",
    //                             color: "warning"
    //                         },
    //                         actions: {
    //                             confirm: {
    //                                 show: false,
    //                                 label: "Save",
    //                                 color: "primary"
    //                             },
    //                             cancel: {
    //                                 show: false,
    //                                 label: "Cancel"
    //                             }
    //                         },
    //                         dismissible: true
    //                     });
    //                 }
    //             });
    //         }
    //     });
    // }
  }
  onSubmit_personaldata() {
    if (this.accountForm.valid) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Edit value",
        message: "Do you want to edit value? ",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });

      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          const applicantId = this.Id;
          if (!applicantId) {
            this._toast.error('Applicant ID not found. Please login again.');
            return;
          }
          const formData = this.accountForm.value;
          this._Service.createPersonaldata(applicantId, formData).subscribe({
            next: (response) => {
              console.log('Response:', response);
              this._toast.success('Update successful!');
            },
            error: (error) => {
              console.error('Error:', error);
              this._toast.error('Error saving data. Please try again.');
            },
          });
        }
      });
    } else {
      this._toast.error('Please fill in all required fields.');
      this.accountForm.markAllAsTouched();
    }
  }

  onSubmit_familydetail() {
    if (this.formfamily.invalid) {
      this._toast.error(`Fill in the information completely. ${this.formfamily.errors}`);
      return;
    }

    const confirmation = this._fuseConfirmationService.open({
      title: "Edit value",
      message: "Do you want to edit value? ",
      icon: {
        show: false,
        name: "heroicons_outline:exclamation",
        color: "warning"
      },
      actions: {
        confirm: {
          show: true,
          label: "Save",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "Cancel"
        }
      },
      dismissible: true
    });

    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        const familyData = this.formfamily.value;
        const userId = this.Id;
        if (!userId) {
          this._toast.error('User ID not found');
          return;
        }
        this._Service.updateFamily(userId, familyData).subscribe(
          () => {
            this._toast.success('Success');
          },
          (error) => {
            this._toast.error('Failed to update data.');
          }
        );
      }
    });
  }

  onSubmit_educationnaldetail() {
    if (this.formeducational.valid) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Edit value",
        message: "Do you want to edit value? ",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });

      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          const applicantId = this.Id;
          if (!applicantId) {
            this._toast.error('Applicant ID not found. Please login again.');
            console.error('Applicant ID is missing in local storage.');
            return;
          }
          const references = this.formeducational.value.applicantEducations;
          this._Service.updateApplicantEducationById(applicantId, references).subscribe(
            (response) => {
              console.log('Updated educational info:', response);
              this._toast.success('Educational info updated successfully!');
            },
            (error) => {
              console.error('Update failed:', error);
              this._toast.error('Failed to update educational info.');
            }
          );
        }
      });
    } else {
      this._toast.error('Please fill out all required fields.');
      this.formeducational.markAllAsTouched();
    }
  }

  onSubmit_employmenthistory() {
    if (this.formhistory.invalid) {
      this._toast.error('Fill in the information completely.');
      return;
    }

    const confirmation = this._fuseConfirmationService.open({
      title: "Edit value",
      message: "Do you want to edit value? ",
      icon: {
        show: false,
        name: "heroicons_outline:exclamation",
        color: "warning"
      },
      actions: {
        confirm: {
          show: true,
          label: "Save",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "Cancel"
        }
      },
      dismissible: true
    });

    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        const employmentData = this.formhistory.value;
        const userId = this.Id;
        if (!userId) {
          this._toast.error('User ID not found');
          return;
        }
        console.log('Sending request to update employment with ID:', userId);
        this._Service.updateEmploymenthistory(userId, employmentData).subscribe(
          () => {
            this._toast.success('Success');
          },
          (error) => {
            console.error('Error updating data:', error);
            this._toast.error('Failed to update data.');
          }
        );
      }
    });
  }

  onSubmit_traininghistory() {
    if (this.formtraining.invalid) {
      this._toast.error('Fill in the information completely.');
      return;
    }

    const confirmation = this._fuseConfirmationService.open({
      title: "Edit value",
      message: "Do you want to edit value? ",
      icon: {
        show: false,
        name: "heroicons_outline:exclamation",
        color: "warning"
      },
      actions: {
        confirm: {
          show: true,
          label: "Save",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "Cancel"
        }
      },
      dismissible: true
    });

    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        const trainingtData = this.formtraining.value;
        const userId = this.Id;
        if (!userId) {
          this._toast.error('User ID not found');
          return;
        }
        this._Service.updateTraining(userId, trainingtData).subscribe(
          () => {
            this._toast.success('Success');
          },
          (error) => {
            console.error('Error updating data:', error);
            this._toast.error;
          }
        );
      }
    });
  }

  onSubmit_basicskill() {
    if (this.formskill.valid) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Edit value",
        message: "Do you want to edit value? ",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });

      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          const applicantId = this.Id;
          const data = this.formskill.value;
          this._Service.updateApplicant(applicantId, data).subscribe(
            (response) => {
              console.log('Successfully updated references:', response);
              this._toast.success('Update successful!');
            },
            (error) => {
              console.error('Failed to update:', error);
              this._toast.error('An error occurred while updating.');
            }
          );
        }
      });
    } else {
      this._toast.error('Please fill out all required fields.');
    }
  }

  onSubmit_references() {
    if (this.formreference.valid) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Edit value",
        message: "Do you want to edit value? ",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });

      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          const applicantId = this.Id;
          const references = this.formreference.value.references;
          this._Service.updateApplicantReferences(applicantId, references).subscribe(
            (response) => {
              console.log('Successfully updated references:', response);
              this._toast.success('Update successful!');
            },
            (error) => {
              console.error('Failed to update references:', error);
              this._toast.error('An error occurred while updating.');
            }
          );
        }
      });
    } else {
      this._toast.error('Please fill out all required fields.');
    }
  }

  onSubmit_questions() {
    const confirmation = this._fuseConfirmationService.open({
      title: "Edit value",
      message: "Do you want to edit value? ",
      icon: {
        show: false,
        name: "heroicons_outline:exclamation",
        color: "warning"
      },
      actions: {
        confirm: {
          show: true,
          label: "Save",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "Cancel"
        }
      },
      dismissible: true
    });

    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        const applicantId = this.Id;
        this._Service.updateApplicantQuestionById(applicantId, this.formQuestion.value).subscribe(
          () => {
            this._toast.success('Success');
          },
          (error) => {
            console.error('Error updating data:', error);
            this._toast.error;
          }
        );
      }
    });
  }

  onSubmit_emergency() {
    if (this.formreference.valid) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Edit value",
        message: "Do you want to edit value? ",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Save",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });

      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          const formValue = this.formreference.value;
          const payload = {
            applicantContact: this.formreference.value.references.map((contact: any) => ({
              name: contact.name,
              relationship: contact.relationship,
              telHome: contact.telHome || '',
              tel: contact.tel,
            })),
            nameOfYouFriend: this.formreference.value.nameOfYouFriend || '',
            anyAdditional: this.formreference.value.anyAdditional || '',
          };
          console.log('Payload:', payload);
          const applicantId = this.Id;
          this._Service.updateApplicantContact(applicantId, payload).subscribe(
            (response) => {
              console.log('Update Successful:', response);
              this._toast.success('Update successful!');
            },
            (error) => {
              console.error('Update Failed:', error);
              this._toast.error('Failed to update data.');
            }
          );
        }
      });
    } else {
      console.log('Form is invalid');
      this._toast.error('Please fill in all required fields.');
      this.formreference.markAllAsTouched();
    }
  }

  onSubmit_files() {
    if (this.formFile.valid) {
      const confirmation = this._fuseConfirmationService.open({
        title: "Upload Files",
        message: "Do you want to upload these files?",
        icon: {
          show: false,
          name: "heroicons_outline:exclamation",
          color: "warning"
        },
        actions: {
          confirm: {
            show: true,
            label: "Upload",
            color: "primary"
          },
          cancel: {
            show: true,
            label: "Cancel"
          }
        },
        dismissible: true
      });

      confirmation.afterClosed().subscribe((result) => {
        if (result === 'confirmed') {
          // const FormData = new FormData();
          // this.files.controls.forEach((fileGroup) => {
          //   formData.append('file', fileGroup.get('file')?.value);
          //   formData.append('name', fileGroup.get('name')?.value);
          // });
          const payload = {
            applicantFiles: this.formFile.value.applicantFiles.map((contact: any) => ({
              name: contact.name,
              file: contact.file,
            })),
          };
          console.log('Payload:', payload);

          const applicantId = this.Id;
          this._Service.uploadFiles(applicantId, payload).subscribe(
            (response) => {
              console.log('Files uploaded successfully:', response);
              this._toast.success('Files uploaded successfully!');
            },
            (error) => {
              console.error('Failed to upload files:', error);
              this._toast.error('Failed to upload files.');
            }
          );
        }
      });
    } else {
      this._toast.error('Please fill out all required fields.');
    }
  }

  onCancel(): void {
    // const email = this.accountForm.get('email')?.value;
    // this.accountForm.reset({ email }); //
    // alert('Form has been reset.');
    this._router.navigate(['applicant']);
  }

  backTo() {
    this._router.navigate(['applicant']);
  }
}
