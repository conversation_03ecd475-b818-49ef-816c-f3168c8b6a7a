import { Routes } from '@angular/router';
import { ApplicantComponent } from './applicant.component';
import { FormComponent } from './form/form.component';

export default [
    {
        path: '',
        component: ApplicantComponent,
        // children: [
        //     {
        //         path: 'form',
        //         component: FormComponent,
        //     },
        //     {
        //         path: 'edit/:id',
        //         component: FormComponent,
        //     },
        // ]
    },
    {
      path: 'form',
      component: FormComponent,
    },
    {
        path: 'edit/:id',
        component: FormComponent,
    },
    // {
    //   path     : 'job',
    //   component: JobComponent,
    //   resolve: {
    //       // title: () => inject(EmployeeService).getTitle(),
    //       // level: () => inject(EmployeeService).getLevel(),
    //       // department: () => inject(EmployeeService).getDepartment(),
    //       // employeeType: () => inject(EmployeeService).getEmployeeType(),
    //       // workShift: () => inject(EmployeeService).getWorkShift(),
    //       // approval: () => inject(EmployeeService).getApproval(),
    //   },
    // },

] as Routes;
