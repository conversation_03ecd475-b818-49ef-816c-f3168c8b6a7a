import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { removeEmpty } from 'app/helper';
import { param } from 'jquery';

@Injectable({
  providedIn: 'root'
})
export class ApplicantService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _title: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _level: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _department: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _leaveType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _employeeType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _workShift: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _position: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _permission: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }



  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/applicant/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        // ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }



  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/job`, data)
  }

  update(id: number, data: object) {
    return this.http.put(`${environment.apiUrl}/api/job/${id}`, data)
  }

  getUserId(id: any) {
    return this.http.get(`${environment.apiUrl}/api/job/` + id).pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }
  getEmployee() {
    return this.http.get(environment.apiUrl + '/api/employee').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }

  getTitle() {
    return this.http.get(environment.apiUrl + '/api/title').pipe(
      tap((resp: any) => {
        this._title.next(resp);
      }),
    )
  }
  getLevel() {
    return this.http.get(environment.apiUrl + '/api/level').pipe(
      tap((resp: any) => {
        this._level.next(resp);
      }),
    )
  }
  getDepartment() {
    return this.http.get(environment.apiUrl + '/api/department').pipe(
      tap((resp: any) => {
        this._department.next(resp);
      }),
    )
  }
  getLeaveType() {
    return this.http.get(environment.apiUrl + '/api/leave-type').pipe(
      tap((resp: any) => {
        this._leaveType.next(resp);
      }),
    )
  }
  getEmployeeType() {
    return this.http.get(environment.apiUrl + '/api/employee-type').pipe(
      tap((resp: any) => {
        this._employeeType.next(resp);
      }),
    )
  }
  getWorkShift() {
    return this.http.get(environment.apiUrl + '/api/work-shift').pipe(
      tap((resp: any) => {
        this._workShift.next(resp);
      }),
    )
  }

  getApproval() {
    return this.http.get(environment.apiUrl + '/api/appove-list').pipe(
      tap((resp: any) => {
        this._workShift.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/applicant/' + id)
  }

  checkEmployeeCode(employeeCode: string): Observable<boolean> {
    return this.http.get<{ exists: boolean }>(environment.apiUrl + `/api/employee/check-code/${employeeCode}`).pipe(
      map((response) => response.exists)
    );
  }

  checkUsername(username: string): Observable<boolean> {
    return this.http.get<{ exists: boolean }>(environment.apiUrl + `/api/employee/check-username/${username}`).pipe(
      map((response) => response.exists)
    );
  }

  getLeavePermissionByEmp(id: number) {
    return this.http.get(`${environment.apiUrl}/api/employee/${id}/leave-permission`)
  }
  getInitialLeave(id: number) {
    return this.http.get(`${environment.apiUrl}/api/employee/${id}/leave-permission/initial`)
  }
  updateLeavePermission(id: number, data: any) {
    return this.http.put(`${environment.apiUrl}/api/employee/${id}/leave-permission/update`, data)
  }

  generateCode(id: any) {
    return this.http.get(`${environment.apiUrl}/api/employee/auto-code`, {
      params: {
        t: id
      }
    })
  }
  //================================================================================================
  createPersonaldata(applicantId: number, data: any): Observable<any> {
    return this.http.put(`${environment.apiUrl}/api/applicant/${applicantId}`, data);
  }
  getApplicantById(id: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/api/applicant/${id}`);
  }

  getQuestion(): Observable<any> {
    return this.http.get(`${environment.apiUrl}/api/question`);
  }

  updateFamily(id: number, data: any): Observable<any> {
    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_family/${id}`, data);
  }

  updateEmploymenthistory(id: number, data: any): Observable<any> {
    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_work/${id}`, data);
  }

  updateApplicantEducationById(id: number, references: any): Observable<any> {
    // โครงสร้าง JSON ตาม API คาดหวัง
    console.log('ass',references);

    const payload = {
      applicantEducation: references.map((ref: any) => ({
        ...ref,
      })),
    };
    console.log('aaa',payload);

    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_education/${id}`, payload);
  }
  updateTraining(id: number, data: any): Observable<any> {
    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_train/${id}`, data);
  }
  updateApplicant(id: number, data: any): Observable<any> {
    const url = `${environment.apiUrl}/api/applicant/applicant_skill/${id}`;
    return this.http.put(url, data); // ส่งข้อมูล `data` ไปยัง backend
  }

  updateApplicantReferences(id: number, references: any): Observable<any> {
    const payload = {
      applicantReference: references.map((ref: any) => ({
        ...ref, // ข้อมูลจากฟอร์ม เช่น name, companyName, tel
      })),
    };
    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_reference/${id}`, payload);
  }
  updateApplicantContact(id: number, data: any): Observable<any> {
    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_contact/${id}`, data);
  }

  updateApplicantQuestionById(id: number, references: any): Observable<any> {
    const url = `${environment.apiUrl}/api/applicant/applicant_question/${id}`; // รวม base URL และ id
    // โครงสร้าง JSON ตาม API คาดหวัง

    return this.http.put(url, references); // ใช้ URL และ payload
  }

  getQuestionById(id: string): Observable<any> {
    return this.http.get(`${environment.apiUrl}/api/applicant/applicant_question/${id}`);
  }

  uploadFiles(applicantId: number, formData: any): Observable<any> {
    return this.http.put(`${environment.apiUrl}/api/applicant/applicant_files/${applicantId}`, formData);
  }
}
