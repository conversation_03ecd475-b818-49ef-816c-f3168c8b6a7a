import { Routes } from '@angular/router';
import { EmployeeListComponent } from './page.component';
import { FormComponent } from './form/form.component';
import { inject } from '@angular/core';
import { EmployeeService } from './page.service';
import { EmployeeComponent } from './employee.component';

export default [
    {
        path: '',
        component: EmployeeComponent,
        children: [
            {
                path     : '',
                component: EmployeeListComponent,
                resolve: {
                    title: () => inject(EmployeeService).getTitle(),
                    level: () => inject(EmployeeService).getLevel(),
                    department: () => inject(EmployeeService).getDepartment(),
                    employeeType: () => inject(EmployeeService).getEmployeeType(),
                    workShift: () => inject(EmployeeService).getWorkShift(),
                    approval: () => inject(EmployeeService).getApproval(),
                },

            },
            {
                path: 'form',
                component: FormComponent,
                resolve: {
                    title: () => inject(EmployeeService).getTitle(),
                    level: () => inject(EmployeeService).getLevel(),
                    department: () => inject(EmployeeService).getDepartment(),
                    employeeType: () => inject(EmployeeService).getEmployeeType(),
                    workShift: () => inject(EmployeeService).getWorkShift(),
                    approval: () => inject(EmployeeService).getApproval(),
                },
            },
            {
                path: 'edit/:id',
                component: FormComponent,
                resolve: {
                    title: () => inject(EmployeeService).getTitle(),
                    level: () => inject(EmployeeService).getLevel(),
                    department: () => inject(EmployeeService).getDepartment(),
                    employeeType: () => inject(EmployeeService).getEmployeeType(),
                    workShift: () => inject(EmployeeService).getWorkShift(),
                    approval: () => inject(EmployeeService).getApproval(),
                },
            },
        ]
    },

] as Routes;
