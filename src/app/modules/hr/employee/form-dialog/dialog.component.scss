mat-label {
    font-weight: bold;
}

::ng-deep mat-form-field.readonly-field {
    background-color: #f5f5f5; /* สีเทา */
    pointer-events: none; /* ปิดการโต้ตอบ */
    opacity: 0.8;
}


/* Card styles for mobile */
@media (max-width: 768px) {
    .table-container table {
        display: none;
    }

    .card-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .card {
        border: 1px solid #000;
        padding: 1rem;
        background: #f9f9f9;
        border-radius: 8px;
    }

    .card div {
        margin-bottom: 0.5rem;
    }

    .card div span {
        font-weight: bold;
    }
}