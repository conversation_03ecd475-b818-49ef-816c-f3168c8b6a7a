<div class="w-full">
    <div class="flex flex-col md:flex-row  justify-between items-center mb-4">
        <!-- Title -->
        <h1 mat-dialog-title class="text-xl font-semibold px-0" *ngIf="this.data?.type === 'EDIT'">Modify Leave Quota
        </h1>
        <h1 mat-dialog-title class="text-xl font-semibold px-0" *ngIf="this.data?.type === 'VIEW'">View Leave Quota</h1>

        <!-- Actions -->
        <div mat-dialog-actions class="flex justify-end ml-auto px-0">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="goToEdit(data.employee.id)"
                *ngIf="this.data.type === 'VIEW'">
                Edit
            </button>
            <button class="px-6 ml-3 bg-red-500 text-white" mat-flat-button [color]="'red'" (click)="printPage()"
                *ngIf="this.data.type === 'VIEW'">
                Print to PDF
            </button>
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="initialLeave()"
                *ngIf="data?.value?.length <= 0 && this.data?.type === 'EDIT'">
                Initial
            </button>

        </div>
    </div>

    <div mat-dialog-content class="md:max-h-180 p-0">
        <form [formGroup]="form">
            <div class="flex-auto " id="printable-content">
                <div class="flex flex-col mb-2 md:flex border-t-2 border-gray-400">
                    <div class="grid md:grid-cols-2 grid-cols-1 gap-2">
                        <div class="md:w-full mt-4">
                            <p class="sm:text-sm md:text-lg font-semibold">Employee ID :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.code ?? '-'}}</span>
                            </p>
                            <p class="sm:text-sm md:text-lg font-semibold">Name :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.firstname}}
                                    {{data?.employee?.lastname}}</span></p>
                            <p class="sm:text-sm md:text-lg font-semibold">Date of birth :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.birthDate | date :
                                    'dd/MM/yyyy'}}
                                </span></p>
                            <p class="sm:text-sm md:text-lg font-semibold">Gender :
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.sex === 'male'"> Male</span>
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.sex === 'female'"> Female</span>
                            </p>
                            <p class="sm:text-sm md:text-lg font-semibold">Employee Status :
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.active === 'PER'">Permanent</span>
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.active === 'FTC'">Fixed Term Contract</span>
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.active === 'RES'">Resigned</span>
                            </p>
                            <p class="sm:text-sm md:text-lg font-semibold">Work Location :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.workShift?.name}}
                                </span></p>
                        </div>
                        <div class="md:w-full mt-4">
                            <p class="sm:text-sm md:text-lg font-semibold">Title :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.title?.name ??
                                    '-'}}</span>
                            </p>
                            <p class="sm:text-sm md:text-lg font-semibold">Level :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.level?.name ??
                                    '-'}}</span></p>
                            <p class="sm:text-sm md:text-lg font-semibold">Department :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.department?.name ??
                                    '-'}}
                                </span></p>
                            <p class="sm:text-sm md:text-lg font-semibold">Default Apporver :<span
                                    class="sm:text-md font-bold text-green-900"> {{data?.employee?.head?.firstname}}
                                    {{data?.employee?.head?.lastname}}
                                </span></p>
                            <p class="sm:text-sm md:text-lg font-semibold">
                                Date Passed Probation :<span class="sm:text-md font-bold text-green-900">
                                    {{data?.employee?.passProbationDate | date : 'dd/MM/yyyy'}}
                                </span></p>
                            <p class="sm:text-sm md:text-lg font-semibold">
                                Probation Status :
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.employeeStatus === 'under_probation'">Under Probation</span>
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.employeeStatus === 'passed_probation'">Passed
                                    Probation</span>
                                <span class="sm:text-md font-bold text-green-900"
                                    *ngIf="data?.employee?.employeeStatus === 'did_not_pass_probation'">Did Not Pass
                                    Probation</span>
                            </p>

                        </div>
                        <p class="col-span-2 sm:text-sm md:text-lg font-semibold"><span
                                class="sm:text-md font-bold text-green-900"></span>Your leave period is from :
                            <span class="sm:text-md font-bold text-green-900">{{this.datePeriod ?? '-'}}</span>
                        </p>
                    </div>

                    <table
                        class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black my-5 "
                        formArrayName="employeeLeavePermissions" *ngIf="this.data.type === 'EDIT'">
                        <thead class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                            <tr class="border-[1px] border-black">
                                <th scope="col" class="px-2 py-2 w-2/10">
                                    Leave Type
                                </th>
                                <th scope="col" class="px-2 py-2 w-2/10">
                                    Entitlement
                                </th>
                                <th scope="col" class="px-2 py-2 w-2/10">
                                    Excess
                                </th>
                                <th scope="col" class="px-2 py-2 w-2/10">
                                    Taken
                                </th>
                                <th scope="col" class="px-2 py-2 w-2/10 ">
                                    Remain
                                </th>
                                <th scope="col" class="px-2 py-2 w-2/10 ">
                                    Expire
                                </th>
                            </tr>

                        </thead>
                        <tbody>
                            <ng-container>
                                <tr class="font-bold" *ngFor="let item of leavePermissions.controls; let i = index"
                                    [formGroupName]="i">

                                    <td class="px-2 py-2 text-md">
                                        <div
                                            class="px-2 py-1 rounded flex items-center justify-between gap-2 h-full w-full text-md text-gray-700">

                                            <span class="whitespace-nowrap">
                                                {{ this.form.value.employeeLeavePermissions[i].leaveTypeName ?? '-' }}
                                            </span>
                                            <ng-container *ngIf="this.data?.value[i]?.leaveType?.code === 'LT008'">
                                                <mat-checkbox formControlName="toNextPeriod">Permanent</mat-checkbox>
                                            </ng-container>
                                        </div>
                                    </td>
                                    <td class="px-2 py-2 text-md">
                                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                            <input matInput [placeholder]="''" formControlName="qtyDay" type="number"
                                                step="0.5" min="0">
                                        </mat-form-field>
                                    </td>
                                    <td class="px-2 py-2 text-md">
                                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                            <input matInput [placeholder]="''" formControlName="excessDay" type="number"
                                                step="0.5" min="0">
                                        </mat-form-field>
                                    </td>
                                    <td class="px-2 py-2 text-md text-red-500 ">
                                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                            <input matInput [placeholder]="''" formControlName="usedDay" type="number"
                                                step="0.5" min="0">
                                        </mat-form-field>
                                    </td>
                                    <td class="px-2 py-2 text-md">
                                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                            <input matInput [placeholder]="''" formControlName="remain" type="number">
                                        </mat-form-field>
                                    </td>
                                    <td class="px-2 py-2 text-md text-red-500 ">
                                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                            <input matInput [matDatepicker]="picker" placeholder="Select Date"
                                                formControlName="expireDate" (dateChange)="GetReport()">
                                            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                                            <mat-datepicker #picker></mat-datepicker>
                                        </mat-form-field>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                    <div *ngIf="this.data.type === 'VIEW'">
                        <!-- Table for larger screens -->
                        <div class="hidden md:block print:block">
                            <table
                                class="w-full text-md text-left text-gray-500 dark:text-gray-400 border border-black my-5">
                                <thead
                                    class="text-md text-gray-700 uppercase bg-gray-200 dark:bg-gray-700 dark:text-gray-400">
                                    <tr class="border border-black">
                                        <th scope="col" class="px-4 py-2 w-2/10">Leave Type</th>
                                        <th scope="col" class="px-4 py-2 w-2/10">Entitlement</th>
                                        <th scope="col" class="px-4 py-2 w-2/10">Excess</th>
                                        <th scope="col" class="px-4 py-2 w-2/10">Taken</th>
                                        <th scope="col" class="px-4 py-2 w-2/10">Remain</th>
                                        <th scope="col" class="px-4 py-2 w-2/10">Expire</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of leaves" class="border border-black">
                                        <td class="px-4 py-2">{{ item?.leaveType?.name }}</td>
                                        <td class="px-4 py-2 text-center">{{ item?.qtyDay }}</td>
                                        <td class="px-4 py-2 text-center">{{ item?.excessDay }}</td>
                                        <td class="px-4 py-2 text-center">{{ item?.usedDay }}</td>
                                        <td class="px-4 py-2 text-center">{{ calRemain(item) }}</td>
                                        <td class="px-4 py-2 text-center">{{ item?.expireDate }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Cards for smaller screens -->
                        <div class="block md:hidden space-y-4 mt-2 print:hidden">
                            <div *ngFor="let item of leaves" class="border border-black p-4 bg-gray-100 rounded-lg">
                                <div class="mb-2"><span class="font-semibold">Leave Type:</span> {{
                                    item?.leaveType?.name }}</div>
                                <div class="mb-2"><span class="font-semibold">Entitlement:</span> {{ item?.qtyDay }}
                                </div>
                                <div class="mb-2"><span class="font-semibold">Excess:</span> {{ item?.excessDay }}</div>
                                <div class="mb-2"><span class="font-semibold">Taken:</span> {{ item?.usedDay }}</div>
                                <div class="mb-2"><span class="font-semibold">Remain:</span> {{ calRemain(item) }}</div>
                                <div><span class="font-semibold">Expire:</span> {{ item?.expireDate }}</div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </form>

    </div>
    <!-- <div> check : {{this.form.value | json}}</div> -->
    <div mat-dialog-actions class="flex justify-end" *ngIf="this.data.type === 'EDIT'">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
            Save
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="onClose()">
            Cancel
        </button>
    </div>
    <div mat-dialog-actions class="flex justify-end" *ngIf="this.data.type === 'VIEW'">
        <button class="px-6 ml-3" mat-flat-button [color]="'accent'" (click)="onClose()">
            Close
        </button>
    </div>
</div>