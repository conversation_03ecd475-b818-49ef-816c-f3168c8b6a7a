import { Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { EmployeeService } from '../page.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DateTimeToSQL } from 'app/helper';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { Router } from '@angular/router';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
    selector: 'app-employee-adjust-leave',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        MatSlideToggleModule,
        MatDatepickerModule,
        MatCheckboxModule
    ]
})
export class DialogFormAdjustLeave implements OnInit {

    form: FormGroup;
    branch: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: any = {};
    addForm: FormGroup;
    datePeriod: string = '';
    leaves: any[] = []
    currentUserRole: string = localStorage.getItem('role')
    
    constructor(
        private dialogRef: MatDialogRef<DialogFormAdjustLeave>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        private fuseConfirmationService: FuseConfirmationService,
        private _service: EmployeeService,
        private toastr: ToastrService,
        private _router: Router
    ) {
        
        console.log(this.data.value);
        
    }

    ngOnInit(): void {
        this.form = this.FormBuilder.group({
            id: null,
            employeeId: null,
            startPeriod: null,
            endPeriod: null,
            employeeLeavePermissions: this.FormBuilder.array([])
        });

        if (this.data.type === 'EDIT') {
            if (this.data.value.length > 0) {
                this.form.patchValue({
                    startPeriod: this.data?.value[0]?.startPeriod,
                    endPeriod: this.data?.value[0]?.endPeriod,

                })
                this.datePeriod = DateTimeToSQL(this.data?.value[0]?.startPeriod, 'dd/MM/yyyy') + ' to ' + DateTimeToSQL(this.data?.value[0]?.endPeriod, 'dd/MM/yyyy')
            } else {
                this.datePeriod = '-'
            }


            for (const item of this.data.value) {
                this.addLeavePermissionbyid(item)
            }
        } else {
            this.leaves = this.data.value
            console.log(this.leaves);

            this.datePeriod = DateTimeToSQL(this.data?.value[0]?.startPeriod, 'dd/MM/yyyy') + ' to ' + DateTimeToSQL(this.data?.value[0]?.endPeriod, 'dd/MM/yyyy')
        }

        this.leavePermissions.controls.forEach((group: FormGroup, index: number) => {
            const qtyControl = group.get('qtyDay');
            const usedControl = group.get('usedDay');
            const remainControl = group.get('remain');
        
            const calculateRemain = () => {
              const qty = +qtyControl?.value || 0;
              const used = +usedControl?.value || 0;
              remainControl?.setValue(qty - used, { emitEvent: false });
            };
        
            const validateInput = (control: AbstractControl | null, fieldName: string) => {
              control?.valueChanges.subscribe(value => {
                if (+value < 0) {
                  control.setValue(0, { emitEvent: false });
                  this.toastr.error(`${fieldName} cannot be negative`)
                } else {
                  calculateRemain();
                }
              });
            };
        
            validateInput(qtyControl, 'Entitlement');
            validateInput(usedControl, 'Taken');
          });

        this.setupValueChanges()
    }

    calRemain(data: any) {
        const value = data.qtyDay - data.usedDay
        return value
    }

    addLeavePermissionbyid(data?: any) {
        console.log(data);
        const isEditable = ['ADMIN','ADMIN_HR'].includes(this.currentUserRole); // currentUserRole เป็น role ปัจจุบันของ user

        const arrayValue = this.FormBuilder.group({
            toNextPeriod: data.toNextPeriod ?? false,
            id: data?.id,
            leaveTypeId: data?.leaveType?.id,
            leaveTypeName: data?.leaveType?.name,
            qtyDay: data.qtyDay,
            excessDay: data?.excessDay,
            usedDay: [{ value: data.usedDay, disabled: !isEditable }],
            remain: [{ value: (data.qtyDay + data.excessDay) - data.usedDay, disabled: true }],
            expireDate: data?.expireDate,
        });
        this.leavePermissions.push(arrayValue);

    }

    get leavePermissions() {
        return this.form.get("employeeLeavePermissions") as FormArray;
    }

    Submit() {
        let formValue = this.form.getRawValue()
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to save data ?",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "primary"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.updateLeavePermission(this.data?.employee?.id, formValue).subscribe({
                        error: (err) => {
                            this.toastr.error(err.error.message[0])
                        },
                        complete: () => {
                            this.toastr.success('Successed')
                            this.dialogRef.close(true)
                        },
                    });
                }
            }
        )
    }

    setupValueChanges(): void {
        // console.log(1111);

        // วนลูปฟังการเปลี่ยนแปลงของแต่ละ FormGroup ใน FormArray
        this.leavePermissions.controls.forEach((control: AbstractControl) => {
            const group = control as FormGroup;  // Type Assertion
            // ฟังการเปลี่ยนแปลงของ qtyDay
            group.get('qtyDay')?.valueChanges.subscribe((qtyDay) => {
                this.updateRemain(group);
            })

            // ฟังการเปลี่ยนแปลงของ excessDay
            group.get('excessDay')?.valueChanges.subscribe((excessDay) => {
                this.updateRemain(group);
            })
        });
    }

    updateRemain(group: FormGroup<any>): void {
        const qtyDay = group.get('qtyDay')?.value || 0; // ดึงค่าของ qtyDay
        const usedDay = group.get('usedDay')?.value || 0; // ดึงค่าของ usedDay
        const excessDay = group.get('excessDay')?.value || 0; // ดึงค่าของ ecessDay
        const remain = this.calculateLeave(qtyDay, excessDay, usedDay); // คำนวณ remain
        group.get('remain')?.setValue(remain, { emitEvent: false }); // ตั้งค่า remain ใหม่
    }

    calculateLeave(qtyDay: number, excessDay: number, usedDay: number): number {
        return Math.max((qtyDay + excessDay) - usedDay, 0);
    }

    initialLeave() {
        this._service.getInitialLeave(this.data?.employee?.id).subscribe((resp: any) => {
            this.datePeriod = DateTimeToSQL(resp?.startPeriod, 'dd/MM/yyyy') + ' to ' + DateTimeToSQL(resp?.endPeriod, 'dd/MM/yyyy')
            const data = resp.employeeLeavePermissions
            this.form.patchValue({
                ...resp
            })
            const isEditable = ['ADMIN'].includes(this.currentUserRole); // currentUserRole เป็น role ปัจจุบันของ user
            data.forEach(element => {
                const arrayValue = this.FormBuilder.group({
                    id: null,
                    leaveTypeId: element?.leaveTypeId,
                    leaveTypeName: element?.leaveTypeName,
                    qtyDay: element.qtyDay,
                    excessDay: element?.excessDay,
                    usedDay: [{ value: element.usedDay, disabled: !isEditable }],
                    remain: [{ value: (element.qtyDay + element.excessDay) - element.usedDay, disabled: true }],
                    expireDate: resp?.expireDate,
                    toNextPeriod: data?.toNextPeriod ?? false,
                });
                this.leavePermissions.push(arrayValue);
            });
            this.setupValueChanges()
        })
    }

    onClose() {
        this.dialogRef.close()
    }

    goToEdit(id: any) {
        this.onClose()
        this._router.navigate(['employee/edit/' + id])
    }

    printPage() {
        const printContents = document.getElementById('printable-content')?.innerHTML;
        if (printContents) {
            const printWindow = window.open('', '', 'height=600,width=800');
            if (printWindow) {
                printWindow.document.write(`
              <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF</title>
    <style>
        body {
            font-family: 'Open Sans', Arial, sans-serif;
            margin: 20px;
        }

        .container {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 10px;
            width: 800px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .header div {
            width: 100%;
        }

        .title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #797F8B;
        }

        strong {
            color: #1E552F;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        table {
            border: 1px solid #000000;
        }

        th,
        td {
            border-top: 1px solid #000000;
            border-bottom: 1px solid #000000;
        }

        th,
        td {
            padding: 8px 10px;
            text-align: left;
        }

        th {
            background-color: #E2E9F0;
            color: #2D3B4F;
            font-size: 15px;
        }

        td {
            color: #5B6C85;
            text-align: center;
        }

        .left {
            text-align: left;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="title">
                <div>Employee ID : <strong> ${this.data?.employee?.code} </strong></div>
                <div>Name : <strong> ${this.data?.employee?.firstname} ${this.data?.employee?.lastname} </strong></div>
                <div>Date of birth : <strong> ${this.data?.employee?.birthDate} </strong></div>
                <div>Gender : <strong> ${this.data?.employee?.sex} </strong></div>
              <div>
                    Employee Status :
                    <strong>
                        ${
                            this.data?.employee.active == 'PER' ? 'Permanent' :
                            this.data?.employee.active == 'FTC' ? 'Fixed Term Contract' :
                            this.data?.employee.active == 'RES' ? 'Resigned' : '&nbsp;'
                        }
                    </strong>
                </div>
                <div>Work Location : <strong> ${this.data?.employee?.workShift?.name} </strong></div>
            </div>
            <div class="title">
                <div>Title : <strong> ${this.data?.employee?.title?.name} </strong></div>
                <div>Level : <strong> ${this.data?.employee?.level?.name} </strong></div>
                <div>Department : <strong> ${this.data?.employee?.department?.name} </strong></div>
                <div>Default Approver : ${this.data?.employee?.head?.firstname} ${this.data?.employee?.head?.lastname} </div>
                <div>Date Passed Probation : ${(this.data?.employee?.passProbationDate) ? DateTimeToSQL(this.data?.employee?.passProbationDate, 'dd/MM/yyyy') : '&nbsp;'} </div>
                <div>Probation Status : <strong> ${this.data?.employee?.employeeStatus} </strong></div>
            </div>
        </div>
        <div class="title">Your leave period is from : <strong> ${this.datePeriod ?? '&nbsp;'} </strong></div>
        <table>
            <thead>
                <tr>
                    <th>LEAVE TYPE</th>
                    <th>ENTITLEMENT</th>
                    <th>EXCESS</th>
                    <th>TAKEN</th>
                    <th>REMAIN</th>
                    <th>EXPIRE</th>
                </tr>
            </thead>
            <tbody>
                ${this.data?.value.map((item, index) => `
                    <tr>
                        <td class="left">${item.leaveType?.name || ''}</td>
                        <td> ${item.qtyDay || 0} </td>
                        <td> ${item.excessDay || 0} </td>
                        <td> ${item.usedDay || 0} </td>
                        <td> ${item.qtyDay - item.usedDay || 0} </td>
                        <td> ${item.expireDate || 0} </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
</body>
</html>
            `);
                printWindow.document.close();
                printWindow.focus();
                printWindow.print();
            }
        }
    }

}
