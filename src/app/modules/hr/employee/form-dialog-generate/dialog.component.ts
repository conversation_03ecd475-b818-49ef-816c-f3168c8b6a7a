import { Observable, Subscription } from 'rxjs';
import { Component, OnInit, OnChanges, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import {
    MatDialog,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatDialogClose,
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, FormsModule, Validators } from '@angular/forms';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { EmployeeService } from '../page.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DateTimeToSQL } from 'app/helper';

@Component({
    selector: 'app-employee-generate',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [CommonModule, DataTablesModule, MatIconModule, MatFormFieldModule, MatInputModule,
        FormsModule, MatToolbarModule,
        MatButtonModule,
        MatDialogTitle,
        MatDialogContent,
        MatDialogActions,
        MatDialogClose,
        MatSelectModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        MatRadioModule,
        MatSlideToggleModule
    ]
})
export class DialogFormGenerate implements OnInit {

    form: FormGroup;
    branch: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: any = {};
    addForm: FormGroup;
    datePeriod: string = '';

    code: Observable<any>;

    constructor(
        private dialogRef: MatDialogRef<DialogFormGenerate>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        private fuseConfirmationService: FuseConfirmationService,
        private _service: EmployeeService,
        private toastr: ToastrService,
    )
    {

    }

    ngOnInit(): void {
        this.form = this.FormBuilder.group({
            code: [null, Validators.required]
        });

        this.code = this._service.getCode();
    }

    Submit() {
        if (this.form.invalid) {
            return;
        }

        let formValue = this.form.getRawValue()
        this._service.generateCode(formValue.code).subscribe({
            next: (resp: any) => {
                this.toastr.success('Successed');
                this.dialogRef.close(resp.data);
            },
            error: (err: any) => {
                console.error('Error:', err); // แสดง Error ใน console
                this.toastr.error('An error occurred while generating the code');
            }
        });
    }

    onClose() {
        this.dialogRef.close()
    }
}
