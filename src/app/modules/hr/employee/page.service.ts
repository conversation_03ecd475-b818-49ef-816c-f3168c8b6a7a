import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { contacts } from './mock-data';
import { removeEmpty } from 'app/helper';
import { param } from 'jquery';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _title: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _level: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _department: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _leaveType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _employeeType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _workShift: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _position: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _permission: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }



  getAll(dataTablesParameters: any) {
    console.log(dataTablesParameters);
    
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/employee/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }



  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/employee`, data)
  }

  update(id: number, data: object) {
    return this.http.put(`${environment.apiUrl}/api/employee/${id}`, data)
  }

  getUserId(id: any) {
    return this.http.get(`${environment.apiUrl}/api/employee/` + id).pipe(
      tap((resp: any) => {
        this._user.next(resp.data);
      }),
    )
  }
  getEmployee() {
    return this.http.get(environment.apiUrl + '/api/employee').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
      map((resp: any) => {
        resp.forEach((e: any, i: number) => e.fullName = e.firstname + ' ' + e.lastname);
        return resp;
      })
    )
  }

  getTitle() {
    return this.http.get(environment.apiUrl + '/api/title').pipe(
      tap((resp: any) => {
        this._title.next(resp);
      }),
    )
  }
  getLevel() {
    return this.http.get(environment.apiUrl + '/api/level').pipe(
      tap((resp: any) => {
        this._level.next(resp);
      }),
    )
  }
  getDepartment() {
    return this.http.get(environment.apiUrl + '/api/department').pipe(
      tap((resp: any) => {
        this._department.next(resp);
      }),
    )
  }
  getLeaveType() {
    return this.http.get(environment.apiUrl + '/api/leave-type').pipe(
      tap((resp: any) => {
        this._leaveType.next(resp);
      }),
    )
  }
  getEmployeeType() {
    return this.http.get(environment.apiUrl + '/api/employee-type').pipe(
      tap((resp: any) => {
        this._employeeType.next(resp);
      }),
    )
  }
  getWorkShift() {
    return this.http.get(environment.apiUrl + '/api/work-shift').pipe(
      tap((resp: any) => {
        this._workShift.next(resp);
      }),
    )
  }

  getApproval() {
    return this.http.get(environment.apiUrl + '/api/appove-list').pipe(
      tap((resp: any) => {
        this._workShift.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/employee/' + id)
  }

  checkEmployeeCode(employeeCode: string): Observable<boolean> {
    return this.http.get<{ exists: boolean }>(environment.apiUrl + `/api/employee/check-code/${employeeCode}`).pipe(
      map((response) => response.exists)
    );
  }

  checkUsername(username: string): Observable<boolean> {
    return this.http.get<{ exists: boolean }>(environment.apiUrl + `/api/employee/check-username/${username}`).pipe(
      map((response) => response.exists)
    );
  }

  getLeavePermissionByEmp(id: number) {
    return this.http.get(`${environment.apiUrl}/api/employee/${id}/leave-permission`)
  }
  getInitialLeave(id: number) {
    return this.http.get(`${environment.apiUrl}/api/employee/${id}/leave-permission/initial`)
  }
  updateLeavePermission(id: number, data: any) {
    return this.http.put(`${environment.apiUrl}/api/employee/${id}/leave-permission/update`, data)
  }

  generateCode(id: any) {
    return this.http.get(`${environment.apiUrl}/api/employee/auto-code`, {
      params: {
        t: id
      }
    })
  }

  getCode() {
    return this.http.get(environment.apiUrl + '/api/company-for-employee').pipe()
  }
}
