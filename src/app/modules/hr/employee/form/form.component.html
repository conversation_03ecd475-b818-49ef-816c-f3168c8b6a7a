<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a class="whitespace-nowrap text-primary-500 cursor-pointer" 
                        >Employee list</a
                    >
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon
                        class="text-secondary icon-size-5"
                        [svgIcon]="'heroicons_mini:chevron-right'"
                    ></mat-icon>
                    <a class="ml-1 text-primary-500" *ngIf="!this.Id">Create new employee</a>
                    <a class="ml-1 text-primary-500" *ngIf="this.Id">Edit employee</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Create New Employee
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Edit Employee
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-6 sm:p-10">
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
                [formGroup]="form">
                <div class="flex flex-col md:flex-row gap-4 justify-center">
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0" *ngIf="!this.Id">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-2/3">
                                    <mat-label>Employee ID</mat-label>
                                    <input matInput [formControlName]="'code'" readonly
                                        [placeholder]="'Please enter employee ID'">
                                        @if (form.get('code').hasError('duplicate')) {
                                            <mat-error>
                                                Employee code already exists in the system.
                                            </mat-error>
                                        }
                                </mat-form-field>
                                <div class="w-1/3">
                                    <button class="px-6 mt-6" mat-flat-button [color]="'primary'" (click)="openDialogGenerate();">
                                        Generate
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0" *ngIf="this.Id">
                            <div class="flex flex-row justify-between md:min-w-full px-3 mb-2 md:mb-0 gap-2" >
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Employee ID</mat-label>
                                    <input matInput [formControlName]="'code'" readonly
                                        [placeholder]="'Please enter employee ID'">
                                        @if (form.get('code').hasError('duplicate')) {
                                            <mat-error>
                                                Employee code already exists in the system.
                                            </mat-error>
                                        }
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Firstname</mat-label>
                                    <input matInput [formControlName]="'firstname'" [placeholder]="'Please enter firstname'">
                                    @if (form.get('firstname').hasError('required')) {
                                        <mat-error> Firstname is required </mat-error>
                                        }
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Lastname</mat-label>
                                    <input matInput [formControlName]="'lastname'" [placeholder]="'Please enter lastname'">
                                    @if (form.get('lastname').hasError('required')) {
                                        <mat-error> Lastname is required </mat-error>
                                        }
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Username (only lowercase a-z)</mat-label>
                                    <input matInput [formControlName]="'username'"
                                        [placeholder]="'Please enter username'">
                                        <mat-error *ngIf="form.get('username').hasError('pattern')">
                                            Username must contain only lowercase letters (a-z).
                                        </mat-error>
                                        <mat-error *ngIf="form.get('username').hasError('required')">
                                            Username is required.
                                        </mat-error>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Date of birth</mat-label>
                                    <input matInput [matDatepicker]="birthDate" [formControlName]="'birthDate'" placeholder="birth date">
                                    <mat-datepicker-toggle matSuffix [for]="birthDate"></mat-datepicker-toggle>
                                    <mat-datepicker #birthDate></mat-datepicker>
                                  </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Email Address</mat-label>
                                    <input id="email" matInput [formControlName]="'email'" />
                                    @if (form.get('email').hasError('required')) {
                                    <mat-error> Email is required </mat-error>
                                    }
                                    @if (form.get('email').hasError('email')) {
                                    <mat-error>
                                        Please enter a valid email.
                                    </mat-error>
                                    }
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >Work Location</mat-label>
                                    <mat-select [formControlName]="'employeeTypeId'">
                                        <mat-option *ngFor="let item of this.employeeType;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="flex flex-col md:flex-row -mx-3 md:flex mb-2 md:mb-0 w-full">
                            
                            <div class="flex flex-col w-full md:w-full px-3 mb-2 md:mb-0">
                                <mat-label class="font-bold">Employee Status</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="active" >
                                    <mat-radio-button [value]="'PER'">Permanent </mat-radio-button>
                                    <mat-radio-button [value]="'FTC'">Fixed Term Contract</mat-radio-button>
                                    <mat-radio-button [value]="'RES'">Resigned</mat-radio-button>
                                </mat-radio-group>
                            </div>
                         
                        </div>
                        <div class="flex flex-col md:flex-row -mx-3 md:flex mb-2 md:mb-0 w-full">
                            <div class="flex flex-col w-full md:w-1/2 px-3 mb-2 md:mb-0">
                                <mat-label class="font-bold">Gender</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="sex">
                                    <mat-radio-button [value]="'male'">Male</mat-radio-button>
                                    <mat-radio-button [value]="'female'">Female </mat-radio-button>
                                </mat-radio-group>
                            </div>
                          
                            <div class="flex flex-col w-full md:w-1/2 px-3 mb-2 md:mb-0">
                                <mat-label class="font-bold">Approver</mat-label>
                                <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="isApprover">
                                    <mat-radio-button [value]="true">Yes </mat-radio-button>
                                    <mat-radio-button [value]="false">No </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-1/3">
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >Title</mat-label>
                                    <mat-select [formControlName]="'titleId'">
                                        <mat-option *ngFor="let item of this.title;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label >Level</mat-label>
                                    <mat-select [formControlName]="'levelId'">
                                        <mat-option *ngFor="let item of this.level;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label>Department</mat-label>
                                    <mat-select [formControlName]="'departmentId'">
                                        <mat-option *ngFor="let item of this.department;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
                                    <mat-label>Default Approver</mat-label>
                                    <input 
                                        matInput 
                                        [formControl]="approverFilter" 
                                        [matAutocomplete]="approverAutoComplete" 
                                        placeholder="Search Approver" 
                                    />
                                    <mat-autocomplete 
                                        #approverAutoComplete="matAutocomplete" 
                                        (optionSelected)="onSelectApprover($event.option.value, 'manual')">
                                        <mat-option *ngFor="let item of filterApprover | async" [value]="item">
                                            {{item.firstname}} {{item.lastname}}
                                        </mat-option>
                                    </mat-autocomplete>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Date Start</mat-label>
                                    <input matInput [matDatepicker]="registerDate" [formControlName]="'registerDate'" placeholder="Date" (dateChange)="changeDate()">
                                    <mat-datepicker-toggle matSuffix [for]="registerDate"></mat-datepicker-toggle>
                                    <mat-datepicker #registerDate></mat-datepicker>
                                  </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-2 md:mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Date Passed Probation</mat-label>
                                    <input matInput [matDatepicker]="passProbationDate" [formControlName]="'passProbationDate'" placeholder="Date">
                                    <mat-datepicker-toggle matSuffix [for]="passProbationDate"></mat-datepicker-toggle>
                                    <mat-datepicker #passProbationDate></mat-datepicker>
                                  </mat-form-field>
                            </div>
                        </div>
                        <div class="-mx-3 md:flex mb-0">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                                    <mat-label>Work Period</mat-label>
                                    <mat-select [formControlName]="'workShiftId'">
                                        <mat-option *ngFor="let item of this.workShift;" value="{{item.id}}">
                                            {{item.name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="flex flex-col w-full md:w-full px-3 mb-2 md:mb-0">
                            <mat-label class="font-bold">Probation Status</mat-label>
                            <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="employeeStatus" (change)="changeEmpStatus()">
                                <mat-radio-button [value]="'under_probation'">Under Probation </mat-radio-button>
                                <mat-radio-button [value]="'passed_probation'">Passed Probation</mat-radio-button>
                                <mat-radio-button [value]="'did_not_pass_probation'">Did Not Pass Probation</mat-radio-button>
                            </mat-radio-group>
                        </div>
                  
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Save
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
            
        </div>
    </div>
</div>