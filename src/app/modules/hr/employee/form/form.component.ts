import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    AbstractControl,
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { EmployeeService } from '../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { debounceTime, forkJoin, lastValueFrom, switchMap, map, of, Observable, ReplaySubject, Subject } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';
import { DateTimeToISO, DateTimeToJSDate, DateTimeToSQL } from 'app/helper';
import { catchError, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { DateTime } from 'luxon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DialogFormGenerate } from '../form-dialog-generate/dialog.component';
@Component({
    selector: 'form-user',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule,
        MatAutocompleteModule
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;

    item1Data: any = [];
    item2Data: any = [];
    subCategory: any = [];
    itemSupplier: any = [];

    itemBrand: any = [];
    itemBrandModel: any = [];
    itemCC: any = [];
    itemColor: any = [];

    formData: FormGroup;
    formData2: FormGroup;

    files: File[] = [];
    warehouseData: any;
    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    position: any[] = [];

    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;

    ///approver filter
    approverFilter = new FormControl('');
    filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    approval: any[] = [];
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: EmployeeService,
        private dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _toast: ToastrService,
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        this.title = this._activatedRoute.snapshot.data.title;
        this.level = this._activatedRoute.snapshot.data.level;
        this.department = this._activatedRoute.snapshot.data.department;
        this.leaveType = this._activatedRoute.snapshot.data.leaveType;
        this.employeeType = this._activatedRoute.snapshot.data.employeeType;
        this.workShift = this._activatedRoute.snapshot.data.workShift;
        this.approval = this._activatedRoute.snapshot.data.approval;
        this.filterApprover.next(this.approval.slice());
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            id: null,
            code: [
                '',
                [Validators.required],
                [this.employeeCodeValidator.bind(this)] // ใช้ Async Validator
            ],
            firstname: ['', Validators.required],
            lastname: ['', Validators.required],// บังคับให้เป็นตัวอักษรตัวเล็กเท่านั้น
            username: [
                '',
                [
                    Validators.required,
                    Validators.pattern(/^[a-z]+$/) // บังคับให้เป็นตัวอักษรตัวเล็กเท่านั้น
                ],
            ],
            email: ['', [Validators.required, Validators.email]],
            birthDate: null,
            registerDate: null,
            passProbationDate: null,
            active: true,
            titleId: null,
            levelId: null,
            departmentId: null,
            headId: null,
            employeeTypeId: null,
            workShiftId: null,
            sex: null,
            isApprover: false,
            employeeStatus: 'under_probation',
            status: 'PER'

        })

        if (this.Id) {
            this._Service.getUserId(this.Id).subscribe((resp: any) => {
                this.itemData = resp
                if (this.itemData?.head) {
                    this.approverFilter.setValue(`${this.itemData?.head?.firstname ?? ''} ${this.itemData?.head?.lastname ?? ''}`);
                }
                this.form.patchValue({
                    ...this.itemData,
                    departmentId: this.itemData?.department?.id?.toString(),
                    employeeTypeId: this.itemData?.employeeType?.id?.toString(),
                    workShiftId: this.itemData?.workShift?.id?.toString(),
                    levelId: this.itemData?.level?.id?.toString(),
                    titleId: this.itemData?.title?.id?.toString(),
                    headId: this.itemData?.head?.id?.toString(),
                })
            })
        }

        this.approverFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe(() => {
                this._filterBrand();
            });
    }

    changeDate() {
        const date: any = DateTimeToISO(this.form.value.registerDate, 'yyyy-MM-dd');
        if (date) {
            const formatDate: DateTime = DateTime.fromISO(date);
            const jsDate: Date = formatDate.toJSDate();
            const luxonDate = DateTime.fromJSDate(jsDate);
            const probationDate = luxonDate.plus({ days: 90 });
            const formattedDate = probationDate.toFormat('yyyy-MM-dd');
            this.form.patchValue({
                passProbationDate: formattedDate
            })
        } else {
            this._toast.error('no register date')
        }
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }



    onSelect(event: any) {
        this.files.push(...event.addedFiles);
        // Trigger Image Preview
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);
    }

    onRemove(event: any) {
        this.files.splice(this.files.indexOf(event), 1);
    }

    Submit(): void {
        let formValue = this.form.value;
        if (this.form.value.birthDate) {
            formValue.birthDate = DateTimeToISO(this.form.value.birthDate, 'yyyy-MM-dd');
        } else {
            formValue.birthDate = null;  // ถ้าไม่มีค่าให้เป็นค่าว่าง
        }

        if (this.form.value.registerDate) {
            formValue.registerDate = DateTimeToISO(this.form.value.registerDate, 'yyyy-MM-dd');
        } else {
            formValue.registerDate = null;  // ถ้าไม่มีค่าให้เป็นค่าว่าง
        }

        if (this.form.value.passProbationDate) {
            formValue.passProbationDate = DateTimeToISO(this.form.value.passProbationDate, 'yyyy-MM-dd');
        } else {
            formValue.passProbationDate = null;  // ถ้าไม่มีค่าให้เป็นค่าว่าง
        }
        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit value",
                message: "Do you want to edit value? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    this._Service.update(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['employee']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add value",
                message: "Do you want to save data ?",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {


                    this._Service.create(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['employee']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }


    backTo() {
        this._router.navigate(['employee'])
    }

    // ฟังก์ชัน Async Validator
    employeeCodeValidator(control: AbstractControl) {
        return control.valueChanges.pipe(
            debounceTime(500), // หน่วงเวลาการตรวจสอบ
            switchMap((value) => this._Service.checkEmployeeCode(value)), // เรียก API เพื่อตรวจสอบรหัส
            map((isDuplicate) => (isDuplicate ? { duplicate: true } : null)) // ถ้าซ้ำจะให้ error เป็น duplicate
        );
    }


    // ฟังก์ชันสร้าง username และตรวจสอบความซ้ำ
    usernameValidator(control: AbstractControl) {
        return control.valueChanges.pipe(
            debounceTime(500),
            switchMap(() => this.generateUniqueUsername()),
            map((username) => username ? { duplicate: true } : null)
        );
    }

    // แก้ไขฟังก์ชัน generateUniqueUsername เพื่อใช้งาน Observable อย่างถูกต้อง
    private generateUniqueUsername(): Observable<any> {
        const firstname = this.form.get('firstname')?.value?.toLowerCase() || '';
        const lastname = this.form.get('lastname')?.value?.toLowerCase() || '';
        let username = firstname;
        let index = 0;

        return this._Service.checkUsername(username).pipe(
            switchMap((isDuplicate: boolean) => {
                // ถ้า username ซ้ำ ให้เข้าสู่ loop เพื่อสร้าง username ใหม่
                while (isDuplicate && index < lastname.length) {
                    username = firstname + lastname[index];
                    index++;
                    return this._Service.checkUsername(username);
                }
                return of(null); // ถ้าไม่ซ้ำหรือหมดตัวอักษรแล้วให้ return null (ไม่มีความซ้ำ)
            }),
            map(isDuplicate => (isDuplicate ? { duplicate: true } : null)),
            catchError(() => of(null)) // กรณี error จะส่งค่า null กลับ
        );
    }

               /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

    protected _filterBrand() {
        if (!this.approval) {
            return;
        }
        let search = this.approverFilter.value;
        // console.log(search, 's');

        if (!search) {
            this.filterApprover.next(this.approval.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterApprover.next(
            this.approval.filter(item =>
                (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
                item.firstname.toLowerCase().includes(search) ||
                item.lastname.toLowerCase().includes(search)
            )
        );
    }

    onSelectApprover(event: any, type: any) {
        if (!event) {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Approver Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                headId: selectedData.id,
            });
            this.approverFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
        } else {
            if (this.approverFilter.invalid) {
                this.approverFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Approver Found');
            return;
        }
    }

    openDialogGenerate() {
        const DialogRef = this.dialog.open(DialogFormGenerate, {
            disableClose: true,
            width: '500px',
            height: 'auto',
            enterAnimationDuration: 200,
            exitAnimationDuration: 100,
        });
        DialogRef.afterClosed().subscribe((result) => {
            if (result) {
                console.log(result, 'result')
                this.form.patchValue(
                    {
                        code: result
                    }
                )
            }
        });
    }

    changeEmpStatus() {
        if (this.form.value.employeeStatus === 'did_not_pass_probation') {
            this.form.patchValue({
                active: 'RES'
            })
        } else {
            this.form.patchValue({
                active: this.itemData?.active
            })
        }
    }
}
