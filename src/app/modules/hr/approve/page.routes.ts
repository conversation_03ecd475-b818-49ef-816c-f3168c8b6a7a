import { Routes } from '@angular/router';
import { ApprovalEmpComponent } from './page.component';
import { FormComponent } from './form/form.component';
import { inject } from '@angular/core'
import { ApprovalEmpService } from './page.service';

export default [
    {
        path     : 'list',
        component: ApprovalEmpComponent,
    
    },
    {
        path     : 'form',
        component: FormComponent,
        resolve: {
            department: () => inject(ApprovalEmpService).getDepartment()
        }, 
    },
    {
        path     : 'edit/:id',
        component: FormComponent,
        resolve: {
  
        }, 
    },
] as Routes;
