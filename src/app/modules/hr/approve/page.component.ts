import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { ApprovalEmpService } from './page.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
@Component({
    selector: 'app-page-approve',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class ApprovalEmpComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];

    data: any;
    constructor(
        private _service: ApprovalEmpService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activated: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder

    ) {

        this.form = this._fb.group({
            isApprover: true
        })


    }
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        let formValue = this.form.value
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,     // Set the flag
            scrollX: true,
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.isApprover': true,
                }
                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {

                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: 'No.',
                    data: 'no',
                    className: 'w-15 text-center'
                },
                {
                    title: 'Employee ID',
                    data: 'code',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Firstname',
                    data: 'firstname',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Lastname',
                    data: 'lastname',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Email',
                    data: 'email',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Date of birth',
                    data: function (row: any) {
                        return DateTimeToSQL(row.birthDate, 'dd/MM/yyyy')
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Position',
                    data: 'title.name',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Level',
                    data: 'level.name',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Department',
                    data: 'department.name',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Start Date',
                    data: function (row: any) {
                        return row.registerDate
                            ? DateTimeToSQL(row.registerDate, 'dd/MM/yyyy')
                            : '-';
                    },
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Status',
                    data: 'active',
                    defaultContent: '-',
                    className: 'text-left'
                },
                // {
                //     title: '',
                //     data: null,
                //     defaultContent: '',
                //     ngTemplateRef: {
                //         ref: this.btNg,
                //     },
                //     className: 'w-15 text-center'
                // }

            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    opendialogAdd() {
        this._router.navigate(['employee/form'])
    }

    openDialogEdit(id: any) {
        this._router.navigate(['employee/edit/' + id])

    }



    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to delete? ",
            message: "Please check the information. If the information is deleted, it cannot be retrieved.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('ดำเนินการลบสำเร็จ');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
    onChangeType() {
        this.rerender()
    }
}
