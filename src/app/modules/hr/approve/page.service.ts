import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { contacts } from './mock-data';
import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class ApprovalEmpService {

  private _user: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _title: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _level: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _department: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _leaveType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _employeeType: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _workShift: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _position: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _permission: BehaviorSubject<any[] | null> = new BehaviorSubject(null);



  constructor(private http: HttpClient) { }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length,filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/employee/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  



  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/employee`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/employee/${id}`, data)
  }

  getUserId(id:any) {
    return this.http.get(`${environment.apiUrl}/api/employee/`+ id).pipe(
      tap((resp: any) => {
        this._user.next(resp.data);
      }),
    )
  }
  getEmployee() {
    return this.http.get(environment.apiUrl + '/api/employee').pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }

  getTitle() {
    return this.http.get(environment.apiUrl + '/api/title').pipe(
      tap((resp: any) => {
        this._title.next(resp);
      }),
    )
  }
  getLevel() {
    return this.http.get(environment.apiUrl + '/api/level').pipe(
      tap((resp: any) => {
        this._level.next(resp);
      }),
    )
  }
  getDepartment() {
    return this.http.get(environment.apiUrl + '/api/department').pipe(
      tap((resp: any) => {
        this._department.next(resp);
      }),
    )
  }
  getLeaveType() {
    return this.http.get(environment.apiUrl + '/api/leave-type').pipe(
      tap((resp: any) => {
        this._leaveType.next(resp);
      }),
    )
  }
  getEmployeeType() {
    return this.http.get(environment.apiUrl + '/api/employee-type').pipe(
      tap((resp: any) => {
        this._employeeType.next(resp);
      }),
    )
  }
  getWorkShift() {
    return this.http.get(environment.apiUrl + '/api/work-shift').pipe(
      tap((resp: any) => {
        this._workShift.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/employee/' + id)
  }

  getEmployeeByDepartment(data: any) {
    return this.http.get(environment.apiUrl + '/api/get-employee', {params: {
      departmentId: data.id
    }}).pipe(
      tap((resp: any) => {
        this._user.next(resp);
      }),
    )
  }


}
