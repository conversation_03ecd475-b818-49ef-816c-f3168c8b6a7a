import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { ApprovalEmpService } from '../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { forkJoin, lastValueFrom } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';
import { DateTimeToISO, DateTimeToJSDate, DateTimeToSQL } from 'app/helper';
import { DualListComponent } from '../../dual-list/dual-list.component';

@Component({
    selector: 'form-approve',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule,
        DualListComponent
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit, OnDestroy {

    tab = 1;
    keepSorted = true;
    key: string;
    display: string;
    filter = false;
    source: Array<any>;
    confirmed: Array<any>;
    userAdd = '';
    disabled = false;
    
    sourceLeft = true;
    format: any = DualListComponent.DEFAULT_FORMAT;
  
    private sourceStations: Array<any>;
  
    private confirmedStations: Array<any>;
  
  
    private stations: Array<any> = [
      { key: 1, station: 'Antonito', state: 'CO' },
      { key: 2, station: 'Big Horn', state: 'NM' },
      { key: 3, station: 'Sublette', state: 'NM' },
      { key: 4, station: 'Toltec', state: 'NM' },
      { key: 5, station: 'Osier', state: 'CO' },
      { key: 6, station: 'Chama', state: 'NM' },
      { key: 7, station: 'Monero', state: 'NM' },
      { key: 8, station: 'Lumberton', state: 'NM' },
      { key: 9, station: 'Duice', state: 'NM' },
      { key: 10, station: 'Navajo', state: 'NM' },
      { key: 11, station: 'Juanita', state: 'CO' },
      { key: 12, station: 'Pagosa Jct', state: 'CO' },
      { key: 13, station: 'Carracha', state: 'CO' },
      { key: 14, station: 'Arboles', state: 'CO' },
      { key: 15, station: 'Solidad', state: 'CO' },
      { key: 16, station: 'Tiffany', state: 'CO' },
      { key: 17, station: 'La Boca', state: 'CO' },
      { key: 18, station: 'Ignacio', state: 'CO' },
      { key: 19, station: 'Oxford', state: 'CO' },
      { key: 20, station: 'Florida', state: 'CO' },
      { key: 21, station: 'Bocea', state: 'CO' },
      { key: 22, station: 'Carbon Jct', state: 'CO' },
      { key: 23, station: 'Durango', state: 'CO' },
      { key: 24, station: 'Home Ranch', state: 'CO' },
      { key: 25, station: 'Trimble Springs', state: 'CO' },
      { key: 26, station: 'Hermosa', state: 'CO' },
      { key: 27, station: 'Rockwood', state: 'CO' },
      { key: 28, station: 'Tacoma', state: 'CO' },
      { key: 29, station: 'Needleton', state: 'CO' },
      { key: 30, station: 'Elk Park', state: 'CO' },
      { key: 31, station: 'Silverton', state: 'CO' },
      { key: 32, station: 'Eureka', state: 'CO' }
    ];



    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;

    item1Data: any = [];
    item2Data: any = [];
    subCategory: any = [];
    itemSupplier: any = [];

    itemBrand: any = [];
    itemBrandModel: any = [];
    itemCC: any = [];
    itemColor: any = [];

    formData: FormGroup;
    formData2: FormGroup;

    files: File[] = [];
    warehouseData: any;
    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    position: any[] = [];
    
    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: ApprovalEmpService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        this.title = this._activatedRoute.snapshot.data.title;
        this.level = this._activatedRoute.snapshot.data.level;
        this.department = this._activatedRoute.snapshot.data.department;
        this.leaveType = this._activatedRoute.snapshot.data.leaveType;
        this.employeeType = this._activatedRoute.snapshot.data.employeeType;
        this.workShift = this._activatedRoute.snapshot.data.workShift;
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            id: null,
            code: null,
            username: null,
            firstname: null,
            lastname: null,
            email: ['', [Validators.required, Validators.email]],
            password: null,
            birthDate: null,
            registerDate: null,
            passProbationDate: null,
            active: true,
            titleId: null,
            levelId: null,
            departmentId: null,
            headId: null,
            employeeTypeId: null,
            workShiftId: null,
            sex: null,
         
        })
        if (this.Id) {
            this._Service.getUserId(this.Id).subscribe((resp: any) => {
                this.itemData = resp
                console.log(this.itemData);
                this.form.patchValue({
                    ...this.itemData,
                    departmentId: this.itemData?.department?.id?.toString(),
                    employeeTypeId: this.itemData?.employeeType?.id?.toString(),
                    workShiftId: this.itemData?.workShift?.id?.toString(),
                    levelId: this.itemData?.level?.id?.toString(),
                    titleId: this.itemData?.title?.id?.toString(),
                })
                console.log(this.form.value);
            })
        }
        this.doReset();





    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }



    onSelect(event: any) {
        this.files.push(...event.addedFiles);
        // Trigger Image Preview
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);
    }

    onRemove(event: any) {
        this.files.splice(this.files.indexOf(event), 1);
    }

    Submit(): void {
     
    
        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit value",
                message: "Do you want to edit value? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value
                    formValue.birthDate = DateTimeToSQL(this.form.value.birthDate , 'yyyy-MM-dd')
                    formValue.registerDate = DateTimeToSQL(this.form.value.registerDate , 'yyyy-MM-dd')
                    formValue.passProbationDate = DateTimeToSQL(this.form.value.passProbationDate , 'yyyy-MM-dd')
                    formValue.username = this.form.value.code
             
                    
                    this._Service.update(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['employee']);
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add value",
                message: "Do you want to save data ? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value
                    formValue.birthDate = DateTimeToISO(this.form.value.birthDate , 'yyyy-MM-dd')
                    formValue.registerDate = DateTimeToISO(this.form.value.registerDate , 'yyyy-MM-dd')
                    formValue.passProbationDate = DateTimeToISO(this.form.value.passProbationDate , 'yyyy-MM-dd')
                    formValue.username = this.form.value.code
                    // const formData = new FormData();
                    
                    // Object.entries(formValue).forEach(([key, value]: any[]) => {
                    //     formData.append(key, value !== null && value !== undefined && value !== '' ? value : '');
                    // });

                    // this.files.forEach((file) => {
                    //     formData.append('image', file ? file : '');
                    // });
                    
                    this._Service.create(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['employee']);

                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }


    backTo() {
        this._router.navigate(['employee'])
    }

    private useStations() {
        this.key = 'key';
        this.display = 'station'; // [ 'station', 'state' ];
        this.keepSorted = true;
        this.source = this.sourceStations;
        this.confirmed = this.confirmedStations;
      }
      doReset() {
        this.sourceStations = JSON.parse(JSON.stringify(this.stations));
        
        this.confirmedStations = new Array<any>();
        
        // Preconfirm some items.
        this.confirmedStations.push(this.stations[31]);
        this.confirmedStations.push(this.stations[30]);
        this.confirmedStations.push(this.stations[29]);
        // console.log(this.confirmedStations);
    
        this.useStations();
      }
    
    
    
      filterBtn() {
        return (this.filter ? 'Hide Filter' : 'Show Filter');
      }
    
      doDisable() {
        this.disabled = !this.disabled;
      }
    
      disableBtn() {
        return (this.disabled ? 'Enable' : 'Disabled');
      }
    
      swapDirection() {
        this.sourceLeft = !this.sourceLeft;
        this.format.direction = this.sourceLeft ? DualListComponent.LTR : DualListComponent.RTL;
        
      }

      GetEmployee(data: any) {
        this._Service.getEmployeeByDepartment(data).subscribe((resp: any)=>{
            
        })
      }

}
