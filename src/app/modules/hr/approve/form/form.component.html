<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a class="whitespace-nowrap text-primary-500 cursor-pointer" 
                        >Approve list</a
                    >
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon
                        class="text-secondary icon-size-5"
                        [svgIcon]="'heroicons_mini:chevron-right'"
                    ></mat-icon>
                    <a class="ml-1 text-primary-500">Create Edit Approve List</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Approve list
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Approve list
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-6 sm:p-10">
        <div>
            <div class="-mx-3 md:flex mb-0">
                <div class="md:min-w-1/2 px-3 mb-2 md:mb-0">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full" >
                        <mat-label>Department</mat-label>
                        <mat-select>
                            <mat-option *ngFor="let item of this.department;" value="{{item.id}}">
                                {{item.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
        </div>
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <dual-list [sort]="keepSorted" [source]="source" [key]="key" [display]="display" [filter]="filter"
            [(destination)]="confirmed" height="300px" [format]="format" [disabled]="disabled"></dual-list>
        </div>
        <div class="flex flex-row mt-2 justify-center">
            <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                    Save
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>