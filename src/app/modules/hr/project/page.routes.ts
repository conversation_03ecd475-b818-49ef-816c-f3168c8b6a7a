import { Routes } from '@angular/router';
import { ProjectComponent } from './page.component';
import { DepartmentService } from '../../hr/department/page.service';
import { EmployeeService } from '../../hr/employee/page.service';
import { ProjectService } from '../../hr/project/page.service';
import { inject } from '@angular/core';

export default [
    {
        path     : 'list',
        component: ProjectComponent,
        resolve: {
            employee: () => inject(EmployeeService).getEmployee(),
            country: () => inject(ProjectService).getCountry(),
        },
    },

] as Routes;
