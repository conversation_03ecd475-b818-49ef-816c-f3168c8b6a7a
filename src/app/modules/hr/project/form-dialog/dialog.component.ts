import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import {
    Form<PERSON>uilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
    MAT_DIALOG_DATA,
    MatDialog,
    MatDialogActions,
    MatDialogClose,
    MatDialogContent,
    MatDialogRef,
    MatDialogTitle,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatToolbarModule } from '@angular/material/toolbar';
import { ActivatedRoute } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTablesModule } from 'angular-datatables';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { ToastrService } from 'ngx-toastr';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { ProjectService } from '../page.service';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

@Component({
    selector: 'app-project-dialog',
    standalone: true,
    templateUrl: './dialog.component.html',
    styleUrl: './dialog.component.scss',
    imports: [
    CommonModule,
    DataTablesModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatToolbarModule,
    MatButtonModule,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatSelectModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatRadioModule,
    MatSlideToggleModule,
    MatAutocompleteModule
],
})
export class DialogForm implements OnInit {
    form: FormGroup;
    branch: any;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    dtOptions: any = {};
    addForm: FormGroup;

    employeeFilter = new FormControl('');
    filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    employee: any[] = [];
    country: any[] = [];

    roles: any[] = [
        { id: 2, name: 'Admin' },
        { id: 3, name: 'Supervisor' },
        { id: 4, name: 'Cashier' },
    ];
    constructor(
        private dialogRef: MatDialogRef<DialogForm>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        public dialog: MatDialog,
        private FormBuilder: FormBuilder,
        private fuseConfirmationService: FuseConfirmationService,
        private _service: ProjectService,
        private toastr: ToastrService,
        private imageUploadService: ImageUploadService,
    ) {
        this.form = this.FormBuilder.group({
            id: null,
            name: null,
            code: null,
            employeeId: null,
            countryId: null,
            active: 1,
            status: null
        });

        this.employee = this.data.employee;
        this.country = this.data.country;

        this.filterEmployee.next(this.employee.slice());

        console.log(this.data.value);
    }
    protected _onDestroy = new Subject<void>();

    ngOnInit(): void {
        if (this.data.type === 'EDIT') {
          console.log(this.data.value);

            this.form.patchValue({
                ...this.data.value,
                countryId: this.data.value.country?.id
            });
            this.employeeFilter.setValue(
                `${this.data.value.employee.firstname} ${this.data.value.employee.lastname}`
            );
        } else {
            console.log('New');
        }

        this.employeeFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filteremployee();
            });
    }

    protected _filteremployee() {
        if (!this.employee) {
            return;
        }
        let search = this.employeeFilter.value;

        if (!search) {
            this.filterEmployee.next(this.employee.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterEmployee.next(
            this.employee.filter(
                (item) =>
                    (
                        item.firstname.toLowerCase() +
                        ' ' +
                        item.lastname.toLowerCase()
                    ).includes(search) ||
                    item.firstname.toLowerCase().includes(search) ||
                    item.lastname.toLowerCase().includes(search)
            )
        );
    }
    onSelectemployee(event: any, type: any) {
        if (!event) {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                employeeId: selectedData.id,
            });
            this.employeeFilter.setValue(
                `${selectedData.firstname} ${selectedData.lastname}`
            );
        } else {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Found');
            return;
        }
    }

    Submit() {
        let formValue = this.form.value;
        const confirmation = this.fuseConfirmationService.open({
            title: 'Do you want to save data?',
            icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'primary',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'Save',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'Cancel',
                },
            },
            dismissible: false,
        });

        confirmation.afterClosed().subscribe((result) => {
            if (result == 'confirmed') {
                if (this.data.type === 'NEW') {
                    this._service.create(formValue).subscribe({
                        error: (err) => {
                            this.toastr.error(err.error.message[0]);
                        },
                        complete: () => {
                            this.toastr.success('Scuccess');
                            this.dialogRef.close(true);
                        },
                    });
                } else {
                    this._service
                        .update(this.data.value.id, formValue)
                        .subscribe({
                            error: (err) => {
                                this.toastr.error(err.error.message[0]);
                            },
                            complete: () => {
                                this.toastr.success('Successed');
                                this.dialogRef.close(true);
                            },
                        });
                }
            }
        });
    }

    onClose() {
        this.dialogRef.close();
    }
}
