<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 my-5">
      <div>
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Level Type List
        </h2>
      </div>
      <button mat-flat-button [color]="'primary'" (click)="opendialogAdd()">
        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
        <span class="ml-2"> Create new level type</span>
      </button>
    </div>
    <div class="overflow-x-auto whitespace-nowrap mt-2 max-w-full">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
      </table>
    </div>
    
  </div>
</div>
<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="openDialogEdit(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>Edit</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)" *ngIf="this.user?.role !== 'HR'">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>Delete</span>
    </button>
  </mat-menu>
</ng-template>