import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormArray,
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { LevelTypeService } from '../page.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { forkJoin, lastValueFrom } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'form-level-type',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule,
        MatInputModule,
        MatSlideToggleModule,
        RouterLink
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit, OnDestroy {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;


    warehouseData: any;
    Id: any
    itemData: any
    branch: any[] = [];
    permission: any[] = [];
    department: any[] = [];
    position: any[] = [];
    leaveType: any[] = [];
    daysOfWeek = [
        { full: 'Monday', short: '1' },
        { full: 'Tuesday', short: '2' },
        { full: 'Wednesday', short: '3'},
        { full: 'Thursday', short: '4' },
        { full: 'Friday', short: '5' },
        { full: 'Saturday', short: '6' },
        { full: 'Sunday', short: '7' },
    ]
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: LevelTypeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _toastService: ToastrService
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        this.leaveType = this._activatedRoute.snapshot.data.leaveType;
        // this.department = this._activatedRoute.snapshot.data.department.data;
        // this.position = this._activatedRoute.snapshot.data.position.data;
        // this.permission = this._activatedRoute.snapshot.data.permission.data;


    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            code: [''],
            name: [''],
            active: true,
            leavePermissions: this._fb.array([]),
        });

        if (this.Id) {
            this._Service.getLevelTypeById(this.Id).subscribe((resp: any) => {
                this.itemData = resp.data
                this.form.patchValue({
                    name: resp.name,
                    code: resp.code,
                    active: resp.active
                })
                for (const item of resp.leavePermissions) {
                    this.addLeavePermissionbyid(item)
                }
            })
        } else {

        }

    }

    addShift() {
        for (const day of this.leaveType) {
            const arrayValue = this._fb.group({
                id: null,
                leaveTypeId: day.id.toString(),
                ageWork: 1,
                qtyDay: 0,
                active: true
            });
            this.leavePermissions.push(arrayValue);
        }

    }
    addLeavePermissionbyid(data?: any) {
        const arrayValue = this._fb.group({
            id: data.id,
            leaveTypeId: data.leaveType.id.toString(),
            ageWork: data.ageWork,
            qtyDay: data.qtyDay,
            active: data.active
        });
        this.leavePermissions.push(arrayValue);
    }

    get leavePermissions() {
        return this.form.get("leavePermissions") as FormArray;
    }

    removeEmployee(index: any) {
        this.leavePermissions.removeAt(index);
    }


    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }




    Submit(): void {
        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit data",
                message: "Do you want edit data ? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value
                    this._Service.update(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['level-type']);
                            this._toastService.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check vale",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "ยกเลิก"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add value",
                message: "Do you want to add value? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.form.value;
                    this._Service.create(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['level-type']);
                            this._toastService.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "ยืนยัน",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "ยกเลิก"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }

    backTo() {
        this._router.navigate(['level-type'])
    }

}
