<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <div
        class="bg-card flex flex-0 flex-col border-b p-6 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between sm:px-10 sm:py-8">
        <div class="min-w-0 flex-1">
            <!-- Breadcrumbs -->
            <div class="flex flex-wrap items-center font-medium">
                <div>
                    <a [routerLink]="['/level-type']" class="whitespace-nowrap text-primary-500 cursor-pointer">Level
                        Type List</a>
                </div>
                <div class="ml-1 flex items-center whitespace-nowrap">
                    <mat-icon class="text-secondary icon-size-5" [svgIcon]="'heroicons_mini:chevron-right'"></mat-icon>
                    <a class="ml-1 text-primary-500" *ngIf="!this.Id">Create new level type list</a>
                    <a class="ml-1 text-primary-500" *ngIf="this.Id">Edit level type list</a>
                </div>
            </div>
            <!-- Title -->
            <div class="mt-2">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="!this.Id">
                    Create New level type
                </h2>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
                    *ngIf="this.Id">
                    Edit level type
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-6 sm:p-6">
        <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
            <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
                [formGroup]="form">
                <div class="flex flex-col gap-4 justify-center">
                    <div class="flex flex-col md:flex-row w-full md:w-1/3 gap-2">
                        <div class="-mx-3 md:flex mb-0 w-full">
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Level Type Code</mat-label>
                                    <input matInput [formControlName]="'code'" [placeholder]="''">
                                </mat-form-field>
                            </div>
                            <div class="md:min-w-full px-3 mb-2 md:mb-0">
                                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label>Level Type Name</mat-label>
                                    <input matInput [formControlName]="'name'" [placeholder]="''">
                                </mat-form-field>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col w-full md:w-full">
                        <div class="flex flex-col" formArrayName="leavePermissions">
                            <div class="relative overflow-x-auto border-[1px]">
                                <table class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead
                                        class="text-md text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 w-6/12">
                                                Leave Type
                                            </th>
                                            <th scope="col" class="px-6 py-3 w-1/12">
                                                Year
                                            </th>
                                            <th scope="col" class="px-6 py-3 w-2/12">
                                                Days
                                            </th>
                                            <th scope="col" class="px-6 py-3 w-2/12">
                                                Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let depo of leavePermissions.controls let i = index"
                                            [formGroupName]="i"
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                            <th scope="row"
                                                class="px-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                <div class="flex flex-row gap-2">
                                                    <div class="flex-col justify-center mt-2">
                                                        <mat-icon class="icon-size-7 cursor-pointer"
                                                            svgIcon="heroicons_solid:minus-circle"
                                                            (click)="removeEmployee(i)"></mat-icon>
                                                        <!-- <button class="mat-primary" mat-flat-button >
    
                                                        </button> -->
                                                    </div>
                                                    <div class="w-full">
                                                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                                            <mat-select [formControlName]="'leaveTypeId'">
                                                                <mat-option *ngFor="let item of this.leaveType;"
                                                                    value="{{item.id}}">
                                                                    {{item.name}}
                                                                </mat-option>
                                                            </mat-select>
                                                        </mat-form-field>
                                                    </div>

                                                </div>
                                            </th>
                                            <td class="px-2 py-2 text-lg">
                                                <div
                                                    class="md:min-w-full mb-6 md:mb-0  flex flex-col md:flex-row gap-2">
                                                    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                                        <input matInput [formControlName]="'ageWork'"
                                                            [placeholder]="''">
                                                    </mat-form-field>
                                                </div>
                                            </td>
                                            <td class="px-2 py-2">
                                                <div
                                                    class="md:min-w-full mb-6 md:mb-0  flex flex-col md:flex-row gap-2">
                                                    <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                                        <input matInput [formControlName]="'qtyDay'" [placeholder]="''">
                                                    </mat-form-field>
                                                </div>
                                            </td>
                                            <td class="flex justify-start px-2 py-2">
                                                <div
                                                    class="md:min-w-full mb-6 md:mb-0  flex flex-col md:flex-row gap-2 justify-start items-start">
                                                    <mat-slide-toggle class="" [checked]="checked" [color]="'primary'"
                                                        [formControlName]="'active'">
                                                    </mat-slide-toggle>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody>
                                        <tr
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                            <th scope="row" colspan="5"
                                                class="px-2 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                <div
                                                    class="md:min-w-full mb-6 md:mb-0  flex flex-col md:flex-row gap-2">
                                                    <button class="px-6" mat-flat-button [color]="'primary'"
                                                        (click)="addShift();">
                                                        Add
                                                    </button>
                                                </div>
                                            </th>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row mt-2 justify-center">
                    <div
                        class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit();">
                            Save
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>