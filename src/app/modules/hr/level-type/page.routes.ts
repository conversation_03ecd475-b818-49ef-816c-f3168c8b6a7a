import { Routes } from '@angular/router';
import { LevelTypeListComponent } from './level-type-list.component';
import { FormComponent } from './form/form.component';
import { inject } from '@angular/core';
import { LevelTypeService } from './page.service';
import { EmployeeService } from '../employee/page.service';
import { LevelTypeComponent } from './level-type.component';

export default [
    {
        path: '',
        component: LevelTypeComponent,
        children: [
            {
                path     : '',
                component: LevelTypeListComponent,
            },
            {
                path     : 'form',
                component: FormComponent,
                resolve: {
                    leaveType: () => inject(EmployeeService).getLeaveType()
                    // branch: () => inject(EmployeeService).getBranch(),
                    // permission: () => inject(EmployeeService).getPermission(),
                    // department: () => inject(EmployeeService).getDepartment(),
                    // position: () => inject(EmployeeService).getPosition(),
                },
            },
            {
                path     : 'edit/:id',
                component: FormComponent,
                resolve: {
                    leaveType: () => inject(EmployeeService).getLeaveType()
                    // branch: () => inject(EmployeeService).getBranch(),
                    // permission: () => inject(EmployeeService).getPermission(),
                    // department: () => inject(EmployeeService).getDepartment(),
                    // position: () => inject(EmployeeService).getPosition(),
                },
            },
        ]
    }

] as Routes;
