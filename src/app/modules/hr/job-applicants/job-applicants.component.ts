import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { JobApplicantsService } from './job-applicants.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { environment } from 'environments/environment';

@Component({
    selector: 'app-applicant-employee',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,

    ],
    templateUrl: './job-applicants.component.html',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class JobApplicantsComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;

    @ViewChild('name') name: any;
    @ViewChild('job') job: any;
    // @ViewChild('status') status: any;

    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    title: any;
    level: any;
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;

    data: any;

    status: any[] = [
      {
          value: 'APPLY',
          name: 'APPLY'
      },
      {
          value: 'SELECTED',
          name: 'SELECTED'
      },
      {
          value: 'INTERVIEW1',
          name: 'INTERVIEW 1'
      },
      {
          value: 'INTERVIEW2',
          name: 'INTERVIEW 2'
      },
      {
          value: 'INTERVIEW3',
          name: 'INTERVIEW 3'
      },
      {
          value: 'PASS',
          name: 'PASS'
      },
      {
          value: 'FAIL',
          name: 'FAIL'
      },
  ]

    constructor(
        private _service: JobApplicantsService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,
        private _matDialog: MatDialog,
        private _changeDetectorRef: ChangeDetectorRef

    ) {
    }
    ngOnInit(): void {
      setTimeout(() => this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
          console.log('this.dtOptions',this.dtOptions);

            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        // let formValue = this.form.value

        const self = this; // Capture the correct `this` context

        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            scrollX: true,
            order: [[2, 'asc']],
            ajax: (dataTablesParameters: any, callback) => {
              console.log('dataTablesParameters',dataTablesParameters);
                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                      console.log('resp',resp);

                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: 'No.',
                    data: null,
                    className: 'w-10 text-center',
                    render: function (data, type, row, meta) {
                        return meta.row + 1;
                    }
                },
                {
                    title: 'Name',
                    data: null,
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.name,
                  },
                },
                {
                    title: 'Job',
                    data: 'Job.title',
                    defaultContent: '-',
                    className: 'text-left',
                    ngTemplateRef: {
                      ref: this.job,
                  },
                },
                {
                  title: 'Status',
                  data: function (row: any) {
                      if (!row.status) {
                          return '-'; // Return default value if status is null or undefined
                      }
                      const status = self.status.find(s => s.value === row.status);
                      return status ? status.name : '-';
                  },
                  defaultContent: '-',
                  className: 'text-left'
                },
                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }
            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    opendialogAdd() {
        this._router.navigate(['job-applicants/form'])
    }

    openDialogEdit(id: any) {
        this._router.navigate(['job-applicants/edit/' + id])
    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to delete? ",
            message: "Please check the information. If the information is deleted, it cannot be retrieved.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('Successed');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
    onChangeType() {
        this.rerender()
    }

    clearData() {
        this.form.reset();
        this.rerender();
    }

    printPDF(id: any) {
        window.open(environment.apiUrl + `/api/report/redirect-report?id=${id}&type=applicant`)
      }
}
