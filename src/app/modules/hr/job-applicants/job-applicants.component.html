<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between pb-2 my-5  gap-4 border-b-2 border-gray-300">
      <div class="px-4 md:px-0">
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Job Applicants List
        </h2>
      </div>
      <div class="flex flex-col md:flex-row justify-end pb-2 px-4 md:px-0 my-2 gap-2">
        <!-- <button mat-flat-button [color]="'primary'" (click)="this.rerender()">
          <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
          <span class="ml-2"> Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2"> Reset</span>
        </button> -->
        <!-- <button mat-flat-button [color]="'primary'" (click)="opendialogAdd()">
          <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:plus-circle'"></mat-icon>
          <span class="ml-2"> Create Personnel Request</span>
        </button> -->
      </div>
    </div>
    <div class="overflow-x-auto whitespace-nowrap mt-2 max-w-full">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
      </table>
    </div>
  </div>
</div>

<!-- ================================================================================================ -->

<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="printPDF(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:document-arrow-down'"></mat-icon>
      <span>PDF</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="openDialogEdit(data.id)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>Edit</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="clickDelete(data.id)" *ngIf="this.user?.role !== 'HR'">
      <mat-icon [svgIcon]="'heroicons_solid:trash'"></mat-icon>
      <span>Delete</span>
    </button>
  </mat-menu>
</ng-template>

<ng-template #leaveNo let-data="adtData">
  <p (click)="openDialogViewLeaveQuota(data)" class="underline cursor-pointer text-blue-500">{{data.code}}</p>
</ng-template>
<ng-template #position let-data="adtData">
  <p class=" ">{{data.position.name}}</p>
</ng-template>
<ng-template #department let-data="adtData">
  <p class=" ">{{data.department.name}}</p>
</ng-template>
<ng-template #requestBy let-data="adtData">
  <p class=" ">{{data.requestBy.firstname}} {{data.requestBy.lastname}}</p>
</ng-template>

<ng-template #date let-data="adtData">
  <p class=" ">{{data.dateToStart | date : 'dd/MM/yyyy'}}</p>
</ng-template>
<ng-template #No let-data="adtData">
  <p (click)="View(data)" class="underline cursor-pointer text-blue-500">{{data.code}}</p>
</ng-template>
<ng-template #approve let-data="adtData">
  <p class=" ">{{data.approveBy ? (data.approveB?.firstname + ' ' + data.approveBy?.lastname) : '-'}}</p>
</ng-template>

<ng-template #name let-data="adtData">
  <p class="">{{data.applicant?.fullnameEn}}</p>
</ng-template>
<ng-template #job let-data="adtData">
  <p class="">{{data.job?.title}}</p>
</ng-template>
