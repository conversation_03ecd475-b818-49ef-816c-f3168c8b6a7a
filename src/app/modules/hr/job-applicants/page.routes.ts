import { Routes } from '@angular/router';
import { JobApplicantsComponent } from './job-applicants.component';
import { FormComponent } from './form/form.component';


export default [
    {
        path: '',
        component: JobApplicantsComponent,
        // children: [
        //     {
        //         path     : 'job',
        //         component: JobComponent,
        //         resolve: {
        //             // title: () => inject(EmployeeService).getTitle(),
        //             // level: () => inject(EmployeeService).getLevel(),
        //             // department: () => inject(EmployeeService).getDepartment(),
        //             // employeeType: () => inject(EmployeeService).getEmployeeType(),
        //             // workShift: () => inject(EmployeeService).getWorkShift(),
        //             // approval: () => inject(EmployeeService).getApproval(),
        //         },

        //     },
        // ]
    },
    {
      path: 'form',
      component: FormComponent,
      resolve: {
          // department: () => inject(StaffingRequestService).getDepartment(),
          // title: () => inject(StaffingRequestService).getTitle(),
      },
    },
    {
      path: 'edit/:id',
      component: FormComponent,
      resolve: {

      },
    },

] as Routes;
