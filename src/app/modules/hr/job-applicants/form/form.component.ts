import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule, NgClass } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { QuillModule } from 'ngx-quill';
import { ToastrService } from 'ngx-toastr';
import { JobApplicantsService } from '../job-applicants.service';
import { MatDividerModule } from '@angular/material/divider';
import { Subject } from 'rxjs';

@Component({
  selector: 'form-user',
  templateUrl: './form.component.html',
  standalone: true,
  imports: [
    MatIconModule,
    FormsModule,
    MatFormFieldModule,
    NgClass,
    MatInputModule,
    TextFieldModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    MatChipsModule,
    MatDatepickerModule,
    CommonModule,
    NgxDropzoneModule,
    MatRadioModule,
    MatAutocompleteModule,
    QuillModule,
    MatTabsModule,
    MatDividerModule,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit {
  formFieldHelpers: string[] = ['fuse-mat-dense'];

  Id: number;
  userId: number;
  form: FormGroup;
  user: any;
  jobDetails: any;

  data: any;

  pdfUrl: string;

  protected _onDestroy = new Subject<void>();

  status: any[] = [
    {
        value: 'APPLY',
        name: 'APPLY'
    },
    {
        value: 'SELECTED',
        name: 'SELECTED'
    },
    {
        value: 'INTERVIEW1',
        name: 'INTERVIEW 1'
    },
    {
        value: 'INTERVIEW2',
        name: 'INTERVIEW 2'
    },
    {
        value: 'INTERVIEW3',
        name: 'INTERVIEW 3'
    },
    {
        value: 'PASS',
        name: 'PASS'
    },
    {
        value: 'FAIL',
        name: 'FAIL'
    },
]
combinedContent: string = ''
  /**
   * Constructor
   */
  constructor(
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _fb: FormBuilder,
    private _Service: JobApplicantsService,
    private dialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _toast: ToastrService
  ) {
    this.Id = this._activatedRoute.snapshot.params.id;

    this.user = JSON.parse(localStorage.getItem('user'))
    this.userId = this.user ? this.user.id : 0;

    const currentDate = new Date().toISOString().split('T')[0];

    this.form = this._fb.group({
      applyDate: [null, Validators.required],
      status: ['', Validators.required],
      interview1Date: [null],
      score1: [0],
      result1: [true],
      comment1: [''],
      interview2Date: [null],
      score2: [0],
      result2: [true],
      comment2: [''],
      interview3Date: [null],
      score3: [0],
      result3: [true],
      comment3: [''],
      applicantId: [null],
      jobId: [null],
      interviewerId: [null]
    });

    console.log();
  
    // this.pdfUrl = https://hrm-mh.dev-asha.com:8443/api/report/redirect-report?id=1&type=applicant
    // this.pdfUrl = environment.apiUrl + `/api/report/redirect-report?id=${this.Id}&type=applicant`;

  }
  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  ngOnInit() {
    this.loadData();
  }

  /**
   * After view init
   */
  ngAfterViewInit(): void { }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
  }
  //================================================================================================

  loadData(): void {
    const applicantId = this.Id;

    // ตรวจสอบว่ามี applicantId หรือไม่
    if (!applicantId) {
      return;
    }
    //ประวัตส่วนตัว
    this._Service.getById(applicantId).subscribe({
      next: (data:any) => {
        console.log('Data loaded:', data);
        this.data = data;
        const pdfFile = this.data.applicant.applicantFiles.find((item: any) => item.name === 'Resume');
        this.pdfUrl = `${pdfFile?.file}`
        console.log(pdfFile);
        
        this.form.patchValue({
          ...data,
          applicantId: data.applicant?.id ?? null,
          jobId: data.job?.id ?? null,
          interviewerId: data.interviewer?.id ?? null,
        });
        this.jobDetails = data.job;
        this.combinedContent = `
        <div>${this.jobDetails.responsibility || ''}</div>
        <hr class="my-2 border-gray-200">
        <div>${this.jobDetails.qualification || ''}</div>
        <hr class="my-2 border-gray-200">
        <div>${this.jobDetails.benefits || ''}</div>
      `;


        this._changeDetectorRef.markForCheck();
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this._toast.error('Failed to load data.');
      },
    });
  }

  private transformDate(dateString: string): string | null {
    if (!dateString) return null;
    const date = new Date(dateString); // แปลง string เป็น Date object
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  Submit(): void {
    console.log('form',this.form.value);

    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this._toast.error('Please check value.');
      return;
    }
    let formValue = this.form.value;
    formValue.dateToStart = this.transformDate(formValue.dateToStart);
    if (this.Id) {
        const confirmation = this._fuseConfirmationService.open({
            title: "Edit value",
            message: "Do you want to edit value? ",
            icon: {
                show: false,
                name: "heroicons_outline:exclamation",
                color: "warning"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: true
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this._Service.update(this.Id, formValue).subscribe({
                    next: (resp: any) => {
                        this._router.navigate(['job-applicants']);
                        this._toast.success('Successed')
                    },
                    error: (err: any) => {
                        this.form.enable();
                        this._toast.error(err.error.message);
                    }
                });
            }
        });
    } else {
        const confirmation = this._fuseConfirmationService.open({
            title: "Add value",
            message: "Do you want to save data ?",
            icon: {
                show: false,
                name: "heroicons_outline:exclamation",
                color: "warning"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: true
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                this._Service.create(formValue).subscribe({
                    next: (resp: any) => {
                        this._router.navigate(['job-applicants']);
                        this._toast.success('Successed')
                    },
                    error: (err: any) => {
                        this.form.enable();
                        this._toast.error(err.error.message);
                    }
                });
            }
        });
    }
  }

  onCancel(): void {
    this._router.navigate(['job-applicants']);
  }

  openApplicant() {
    this._router.navigate(['job-applicant', 'edit', this.data.applicant.id]);
  }

  isPdfFile(url: string): boolean {
    // console.log(url);
    
    return url?.toLowerCase().endsWith('.pdf');
  }
}
