<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto bg-white shadow-md rounded-lg p-6 sm:p-10 m-4">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between pb-4 border-b border-gray-300 mb-6">
      <div>
        <h2 *ngIf="this.Id == null" class="text-3xl md:text-4xl font-extrabold tracking-tight">
          Job Applicants Form
        </h2>
        <h2 *ngIf="this.Id != null" class="text-3xl md:text-4xl font-extrabold tracking-tight">
          Edit Job Applicants Form
        </h2>
      </div>
    </div>

    <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start">
      <mat-tab label="Job Details">
        <!-- Job Details -->
        <div *ngIf="jobDetails" class="mb-6 p-6 bg-gray-100 rounded-md">
          <h2 class="text-xl font-semibold text-gray-800 mb-2">Job Details</h2>
          <div class="text-gray-700 leading-relaxed space-y-2">
            <p><strong>Title:</strong> {{ jobDetails.title }}</p>
            <p><strong>Job Type:</strong> {{ jobDetails.jobType }}</p>
            <p><strong>Description:</strong></p>
            <div class=" pl-4">
              <quill-view [content]="combinedContent" format="html"></quill-view>
            </div>
          </div>
        </div>


        <!-- Form -->
        <form [formGroup]="form">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
            <!-- Apply Date -->
            <div>
              <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                <mat-label>Apply Date</mat-label>
                <input matInput [matDatepicker]="applyDatePicker" formControlName="applyDate">
                <mat-datepicker-toggle matSuffix [for]="applyDatePicker"></mat-datepicker-toggle>
                <mat-datepicker #applyDatePicker></mat-datepicker>
              </mat-form-field>
            </div>

            <!-- Status -->
            <div>
              <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status">
                  <mat-option *ngFor="let status of status" [value]="status.value">
                    {{ status.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <table class="w-full border-collapse border border-gray-300">
            <thead>
              <tr class="bg-gray-200">
                <th class="border border-gray-300 p-2 w-20">No.</th>
                <th class="border border-gray-300 p-2">Date</th>
                <th class="border border-gray-300 p-2">Score</th>
                <th class="border border-gray-300 p-2">Result</th>
                <th class="border border-gray-300 p-2">Comment</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="items-center border border-gray-300 p-2 text-center">
                  1.
                </td>
                <td class="flex flex-col items-center border border-gray-300 p-2">
                  <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                    <input matInput [matDatepicker]="interview1Date" formControlName="interview1Date" class="mb-0">
                    <mat-datepicker-toggle matSuffix [for]="interview1Date"></mat-datepicker-toggle>
                    <mat-datepicker #interview1Date></mat-datepicker>
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <input matInput [placeholder]="''" formControlName="score1">
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-select formControlName="result1">
                      <mat-option [value]="true">Pass</mat-option>
                      <mat-option [value]="false">Fail</mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <input matInput [placeholder]="''" formControlName="comment1">
                  </mat-form-field>
                </td>
              </tr>
              <tr>
                <td class="items-center border border-gray-300 p-2 text-center">
                  2.
                </td>
                <td class="flex flex-col items-center border border-gray-300 p-2">
                  <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                    <input matInput [matDatepicker]="interview2Date" formControlName="interview2Date" class="mb-0">
                    <mat-datepicker-toggle matSuffix [for]="interview2Date"></mat-datepicker-toggle>
                    <mat-datepicker #interview2Date></mat-datepicker>
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <input matInput [placeholder]="''" formControlName="score2">
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-select formControlName="result2">
                      <mat-option [value]="true">Pass</mat-option>
                      <mat-option [value]="false">Fail</mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2 ">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <input matInput [placeholder]="''" formControlName="comment2">
                  </mat-form-field>
                </td>
              </tr>
              <tr>
                <td class="items-center border border-gray-300 p-2 text-center">
                  3.
                </td>
                <td class="flex flex-col items-center border border-gray-300 p-2">
                  <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                    <input matInput [matDatepicker]="interview3Date" formControlName="interview3Date" class="mb-0">
                    <mat-datepicker-toggle matSuffix [for]="interview3Date"></mat-datepicker-toggle>
                    <mat-datepicker #interview3Date></mat-datepicker>
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <input matInput [placeholder]="''" formControlName="score3">
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <mat-select formControlName="result3">
                      <mat-option [value]="true">Pass</mat-option>
                      <mat-option [value]="false">Fail</mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
                <td class="border border-gray-300 p-2">
                  <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                    <input matInput [placeholder]="''" formControlName="comment3">
                  </mat-form-field>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Actions -->
          <div class="flex justify-end mt-6 space-x-4">
            <button mat-stroked-button type="button" (click)="onCancel()">Cancel</button>
            <button mat-flat-button color="primary" type="submit" (click)="Submit()">Submit</button>
          </div>
        </form>
      </mat-tab>
      <mat-tab label="Resume">
        <div *ngIf="pdfUrl; else noDataTemplate" class="flex justify-center items-center w-full h-full">
          <ng-container *ngIf="isPdfFile(pdfUrl); else imageTemplate">
            <table id="excel-table"
            class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black mb-10">
            <thead class="text-gray-700 uppercase bg-gray-200 text-md dark:bg-gray-700 dark:text-gray-400">
              <tr class="border-[1px] border-black">
                <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">No</th>
                <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Type File</th>
                <th scope="col" class="px-2 py-2 w-2/9 border-[1px] border-gray-900">Download</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let item of data.applicant.applicantFiles;let i = index;">
                <!-- Row for Department -->
                <tr class="font-normal">
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900 w-1/6">
                    {{ i + 1}}
                  </td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900 w-4/6">
                    {{ item?.name ?? '' }}
                  </td>
                  <td class="px-2 py-2 text-md border-[1px] border-gray-900 w-1/6">
                    <a class="flex h-full w-full flex-col items-left justify-left no-underline"
                        [href]="item?.file" target="_blank"> <span class="font-bold text-gray-700 underline">View</span>
                    </a>
                  </td>
                
                </tr>
              </ng-container>
            </tbody>
          </table>
          </ng-container>
      
          <ng-template #imageTemplate>
            <img [src]="pdfUrl" alt="Uploaded Image" class="w-full h-auto">
          </ng-template>
        </div>
      
        <ng-template #noDataTemplate>
          <div class="flex justify-center items-center w-full h-64 text-gray-500 text-lg font-semibold">
            ไม่มีข้อมูล
          </div>
        </ng-template>
      </mat-tab>
      

      <mat-tab label="Application Data">
        <a class="bg-primary text-white p-5 rounded shadow-md" [routerLink]="['/applicant', 'edit', data?.applicant?.id]">View Detail</a>
      </mat-tab>
    </mat-tab-group>

  </div>
</div>
<style>
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
</style>
