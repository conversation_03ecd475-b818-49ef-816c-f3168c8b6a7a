<div mat-dialog-content class="p-4 border border-gray-300 rounded-lg">
  <h2 class="text-xl font-bold mb-4 border-b pb-2">Applicant Details</h2>

  <div *ngIf="data.applicants.length === 0" class="text-center text-gray-500 py-4">
    No applicants
  </div>

  <div *ngFor="let applicant of data.applicants" class="mb-4 p-4 border border-gray-300 rounded-lg shadow-sm relative bg-white">
    <button mat-icon-button class="absolute top-2 right-2" (click)="editApplicant(applicant.id)">
      <mat-icon>edit</mat-icon>
    </button>
    <p class="font-semibold">Name: <span class="font-normal">{{ applicant.applicant.fullnameEn }}</span></p>
    <p class="font-semibold">Status: <span class="font-normal">{{ applicant.status }}</span></p>
    <p class="font-semibold">Apply Date: <span class="font-normal">{{ applicant.applyDate | date:'dd/MM/yyyy' }}</span></p>
  </div>
</div>

<div mat-dialog-actions class="p-4  border-gray-300 flex justify-end">
  <button class="px-6 rounded-lg" mat-flat-button color="warn" (click)="Closed()">
    Closed
  </button>
</div>
