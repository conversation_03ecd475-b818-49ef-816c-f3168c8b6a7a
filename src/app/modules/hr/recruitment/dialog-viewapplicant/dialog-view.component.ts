import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { DataTablesModule } from 'angular-datatables';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dialog-view-recruitment',
  templateUrl: './dialog-view.component.html',
  styleUrls: ['./dialog-view.component.scss'],
  standalone: true,
  providers: [
      CurrencyPipe,
      DecimalPipe
  ],
  imports: [
      CommonModule,
      DataTablesModule,
      MatButtonModule,
      MatIconModule,
      MatMenuModule,
      MatDividerModule,
      MatFormFieldModule,
      ReactiveFormsModule,
      FormsModule,
      MatSelectModule
  ],
})
export class DialogViewComponent {
  applicants: any[];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialogRef: MatDialogRef<DialogViewComponent>,
    private router: Router
  ) {
    this.applicants = data.applicants;
  }

  Closed(): void {
    this.dialogRef.close();
  }

  editApplicant(id: number): void {
    this.dialogRef.close();
    this.router.navigate([`job-applicants/edit/${id}`]);
  }
}
