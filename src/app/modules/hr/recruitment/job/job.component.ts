import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { RecruitmentService } from '../recruitment.service';
import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { DialogViewComponent } from '../dialog-viewapplicant/dialog-view.component';
@Component({
    selector: 'app-job-employee',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule
    ],
    templateUrl: './job.component.html',
    styleUrl: './job.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class JobComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild('leaveNo') leaveNo: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;

    data: any;
    constructor(
        private _service: RecruitmentService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,
    ) {
        this.title = this._activatedRoute.snapshot.data.title;
        this.level = this._activatedRoute.snapshot.data.level;
        this.department = this._activatedRoute.snapshot.data.department;
        this.leaveType = this._activatedRoute.snapshot.data.leaveType;
        this.employeeType = this._activatedRoute.snapshot.data.employeeType;
        this.workShift = this._activatedRoute.snapshot.data.workShift;
        this.approval = this._activatedRoute.snapshot.data.approval;

        this.form = this._fb.group({
            titleId: null,
            levelId: null,
            departmentId: null,
            employeeTypeId: null,
            headId: null,
        })


    }
    ngOnInit(): void {
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        let formValue = this.form.value
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            scrollX: true,
            order: [[2, 'asc']],
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.title': this.form.value.titleId,
                    'filter.level': this.form.value.levelId,
                    'filter.department': this.form.value.departmentId,
                    'filter.employeeType': this.form.value.employeeTypeId,
                    'filter.head': this.form.value.headId,
                }
                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: 'No.',
                    data: null,
                    className: 'w-10 text-center',
                    render: function (data, type, row, meta) {
                        return meta.row + 1;
                    }
                },
                {
                    title: 'Job name',
                    data: 'title',
                    defaultContent: '-',
                    className: 'text-left w-200'
                },
                {
                    title: 'Job Type',
                    data: 'jobType',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Active',
                    data: 'active',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }
            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }

    opendialogAdd() {
        this._router.navigate(['recruitment/form'])
    }

    openDialogEdit(id: any) {
        this._router.navigate(['recruitment/edit/' + id])
    }
    openDialogLeaveQuota(data: any) {
        // this._service.getLeavePermissionByEmp(data.id).subscribe((resp: any) => {
        //     const DialogRef = this.dialog.open(DialogFormAdjustLeave, {
        //         disableClose: true,
        //         width: 'auto',
        //         maxHeight: '100vh',
        //         enterAnimationDuration: 200,
        //         exitAnimationDuration: 100,
        //         data: {
        //             type: 'EDIT',
        //             employee: data,
        //             value: resp,
        //         }
        //     });
        //     DialogRef.afterClosed().subscribe((result) => {
        //         if (result) {
        //             console.log(result, 'result')
        //             this.rerender();
        //         }
        //     });
        // })

    }

    openDialogViewLeaveQuota(data: any) {
        // this._service.getLeavePermissionByEmp(data.id).subscribe((resp: any) => {
        //     const DialogRef = this.dialog.open(DialogFormAdjustLeave, {
        //         disableClose: true,
        //         width: 'auto',
        //         maxHeight: '100vh',
        //         enterAnimationDuration: 200,
        //         exitAnimationDuration: 100,
        //         data: {
        //             type: 'VIEW',
        //             employee: data,
        //             value: resp,
        //         }
        //     });
        //     DialogRef.afterClosed().subscribe((result) => {
        //         if (result) {
        //             console.log(result, 'result')
        //             this.rerender();
        //         }
        //     });
        // })

    }

    openDialogViewApplicant(applicants: any) {
        this.dialog.open(DialogViewComponent, {
            width: '600px',
            data: { applicants }
        });
    }

    clickDelete(id: any) {
        const confirmation = this.fuseConfirmationService.open({
            title: "Do you want to delete? ",
            message: "Please check the information. If the information is deleted, it cannot be retrieved.",
            icon: {
                show: true,
                name: "heroicons_outline:exclamation-triangle",
                color: "warn"
            },
            actions: {
                confirm: {
                    show: true,
                    label: "Save",
                    color: "primary"
                },
                cancel: {
                    show: true,
                    label: "Cancel"
                }
            },
            dismissible: false
        })

        confirmation.afterClosed().subscribe(
            result => {
                if (result == 'confirmed') {
                    this._service.delete(id).subscribe({
                        error: (err) => {

                        },
                        complete: () => {
                            this.toastr.success('Successed');
                            this.rerender();
                        },
                    });
                }
            }
        )
    }
    onChangeType() {
        this.rerender()
    }

    clearData() {
        this.form.reset();
        this.rerender();
    }
}
