import { Routes } from '@angular/router';
import { RecruitmentComponent } from './recruitment.component';
import { JobComponent } from './job/job.component';
import { FormComponent } from './form/form.component';

export default [
    {
        path: '',
        component: RecruitmentComponent,
        children: [
            {
                path     : 'job',
                component: JobComponent,
                resolve: {
                    // title: () => inject(EmployeeService).getTitle(),
                    // level: () => inject(EmployeeService).getLevel(),
                    // department: () => inject(EmployeeService).getDepartment(),
                    // employeeType: () => inject(EmployeeService).getEmployeeType(),
                    // workShift: () => inject(EmployeeService).getWorkShift(),
                    // approval: () => inject(EmployeeService).getApproval(),
                },

            },
            {
                path: 'form',
                component: FormComponent,
            },
            {
                path: 'edit/:id',
                component: FormComponent,
            },
        ]
    },

] as Routes;
