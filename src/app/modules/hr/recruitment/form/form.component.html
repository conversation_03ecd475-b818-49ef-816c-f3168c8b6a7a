<div class="flex flex-col flex-auto min-w-0">
  <!-- Header -->
  <div
    class="bg-card flex flex-0 flex-col border-b p-4 dark:bg-transparent sm:flex-row sm:items-center sm:justify-between">
    <div class="min-w-0 flex-1">
      <!-- Breadcrumbs -->
      <div class="flex flex-wrap items-center font-medium">
        <div>
          <a class="whitespace-nowrap text-primary-500 cursor-pointer">Job list</a>
        </div>
        <div class="ml-1 flex items-center whitespace-nowrap">
          <mat-icon class="text-secondary icon-size-5" [svgIcon]="'heroicons_mini:chevron-right'"></mat-icon>
          <a class="ml-1 text-primary-500" *ngIf="!this.Id">Create new job</a>
          <a class="ml-1 text-primary-500" *ngIf="this.Id">Edit job</a>
        </div>
      </div>
      <!-- Title -->
      <div class="mt-2">
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl"
          *ngIf="!this.Id">
          Create New Job
        </h2>
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl" *ngIf="this.Id">
          Edit Job
        </h2>
      </div>
    </div>
  </div>
  <!-- Main -->
  <div class="flex-auto p-6">
    <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
      <form class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2"
        [formGroup]="form">
        <div class="flex flex-col md:flex-row gap-4 justify-center">
          <div class="flex flex-col w-full md:w-1/3">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Title</mat-label>
                  <input matInput [formControlName]="'title'" [placeholder]="'Please enter title'">
                  @if (form.get('title').hasError('required')) {
                  <mat-error> title is required </mat-error>
                  }
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-1/3">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Job Type</mat-label>
                  <mat-select [formControlName]="'jobType'">
                    <mat-option *ngFor="let type of jobTypes" [value]="type.value">
                      {{ type.label }}
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="form.get('jobType').hasError('required')">
                    Job Type is required
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-1/3">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Job Date</mat-label>
                    <input matInput [matDatepicker]="picker" placeholder="Select Date" formControlName="jobOpen" (dateChange)="changeDate()">
                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col md:flex-row gap-4 justify-center">
          <div class="flex flex-col w-full md:w-1/4">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Education</mat-label>
                  <input matInput [formControlName]="'education'" [placeholder]="'Please enter education'">
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-1/4">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Salary</mat-label>
                  <input matInput [formControlName]="'salary'" [placeholder]="'Please enter salary'">
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-1/4">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Experience</mat-label>
                  <input matInput [formControlName]="'experience'" [placeholder]="'Please enter experience'">
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="flex flex-col w-full md:w-1/4">
            <div class="-mx-3 md:flex mb-2 md:mb-0">
              <div class="md:min-w-full px-3 mb-2 md:mb-0">
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                  <mat-label>Location</mat-label>
                  <input matInput [formControlName]="'location'" [placeholder]="'Please enter location'">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col md:flex-row justify-center">
          <mat-tab-group class="w-full md:w-full">
            <mat-tab label="Responsibilities">
              <div class="flex flex-col w-full mx-auto mt-4">
                <quill-editor [formControlName]="'responsibility'" theme="snow" class="w-full">
                  <div quill-editor-toolbar>
                    <span class="ql-formats">
                      <select class="ql-size">
                        <option value="small"></option>
                        <option selected></option>
                        <option value="large"></option>
                        <option value="huge"></option>
                      </select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-bold"></button>
                      <button class="ql-italic"></button>
                      <button class="ql-underline"></button>
                      <button class="ql-strike"></button>
                    </span>
                    <span class="ql-formats">
                      <select class="ql-color"></select>
                      <select class="ql-background"></select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-list" value="ordered"></button>
                      <button class="ql-list" value="bullet"></button>
                      <select class="ql-align">
                        <option selected></option>
                        <option value="center"></option>
                        <option value="right"></option>
                        <option value="justify"></option>
                      </select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-image"></button>
                    </span>
                  </div>
                </quill-editor>
              </div>
            </mat-tab>
            <mat-tab label="Qualifications">
              <div class="flex flex-col w-full mx-auto mt-4">
                <quill-editor [formControlName]="'qualification'" theme="snow" class="w-full">
                  <div quill-editor-toolbar>
                    <span class="ql-formats">
                      <select class="ql-size">
                        <option value="small"></option>
                        <option selected></option>
                        <option value="large"></option>
                        <option value="huge"></option>
                      </select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-bold"></button>
                      <button class="ql-italic"></button>
                      <button class="ql-underline"></button>
                      <button class="ql-strike"></button>
                    </span>
                    <span class="ql-formats">
                      <select class="ql-color"></select>
                      <select class="ql-background"></select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-list" value="ordered"></button>
                      <button class="ql-list" value="bullet"></button>
                      <select class="ql-align">
                        <option selected></option>
                        <option value="center"></option>
                        <option value="right"></option>
                        <option value="justify"></option>
                      </select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-image"></button>
                    </span>
                  </div>
                </quill-editor>
              </div>
            </mat-tab>
            <mat-tab label="Benefits">
              <div class="flex flex-col w-full mx-auto mt-4">
                <quill-editor [formControlName]="'benefits'" theme="snow" class="w-full">
                  <div quill-editor-toolbar>
                    <span class="ql-formats">
                      <select class="ql-size">
                        <option value="small"></option>
                        <option selected></option>
                        <option value="large"></option>
                        <option value="huge"></option>
                      </select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-bold"></button>
                      <button class="ql-italic"></button>
                      <button class="ql-underline"></button>
                      <button class="ql-strike"></button>
                    </span>
                    <span class="ql-formats">
                      <select class="ql-color"></select>
                      <select class="ql-background"></select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-list" value="ordered"></button>
                      <button class="ql-list" value="bullet"></button>
                      <select class="ql-align">
                        <option selected></option>
                        <option value="center"></option>
                        <option value="right"></option>
                        <option value="justify"></option>
                      </select>
                    </span>
                    <span class="ql-formats">
                      <button class="ql-image"></button>
                    </span>
                  </div>
                </quill-editor>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>


        <div class="flex mt-10 pb-6 flex-col items-center justify-center w-full px-3 mb-2 md:mb-0">
          <mat-label class="font-bold">Active</mat-label>
          <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="active">
            <mat-radio-button [value]="true">Yes </mat-radio-button>
            <mat-radio-button [value]="false">No </mat-radio-button>
          </mat-radio-group>
        </div>


        <div class="flex flex-row mt-2 justify-center">
          <button class="px-6 ml-3" mat-flat-button color="primary" (click)="Submit();">
            Save
          </button>
          <button class="px-6 ml-3" mat-flat-button color="warn" (click)="backTo()">
            Cancel
          </button>
        </div>
      </form>


    </div>
  </div>
</div>
