import { TextFieldModule } from '@angular/cdk/text-field';
import { NgClass } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnInit,
} from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';
import { CommonModule } from '@angular/common';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { ReplaySubject } from 'rxjs';
import { MatRadioModule } from '@angular/material/radio';
import { DateTimeToISO } from 'app/helper';
import { ToastrService } from 'ngx-toastr';
import { DateTime } from 'luxon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { RecruitmentService } from '../recruitment.service';
import { QuillModule } from 'ngx-quill';
import { MatTabsModule } from '@angular/material/tabs';
@Component({
    selector: 'form-user',
    templateUrl: './form.component.html',
    standalone: true,
    imports: [
        MatIconModule,
        FormsModule,
        MatFormFieldModule,
        NgClass,
        MatInputModule,
        TextFieldModule,
        ReactiveFormsModule,
        MatButtonToggleModule,
        MatButtonModule,
        MatSelectModule,
        MatOptionModule,
        MatChipsModule,
        MatDatepickerModule,
        CommonModule,
        NgxDropzoneModule,
        MatRadioModule,
        MatAutocompleteModule,
        QuillModule,
        MatTabsModule
    ],
    changeDetection: ChangeDetectionStrategy.Default,
})
export class FormComponent implements OnInit, AfterViewInit {
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    form: FormGroup;

    files: File[] = [];

    Id: any
    itemData: any

    title: any;
    education: any;
    description: any[] = [];
    experience: any;
    salary: any;
    location: any;

    jobTypes = [
        { value: 'Permanent', label: 'Permanent' },
        { value: 'Fixed Terms Contranct', label: 'Fixed Terms Contranct' },
        // { value: 'Contract', label: 'Contract' },
        // { value: 'Parttime', label: 'Parttime' },
    ];


    active: any[] = [];

    ///approver filter
    approverFilter = new FormControl('');
    filterApprover: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    approval: any[] = [];
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _fb: FormBuilder,
        private _Service: RecruitmentService,
        private dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _toast: ToastrService,
    ) {

        this.Id = this._activatedRoute.snapshot.paramMap.get('id');
        this.active = (this.approval.slice());
    }
    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    ngOnInit() {
        this.form = this._fb.group({
            id: null,
            title: ['', [Validators.required]],
            education: [''],
            description: [''],
            experience: [''],
            salary: [''],
            location: [''],
            jobOpen: [''],
            jobType: [''],
            active: false,
            responsibility: [''],
            qualification: [''],
            benefits: [''],
        })

        if (this.Id) {
            this._Service.getUserId(this.Id).subscribe((resp: any) => {
                this.itemData = resp

                this.form.patchValue({
                    ...this.itemData
                })
            })
        }

    }

    changeDate() {
        const date: any = DateTimeToISO(this.form.value.jobOpen, 'yyyy-MM-dd');
        this.form.patchValue({
            jobOpen: date
        })
        console.log(this.form.value);
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    Submit(): void {
        if (this.form.invalid) {
            return;
        }
        let formValue = this.form.value;

        if (this.Id) {
            const confirmation = this._fuseConfirmationService.open({
                title: "Edit value",
                message: "Do you want to edit value? ",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    this._Service.update(this.Id, formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['recruitment/job']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Close",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        } else {
            const confirmation = this._fuseConfirmationService.open({
                title: "Add value",
                message: "Do you want to save data ?",
                icon: {
                    show: false,
                    name: "heroicons_outline:exclamation",
                    color: "warning"
                },
                actions: {
                    confirm: {
                        show: true,
                        label: "Save",
                        color: "primary"
                    },
                    cancel: {
                        show: true,
                        label: "Cancel"
                    }
                },
                dismissible: true
            });

            // Subscribe to the confirmation dialog closed action
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {

                    // formValue.jobOpen = DateTime.fromSQL(this.form.value.jobOpen).toFormat('yyyy-MM-dd')
                    this._Service.create(formValue).subscribe({
                        next: (resp: any) => {
                            this._router.navigate(['recruitment/job']);
                            this._toast.success('Successed')
                        },
                        error: (err: any) => {
                            this.form.enable();
                            this._fuseConfirmationService.open({
                                title: "Please check value.",
                                message: err.error.message,
                                icon: {
                                    show: true,
                                    name: "heroicons_outline:exclamation",
                                    color: "warning"
                                },
                                actions: {
                                    confirm: {
                                        show: false,
                                        label: "Save",
                                        color: "primary"
                                    },
                                    cancel: {
                                        show: false,
                                        label: "Cancel"
                                    }
                                },
                                dismissible: true
                            });
                        }
                    });
                }
            });
        }
    }


    backTo() {
        this._router.navigate(['recruitment/job'])
    }
}
