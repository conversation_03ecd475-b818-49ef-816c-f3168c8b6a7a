import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { data } from './mock-data';
import { removeEmpty } from 'app/helper';

@Injectable({
  providedIn: 'root'
})
export class CompanyHolidayService {

  private _categories: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _roles: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
  private _branch: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  get categories$() {
    return this._categories.asObservable();
  }

  constructor(private http: HttpClient) { }

  getAll(dataTablesParameters: any) {
    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/holiday/datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  // getAll(dataTablesParameters: any): Observable<any> {

  //   return this.http
  //     .get<any>(`${environment.apiUrl}/api/holiday/datatables`, dataTablesParameters)
  //     .pipe(
  //       map((data: any) => {
  //         return data;
  //       }));
  // }




  create(data: any) {
    return this.http.post(`${environment.apiUrl}/api/holiday`, data)
  }

  update(id: number,data: object) {
    return this.http.put(`${environment.apiUrl}/api/holiday/` + id, data)
  }

  getHolidayById(id:any) {
    return this.http.get(`${environment.apiUrl}/api/holiday/`+ id).pipe(
      tap((resp: any) => {
        this._branch.next(resp.data);
      }),
    )
  }
  getHoliday() {
    return this.http.get(environment.apiUrl + '/api/holiday').pipe(
      tap((resp: any) => {
        this._branch.next(resp);
      }),
    )
  }

  delete(id: number) {
    return this.http.delete(environment.apiUrl + '/api/holiday/' + id)
  }
}
