<div>
    <div class="flex flex-row justify-between border-b-2 border-gray-300">
        <div class="my-2 text-lg text-black font-bold">
            <h4>OT Air Preview</h4>
        </div>
        <div class="flex flex-row mb-2 justify-end" *ngIf="this.itemData.status !== 'cancel'">
            <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                <button class="px-6 ml-3" mat-flat-button [color]="'accent'"
                    *ngIf="this.data?.url === '/admin-ot-air/ot-air'" (click)="EditAdmin()">
                    Edit
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'accent'"
                    *ngIf="this.data?.url === '/ot-air/ot-air-list'" (click)="Edit()">
                    Edit
                </button>

                <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                    *ngIf="this.data?.url === '/admin-ot-air/ot-air'" (click)="SubmitAdmin('cancel')">
                    Eject
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'primary'"
                    *ngIf="this.data?.url === '/admin-ot-air/ot-air'" (click)="Submit('approved')">
                    Approve
                </button>

                <button class="px-6 ml-3" mat-flat-button [color]="'warn'"
                    *ngIf="this.data?.url === '/ot-air/ot-air-approval'" (click)="Submit('reject')">
                    Eject
                </button>
                <button class="px-6 ml-3" mat-flat-button [color]="'primary'"
                    *ngIf="this.data?.url === '/ot-air/ot-air-approval'" (click)="Submit('approved')">
                    Approve
                </button>
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 my-2 border-b-2 border-gray-300">
        <div class="flex flex-col gap-2 mb-2 mx-2">
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>OT Air No. </h4>
                </div>
                <div class="w-4/6">
                    <span class="font-bold text-gray-700">: {{this.itemData?.code ?? '-'}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Name </h4>
                </div>
                <div class="w-4/6">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.employee?.firstname ?? '-')}}
                        {{(this.itemData?.employee?.lastname ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
              <div class="w-2/6">
                  <h4>Date </h4>
              </div>
              <div class="w-4/6 whitespace-normal">
                  <span class="font-bold text-gray-700">: {{(formatDate(this.itemData?.date ) ?? '-')}}</span>
              </div>
          </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Start Time </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{this.itemData?.timeStart ?? '-'}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>End Time </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{this.itemData?.timeEnd ?? '-'}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
              <div class="w-2/6">
                  <h4>Total  Hours </h4>
              </div>
              <div class="w-4/6 whitespace-normal">
                  <span class="font-bold text-gray-700">: {{this.itemData?.qtyHour ?? '-'}}</span>
              </div>
          </div>
          <div class="flex flex-row mt-2 gap-2">
            <div class="w-2/6">
                <h4>Zone </h4>
            </div>
            <div class="w-4/6 whitespace-normal">
                <span class="font-bold text-gray-700">: {{(this.itemData?.zone?.name ?? '-')}}</span>
            </div>
        </div>
        <div class="flex flex-row mt-2 gap-2">
          <div class="w-2/6">
            <h4>Zone Detail</h4>
          </div>
          <div class="w-4/6 whitespace-normal">
            <ng-container *ngIf="itemData?.floor?.pic; else noImage">
              <img
                [src]="itemData?.floor?.pic"
                alt="Floor Picture"
                class="w-20 h-20 object-cover cursor-pointer"
                (click)="openImage(itemData?.floor?.pic)"
              />
            </ng-container>
            <ng-template #noImage>
              <span class="font-bold text-gray-700">: -</span>
            </ng-template>
          </div>
        </div>
        <div class="flex flex-row mt-2 gap-2">
          <div class="w-2/6">
              <h4>Project </h4>
          </div>
          <div class="w-4/6 whitespace-normal">
              <span class="font-bold text-gray-700">: ({{this.itemData?.project?.code}}) {{(this.itemData?.project?.name ?? '-')}}</span>
          </div>
      </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="w-2/6">
                    <h4>Detail </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.detail ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2" *ngIf="this.itemData?.statusRemark">
                <div class="w-2/6">
                    <h4>Reason Reject</h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.statusRemark ?? '-')}}</span>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2 mb-2">
                <div class="w-2/6">
                    <h4>Approver </h4>
                </div>
                <div class="w-4/6 whitespace-normal">
                    <span class="font-bold text-gray-700">: {{(this.itemData?.head?.firstname ?? '-')}}
                        {{(this.itemData?.head?.lastname ?? '-')}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-row mt-2 justify-center">
        <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3 mb-2" mat-flat-button [color]="'accent'" (click)="onClose()">
                Close
            </button>
        </div>
    </div>
</div>
