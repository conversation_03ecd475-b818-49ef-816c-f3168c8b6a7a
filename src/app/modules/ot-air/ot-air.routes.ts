import { Routes } from '@angular/router';

import { EmployeeService } from '../hr/employee/page.service';
import { inject } from '@angular/core';
import { ReportService } from '../report/page.service';
import { OtairComponent } from './ot-air.component';
import { OtListComponent } from '../ot/ot-list/ot-list.component';
import { OtairApplicationFormComponent } from './ot-air-application-form/form.component';
import { OtairApprovalComponent } from './ot-air-approval/ot-air-approval.component';
import { OtairService } from './ot-air.service';
import { ZoneService } from '../hr/zone/page.service';
import { FloorService } from '../hr/floor/page.service';
import { ProjectService } from '../hr/project/page.service';
import { OtairListComponent } from './ot-air-list/ot-air-list.component';


export default [
    {
        path     : '',
        component: OtairComponent,
    },
    {
      path     : 'ot-air-list',
      component: OtairListComponent,
  },
    {
        path     : 'ot-air-application-form',
        component: OtairApplicationFormComponent,
        resolve: {
            email :  () => inject(OtairService).getAppSetting('MAIL_OT'),
            leaveType: () => inject(EmployeeService).getLeaveType(),
            employee: () => inject(EmployeeService).getEmployee(),
            approval: () => inject(EmployeeService).getApproval(),
            leavePermission: () => inject(OtairService).getLaevePermission(),
            ProjectData: () => inject(ProjectService).getProject(),
            ZoneData: () => inject(ZoneService).getZone(),
            FloorData: () => inject(FloorService).getFloor(),
        }
    },
    {

        path     : 'ot-air-approval',
        component: OtairApprovalComponent,
    },

] as Routes;
