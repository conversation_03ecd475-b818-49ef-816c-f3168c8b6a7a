<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto  sm:p-10 bg-card m-4 p-4 rounded-md">
        <div class="flex flex-col md:flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
            <div>
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl mx-2">
                    Overtime Air History
                </h2>
            </div>
            <div class="flex lex-row justify-end pb-2 my-2 gap-2">
              <button mat-flat-button [color]="'primary'" (click)="this.rerender()">
                  <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
                  <span class="ml-2"> Search</span>
              </button>
              <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
                  <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
                  <span class="ml-2"> Reset</span>
              </button>
          </div>
        </div>
        <form [formGroup]="form">
          <div class="flex flex-col md:flex-row justify-start pb-2 my-2 gap-2 border-b-2 border-gray-400">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
                <mat-label> Date</mat-label>
                <input matInput [matDatepicker]="picker" placeholder="Select Date" formControlName="date" (dateChange)="dateChange()">
                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                </mat-form-field>
                <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
                  <mat-label>Status</mat-label>
                  <mat-select [formControlName]="'status'" placeholder="Select status" multiple>
                      <mat-option *ngFor="let item of this.status;" value="{{item.value}}">
                          {{item.name}}
                      </mat-option>
                  </mat-select>
              </mat-form-field>
          </div>
      </form>
        <div class="overflow-auto">
            <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full"></table>
        </div>

    </div>

</div>

<ng-template #btNg let-data="adtData">
    <input type="checkbox" (change)="onCheckboxChange($event, row.id)" />
</ng-template>
<ng-template #leaveNo let-data="adtData">
   <p (click)="ViewLeave(data)" class="underline cursor-pointer text-blue-500">{{data.code}}</p>
</ng-template>
<ng-template #fullName let-data="adtData">
   <p class=" ">{{data.employee.firstname}} {{data.employee.lastname}}</p>
</ng-template>
<ng-template #date let-data="adtData">
  <p class=" ">{{data.date | date : 'dd/MM/yyyy'}}</p>
</ng-template>
