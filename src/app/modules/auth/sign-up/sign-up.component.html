<div
    class="flex min-w-0 flex-auto flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start"
>
    <div
        class="w-full px-4 py-8 sm:bg-card sm:w-auto sm:rounded-2xl sm:p-12 sm:shadow md:flex md:h-full md:w-1/2 md:items-center md:justify-end md:rounded-none md:p-16 md:shadow-none"
    >
        <div class="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
            <!-- Logo -->
            <div class="w-12">
                <img src="images/logo/logo.svg" />
            </div>

            <!-- Title -->
            <div
                class="mt-8 text-4xl font-extrabold leading-tight tracking-tight"
            >
                Sign up
            </div>
            <div class="mt-0.5 flex items-baseline font-medium">
                <div>Already have an account?</div>
                <a
                    class="ml-1 text-primary-500 hover:underline"
                    [routerLink]="['/sign-in']"
                    >Sign in
                </a>
            </div>

            <!-- Alert -->
            @if (showAlert) {
                <fuse-alert
                    class="mt-8"
                    [appearance]="'outline'"
                    [showIcon]="false"
                    [type]="alert.type"
                    [@shake]="alert.type === 'error'"
                >
                    {{ alert.message }}
                </fuse-alert>
            }

            <!-- Sign Up form -->
            <form class="mt-8" [formGroup]="signUpForm" #signUpNgForm="ngForm">
                <!-- Name field -->
                <mat-form-field class="w-full">
                    <mat-label>Full name</mat-label>
                    <input id="name" matInput [formControlName]="'name'" />
                    @if (signUpForm.get('name').hasError('required')) {
                        <mat-error> Full name is required </mat-error>
                    }
                </mat-form-field>

                <!-- Email field -->
                <mat-form-field class="w-full">
                    <mat-label>Email address</mat-label>
                    <input id="email" matInput [formControlName]="'email'" />
                    @if (signUpForm.get('email').hasError('required')) {
                        <mat-error> Email address is required </mat-error>
                    }
                    @if (signUpForm.get('email').hasError('email')) {
                        <mat-error>
                            Please enter a valid email address
                        </mat-error>
                    }
                </mat-form-field>

                <!-- Password field -->
                <mat-form-field class="w-full">
                    <mat-label>Password</mat-label>
                    <input
                        id="password"
                        matInput
                        type="password"
                        [formControlName]="'password'"
                        #passwordField
                    />
                    <button
                        mat-icon-button
                        type="button"
                        (click)="
                            passwordField.type === 'password'
                                ? (passwordField.type = 'text')
                                : (passwordField.type = 'password')
                        "
                        matSuffix
                    >
                        @if (passwordField.type === 'password') {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:eye'"
                            ></mat-icon>
                        }
                        @if (passwordField.type === 'text') {
                            <mat-icon
                                class="icon-size-5"
                                [svgIcon]="'heroicons_solid:eye-slash'"
                            ></mat-icon>
                        }
                    </button>
                    <mat-error> Password is required </mat-error>
                </mat-form-field>

                <!-- Company field -->
                <mat-form-field class="w-full">
                    <mat-label>Company</mat-label>
                    <input
                        id="company-confirm"
                        matInput
                        [formControlName]="'company'"
                    />
                </mat-form-field>

                <!-- ToS and PP -->
                <div class="mt-1.5 inline-flex w-full items-end">
                    <mat-checkbox
                        class="-ml-2"
                        [color]="'primary'"
                        [formControlName]="'agreements'"
                    >
                        <span>I agree with</span>
                        <a
                            class="ml-1 text-primary-500 hover:underline"
                            [routerLink]="['./']"
                            >Terms
                        </a>
                        <span>and</span>
                        <a
                            class="ml-1 text-primary-500 hover:underline"
                            [routerLink]="['./']"
                            >Privacy Policy
                        </a>
                    </mat-checkbox>
                </div>

                <!-- Submit button -->
                <button
                    class="fuse-mat-button-large mt-6 w-full"
                    mat-flat-button
                    [color]="'primary'"
                    [disabled]="signUpForm.disabled"
                    (click)="signUp()"
                >
                    @if (!signUpForm.disabled) {
                        <span> Create your free account </span>
                    }
                    @if (signUpForm.disabled) {
                        <mat-progress-spinner
                            [diameter]="24"
                            [mode]="'indeterminate'"
                        ></mat-progress-spinner>
                    }
                </button>
            </form>
        </div>
    </div>
    <div
        class="relative hidden h-full w-1/2 flex-auto items-center justify-center overflow-hidden bg-gray-800 p-16 dark:border-l md:flex lg:px-28"
    >
        <!-- Background -->
        <!-- Rings -->
        <!-- prettier-ignore -->
        <svg class="absolute inset-0 pointer-events-none"
             viewBox="0 0 960 540" width="100%" height="100%" preserveAspectRatio="xMidYMax slice" xmlns="http://www.w3.org/2000/svg">
            <g class="text-gray-700 opacity-25" fill="none" stroke="currentColor" stroke-width="100">
                <circle r="234" cx="196" cy="23"></circle>
                <circle r="234" cx="790" cy="491"></circle>
            </g>
        </svg>
        <!-- Dots -->
        <!-- prettier-ignore -->
        <svg class="absolute -top-16 -right-16 text-gray-700"
             viewBox="0 0 220 192" width="220" height="192" fill="none">
            <defs>
                <pattern id="837c3e70-6c3a-44e6-8854-cc48c737b659" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <rect x="0" y="0" width="4" height="4" fill="currentColor"></rect>
                </pattern>
            </defs>
            <rect width="220" height="192" fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)"></rect>
        </svg>
        <!-- Content -->
        <div class="relative z-10 w-full max-w-2xl">
            <div class="text-7xl font-bold leading-none text-gray-100">
                <div>Welcome to</div>
                <div>our community</div>
            </div>
            <div class="mt-6 text-lg leading-6 tracking-tight text-gray-400">
                Fuse helps developers to build organized and well coded
                dashboards full of beautiful and rich modules. Join us and start
                building your application today.
            </div>
            <div class="mt-8 flex items-center">
                <div class="flex flex-0 items-center -space-x-1.5">
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/female-18.jpg"
                    />
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/female-11.jpg"
                    />
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/male-09.jpg"
                    />
                    <img
                        class="h-10 w-10 flex-0 rounded-full object-cover ring-4 ring-gray-800 ring-offset-1 ring-offset-gray-800"
                        src="images/avatars/male-16.jpg"
                    />
                </div>
                <div class="ml-4 font-medium tracking-tight text-gray-400">
                    More than 17k people joined us, it's your turn
                </div>
            </div>
        </div>
    </div>
</div>
