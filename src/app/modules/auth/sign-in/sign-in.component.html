<div class="flex min-w-0 flex-auto flex-col items-center sm:flex-row sm:justify-center md:items-start md:justify-start">
    <div
        class="relative hidden h-full w-full flex-auto items-center justify-center overflow-hidden bg-gray-800 dark:border-l md:flex ">
        <!-- Background -->
        <!-- Rings -->
        <!-- prettier-ignore -->
       
        <!-- Dots -->
        <!-- prettier-ignore -->
        <!-- <img
                class="h-auto max-w-full flex-0 object-fill"
                src="images/banner/1.jpg"
            /> -->
            <app-carousel [images]="images" class="w-full max-w-full max-h-full"></app-carousel>
    </div>
    <div
        class="w-full px-4 py-8 sm:bg-card sm:w-auto sm:rounded-2xl sm:p-12 sm:shadow md:flex md:h-full md:w-1/2 md:items-center md:justify-center md:rounded-none md:p-16 md:shadow-none">
        <div class="mx-auto w-full max-w-80 sm:mx-0 sm:w-80">
            <!-- Logo -->
            <div class="flex flex-row w-full">
                <img src="images/logo/meinhardt-logo.png" /><span>{{version}}</span>
                
            </div>

            <!-- Title -->
            <div class="mt-8 text-4xl font-extrabold leading-tight tracking-tight">
                Sign in
            </div>
            <!-- <div class="mt-0.5 flex items-baseline font-medium">
                <div>Don't have an account?</div>
                <a
                    class="ml-1 text-primary-500 hover:underline"
                    [routerLink]="['/sign-up']"
                    >Sign up
                </a>
            </div> -->

            <!-- Alert -->
            <!-- <fuse-alert
                class="mt-8"
                [appearance]="'outline'"
                [showIcon]="false"
                [type]="'info'"
            >
                You are browsing <strong>Fuse Demo</strong>. Click on the "Sign
                in" button to access the Demo and Documentation.
            </fuse-alert> -->

            <!-- Alert -->
            @if (showAlert) {
            <fuse-alert class="mt-8" [appearance]="'outline'" [showIcon]="false" [type]="alert.type"
                [@shake]="alert.type === 'error'">
                {{ alert.message }}
            </fuse-alert>
            }

            <!-- Sign in form -->
            <form class="mt-8" [formGroup]="signInForm" #signInNgForm="ngForm">
                <!-- Email field -->
                <mat-form-field class="w-full">
                    <mat-label>Username</mat-label>
                    <input id="username" matInput [formControlName]="'username'" />
                    @if (signInForm.get('username').hasError('required')) {
                    <mat-error> Username is required </mat-error>
                    }
                </mat-form-field>
                <!-- Password field -->
                <mat-form-field class="w-full">
                    <mat-label>Password</mat-label>
                    <input id="password" matInput type="password" [formControlName]="'password'" #passwordField />
                    <button mat-icon-button type="button" (click)="
                            passwordField.type === 'password'
                                ? (passwordField.type = 'text')
                                : (passwordField.type = 'password')
                        " matSuffix>
                        @if (passwordField.type === 'password') {
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye'"></mat-icon>
                        }
                        @if (passwordField.type === 'text') {
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:eye-slash'"></mat-icon>
                        }
                    </button>
                    <mat-error> Password is required </mat-error>
                </mat-form-field>

                <!-- Actions -->
                <!-- <div
                    class="mt-1.5 inline-flex w-full items-center justify-between"
                >
                    <mat-checkbox
                        class="-ml-2"
                        [color]="'primary'"
                        [formControlName]="'rememberMe'"
                    >
                        Remember me
                    </mat-checkbox>
                    <a
                        class="text-md font-medium text-primary-500 hover:underline"
                        [routerLink]="['/forgot-password']"
                        >Forgot password?
                    </a>
                </div> -->

                <!-- Submit button -->
                <button class="fuse-mat-button-large mt-6 w-full" mat-flat-button [color]="'primary'"
                    [disabled]="signInForm.disabled" (click)="signIn()">
                    @if (!signInForm.disabled) {
                    <span> Sign in </span>
                    }
                    @if (signInForm.disabled) {
                    <mat-progress-spinner [diameter]="24" [mode]="'indeterminate'"></mat-progress-spinner>
                    }
                </button>

                <!-- Separator -->
                <!-- <div class="mt-8 flex items-center">
                    <div class="mt-px flex-auto border-t"></div>
                    <div class="text-secondary mx-2">Or continue with</div>
                    <div class="mt-px flex-auto border-t"></div>
                </div> -->

                <!-- Single sign-on buttons -->
                <!-- <div class="mt-8 flex items-center space-x-4">
                    <button class="flex-auto" type="button" mat-stroked-button>
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'feather:facebook'"
                        ></mat-icon>
                    </button>
                    <button class="flex-auto" type="button" mat-stroked-button>
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'feather:twitter'"
                        ></mat-icon>
                    </button>
                    <button class="flex-auto" type="button" mat-stroked-button>
                        <mat-icon
                            class="icon-size-5"
                            [svgIcon]="'feather:github'"
                        ></mat-icon>
                    </button>
                </div> -->
            </form>
        </div>
    </div>
</div>