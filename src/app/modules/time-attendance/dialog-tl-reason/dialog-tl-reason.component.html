<div>
    <div class="grid grid-cols-1 my-2 border-b-2 border-gray-300">
        <div class="flex flex-col gap-2 mb-2 mx-2">
            <div class="flex flex-row mt-2 gap-2">
                <div class="md:w-full" [formGroup]="form">
                    <mat-label class="font-bold mr-2">Deduct</mat-label>
                    <mat-radio-group aria-label="Select an option" [color]="'primary'" formControlName="deduct">
                        <mat-radio-button [value]="'Yes'">Yes </mat-radio-button>
                        <mat-radio-button [value]="'No'">No</mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="flex flex-row mt-2 gap-2">
                <div class="md:w-full" [formGroup]="form">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <mat-label class="font-bold">Reason</mat-label>
                        <textarea matInput [rows]="3" formControlName="reason"></textarea>
                        <mat-error><PERSON>son is required</mat-error>
                    </mat-form-field>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-row mt-2 justify-center">
        <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
            <button class="px-6 ml-3" mat-flat-button [color]="'primary'"(click)="Submit()">
                Save
            </button>
            <button class="px-6 ml-3 border-[1px] border-gray-200" mat-flat-button [color]="'warn'" (click)="onClose()">
                Cancel
            </button>
        </div>
    </div>
</div>