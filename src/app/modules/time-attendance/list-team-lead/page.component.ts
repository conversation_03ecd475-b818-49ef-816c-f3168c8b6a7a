import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { ReplaySubject, Subject, takeUntil } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { TimeAttendanceService } from '../page.service';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
@Component({
    selector: 'app-time-attendance-list-team-lead',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        MatButtonModule,
        MatInputModule,
        MatAutocompleteModule
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class ListTeamLeadComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;
    years: number[] = [];
    profile: any;
    months = [
        { value: '01', name: 'January' },
        { value: '02', name: 'February' },
        { value: '03', name: 'March' },
        { value: '04', name: 'April' },
        { value: '05', name: 'May' },
        { value: '06', name: 'June' },
        { value: '07', name: 'July' },
        { value: '08', name: 'August' },
        { value: '09', name: 'September' },
        { value: '10', name: 'October' },
        { value: '11', name: 'November' },
        { value: '12', name: 'December' }
    ];
    data: any;
    type: any;
    ///approver filter
    employeeFilter = new FormControl('');
    filterEmployee: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
    employee: any[] = [];
    constructor(
        private _service: TimeAttendanceService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,

    ) {
        this.type = this._activatedRoute.snapshot.data.type
        this.profile = JSON.parse(localStorage.getItem('user'))
        this.employee = this._activatedRoute.snapshot.data.employee
        const currentYear = new Date().getFullYear();
        this.form = this._fb.group({
            year: null,
            month: null,
            approvalId: null,
        })
        this.years = this.getPastAndFutureYears()
    }
    getPastAndFutureYears(): number[] {
        const currentYear = new Date().getFullYear();
        const years = [];

        for (let i = 1; i >= -2; i--) {
            years.push(currentYear + i);
        }

        return years;
    }
    ngOnInit(): void {
        const currentYear = new Date().getFullYear();
        const currentDate = new Date();
        let previousMonth = currentDate.getMonth();
        if (previousMonth === 0) {
            previousMonth = 11; // ธันวาคม
        } else {
            previousMonth -= 1;
        }
        this.form.patchValue({
            year: currentYear - (previousMonth === 11 ? 1 : 0),
            month: String(previousMonth + 1).padStart(2, '0'),
        });
        this.employeeFilter.valueChanges
            .pipe(takeUntil(this._onDestroy))
            .subscribe((value) => {
                this._filterEmployee();
            });
        setTimeout(() =>
            this.loadTable());
    }

    // * On destroy
    // */ 
    protected _onDestroy = new Subject<void>();

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        // let formValue = this.form.value
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            scrollX: true,
            order: [[2, 'asc']],
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'year': +this.form.value.year,
                    'month': +this.form.value.month,
                };
                if (this.type === 'team-lead') {
                    dataTablesParameters.filter = {
                        'year': +this.form.value.year,
                        'month': +this.form.value.month,
                        'filter.head': this.profile.id
                    }

                }
                if (this.type === 'admin') {
                    dataTablesParameters.filter = {
                        'year': +this.form.value.year,
                        'month': +this.form.value.month,
                        'filter.head': +this.form.value.approvalId || '',
                    }
                }
                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    },
                    error: () => {
                        this.toastr.error('Invalid');
                    }
                });
            },
            columns: [
                {
                    title: 'No.',
                    data: null,
                    className: 'w-10 text-center',
                    render: function (data, type, row, meta) {
                        return meta.row + 1;
                    }
                },
                {
                    title: 'Employee ID',
                    data: 'code',
                    defaultContent: '-',
                    className: 'text-center'
                },
                {
                    title: 'Firstname',
                    data: 'firstname',
                    defaultContent: '-',
                    className: 'text-center'
                },
                {
                    title: 'Lastname',
                    data: 'lastname',
                    defaultContent: '-',
                    className: 'text-center'
                },
                {
                    title: 'Month',
                    data: 'create_history_attendance_detail.month',
                    defaultContent: '-',
                    className: 'text-center'
                },
                {
                    title: 'Case 1',
                    data: 'create_history_attendance_detail.case1',
                    defaultContent: 0,
                    className: 'text-center'
                },
                {
                    title: 'Case 2',
                    data: 'create_history_attendance_detail.case2',
                    defaultContent: 0,
                    className: 'text-center'
                },
                {
                    title: 'Case 3',
                    data: 'create_history_attendance_detail.case3',
                    defaultContent: 0,
                    className: 'text-center'
                },
                {
                    title: 'Case 4',
                    data: 'create_history_attendance_detail.case4',
                    defaultContent: 0,
                    className: 'text-center'
                },
                ...(this.type != 'admin'
                    ? [{
                        title: 'Staff Review',
                        data: function (row: any) {
                            if (!row.create_history_attendance_detail?.confirmStatus) {
                                return '-';
                            }
                            switch (row.create_history_attendance_detail?.confirmStatus) {
                                case 'approve':
                                    return 'Reviewed';
                                case 'pending':
                                    return 'Wait Review';
                                default:
                                    return '-';
                            }
                        },
                        defaultContent: '',
                        className: 'text-center'
                    }] : []),
                ...(this.type == 'admin'
                    ? [{
                        title: 'Staff Review',
                        data: function (row: any) {
                            if (!row.create_history_attendance_detail?.confirmStatus) {
                                return '-';
                            }
                            switch (row.create_history_attendance_detail?.confirmStatus) {
                                case 'approve':
                                    return 'Yes';
                                case 'pending':
                                    return 'No';
                                default:
                                    return '-';
                            }
                        },
                        defaultContent: '',
                        className: 'text-center'
                    }] : []),
                ...(this.type == 'team-lead'
                    ? [{
                        title: 'TL Review',
                        data: function (row: any) {
                            if (!row.create_history_attendance_detail?.headConfirmStatus) {
                                return '-';
                            }
                            switch (row.create_history_attendance_detail?.headConfirmStatus) {
                                case 'approve':
                                    return 'Reviewed';
                                case 'pending':
                                    return 'Wait Review';
                                default:
                                    return '-';
                            }
                        },
                        defaultContent: '',
                        className: 'text-center'
                    }] : []),
                ...(this.type === 'admin'
                    ? [{
                        title: 'Head Review',
                        data: function (row: any) {
                            if (!row.create_history_attendance_detail?.headConfirmStatus) {
                                return '-';
                            }
                            switch (row.create_history_attendance_detail?.headConfirmStatus) {
                                case 'approve':
                                    return 'Yes';
                                case 'pending':
                                    return 'No';
                                default:
                                    return '-';
                            }
                        },
                        defaultContent: '',
                        className: 'text-center'
                    }]
                    : []),

                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }
            ]
        };

    }

    review(id: any, year: any, month: any, status: any) {
        console.log(year, 'Year')
        if (!status) {
            if (this.type == 'team-lead') {
                this._router.navigate([`/time-attendance/team-lead-review/${id}/${year}/${month}/pending`])
            } else if (this.type == 'admin') {
                this._router.navigate([`/time-attendance/admin-review/${id}/${year}/${month}/pending`])
            }
        } else {
            if (this.type == 'team-lead') {
                this._router.navigate([`/time-attendance/team-lead-review/${id}/${year}/${month}/${status}`])
            } else if (this.type == 'admin') {
                this._router.navigate([`/time-attendance/admin-review/${id}/${year}/${month}/${status}`])
            }
        }

    }

    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }


    onChangeType() {
        this.rerender()
    }

    clearData() {
        this.form.reset();
        const currentYear = new Date().getFullYear();
        const currentDate = new Date();
        let previousMonth = currentDate.getMonth();
        if (previousMonth === 0) {
            previousMonth = 11; // ธันวาคม
        } else {
            previousMonth -= 1;
        }
        this.form.patchValue({
            year: currentYear - (previousMonth === 11 ? 1 : 0),
            month: String(previousMonth + 1).padStart(2, '0'),
        });
        this.rerender();
    }

    protected _filterEmployee() {
        if (!this.employee) {
            return;
        }
        let search = this.employeeFilter.value;
        // console.log(search, 's');

        if (!search) {
            this.filterEmployee.next(this.employee.slice());
            return;
        } else {
            search = search.toString().toLowerCase();
        }

        // กรองข้อมูลโดยค้นหาใน firstname และ lastname
        this.filterEmployee.next(
            this.employee.filter(item =>
                (item.firstname.toLowerCase() + ' ' + item.lastname.toLowerCase()).includes(search) ||
                item.firstname.toLowerCase().includes(search) ||
                item.lastname.toLowerCase().includes(search)
            )
        );
    }

    onSelect(event: any, type: any) {
        if (!event) {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Selected');
            return;
        }

        const selectedData = event; // event จะเป็นออบเจ็กต์ item

        if (selectedData) {
            this.form.patchValue({
                approvalId: selectedData.id,
            });
            this.employeeFilter.setValue(`${selectedData.firstname} ${selectedData.lastname}`);
        } else {
            if (this.employeeFilter.invalid) {
                this.employeeFilter.markAsTouched(); // กำหนดสถานะ touched เพื่อแสดง mat-error
            }
            console.log('No Employee Found');
            return;
        }
    }
}
