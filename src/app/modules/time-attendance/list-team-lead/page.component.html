<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex flex-col md:flex-row justify-between px-4 pb-2 my-5 border-b-2 border-gray-400">
      <div>
        <h2 *ngIf="type === 'team-lead'"
          class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Team Lead Review
        </h2>
        <h2 *ngIf="type === 'admin'"
          class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Adjust Department Head/Teamlead
        </h2>
      </div>
      <div class="flex flex-row justify-end pb-2 my-2 gap-2">
        <button mat-flat-button [color]="'primary'" (click)="this.rerender()">
          <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
          <span class="ml-2"> Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2"> Reset</span>
        </button>
      </div>
    </div>
    <form [formGroup]="form" class="px-4">
      <div class="flex flex-col md:flex-row pb-0 my-2 gap-2">

        <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
          <mat-label>Year</mat-label>
          <mat-select [formControlName]="'year'" placeholder="Select year">
            <mat-option *ngFor="let item of years;" [value]="item">
              {{item}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/4">
          <mat-label>Month</mat-label>
          <mat-select [formControlName]="'month'" placeholder="Select month">
            <mat-option *ngFor="let item of months;" value="{{item.value}}">
              {{item.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div
          class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-full md:w-3/12">
          <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-4/4">
            <mat-label>Search by Name</mat-label>
            <input matInput [formControl]="employeeFilter" [matAutocomplete]="AutoComplete"
              placeholder="Search Approver" />
            <mat-autocomplete #AutoComplete="matAutocomplete"
              (optionSelected)="onSelect($event.option.value, 'manual')">
              <mat-option *ngFor="let item of filterEmployee | async" [value]="item">
                {{item.firstname}} {{item.lastname}}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
      </div>

    </form>
    <div class="overflow-x-auto whitespace-nowrap mt-2 max-w-full">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
      </table>
    </div>
  </div>
</div>
<ng-template #btNg let-data="adtData">

  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item
      (click)="review(data.id, this.form.value.year, this.form.value.month,data.create_history_attendance_detail?.confirmStatus)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>Review</span>
    </button>
  </mat-menu>
</ng-template>