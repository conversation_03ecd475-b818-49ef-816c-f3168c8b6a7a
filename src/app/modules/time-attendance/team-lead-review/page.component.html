<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
      <div>
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Team Lead Review
        </h2>
      </div>

    </div>
    <div class="flex flex-row justify-between pb-0 my-2 gap-2">
      <div class="flex flex-col justify-end w-full">
        <p class="text-lg font-bold">Sitthisak Opakulchanok</p>
        <p class="text-lg font-bold text-gray-400">November, 2024</p>
      </div>
      <div class="flex flex-col text-left w-full items-end">
        <div
          class="flex flex-col gap-2 border-[1px] border-orange-500 rounded-md bg-orange-100 w-fit p-2 text-orange-500 font-semibold uppercase text-xs">
          <p>case 1 = Late > 30 Mins , case 2 = Late > 60 Mins</p>
          <p>case 3 = Late > 90 Mins , case 4 = Late > 150 Mins</p>
        </div>
      </div>
    </div>
    <div class="my-2 border-b-2 border-gray-300 overflow-auto">
      <table
        class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-[1px] border-black my-2 ">
        <thead class="text-md text-white uppercase bg-blue-900 dark:bg-gray-700 dark:text-gray-400">

          <tr class="border-[1px] border-black">
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Date
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Day
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Time in
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Time Out
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Calc. Hrs
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Work Hrs
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Ann
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Cas
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Sick
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              WOP
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              MTN
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              OTH
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              TIL
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Train
            </th>

            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Case 1
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Case 2
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Case 3
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Case 4
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center">
              Deduct
            </th>
            <th scope="col" class="px-2 py-2 border-[1px] border-black text-center w-60">
              Reason
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of itemData; let i = index">
            <tr class="hover:bg-slate-100 cursor-pointer" [ngStyle]="{'background-color': item.color || 'transparent'}"
              (click)="addReason()">
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{(item.date | date: 'dd/MM/yyyy') ??
                '-'}}</td>
              <td class="px-2 py-2 text-md text-left border-[1px] border-black ">{{item.day ?? '-'}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{transformTime(item.timeIn)}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{transformTime(item.timeOut)}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.calcHrs}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.workHrs}}</td>
              <ng-container *ngFor="let leave of item.leaveType">
                <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{leave.useDay ?? 0}}</td>
              </ng-container>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case1 ?? 0}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case2 ?? 0}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case3 ?? 0}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.case4 ?? 0}}</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">No</td>
              <td class="px-2 py-2 text-md text-center border-[1px] border-black ">{{item.reason ?? ''}}</td>
            </tr>
          </ng-container>
          <ng-container *ngIf="itemData.length === 0">
            <tr class="hover:bg-slate-100">
              <td class="px-2 py-2 text-md text-center" colspan="19"> No data</td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
    <div class="flex flex-row mt-2 justify-center" *ngIf="this.itemData.length > 0">
      <div class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
        <button class="px-6 ml-3" mat-flat-button [color]="'primary'">
          Sumbit
        </button>
        <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>