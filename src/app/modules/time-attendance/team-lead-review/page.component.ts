import { CommonModule, Cur<PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { orderBy } from 'lodash';
import { TimeAttendanceService } from '../page.service';
import { MatInputModule } from '@angular/material/input';
import { DialogReasonComponent } from '../dialog-reason/dialog-reason.component';
import { DialogTeamLeadReasonComponent } from '../dialog-tl-reason/dialog-tl-reason.component';
@Component({
    selector: 'app-time-attendance-team-lead-review',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule,
        MatInputModule
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class TeamLeadReviewComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    formStaffReview: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    title: any;
    level: any;
    department: any[] = [];
    itemData: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;

    employeeId: any;
    data: any;
    constructor(
        private _service: TimeAttendanceService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _toast: ToastrService,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,

    ) {
        const currentYear = new Date().getFullYear();
        this.employeeId = this._activatedRoute.snapshot.params.id

        this.formStaffReview = this._fb.group({
            employee_id: null,
            month: null,
            year: null,
            timeAttendance: this._fb.array([])
        })

    }
    ngOnInit(): void {

    }

    ngAfterViewInit() {

    }

    backTo() {
        this._router.navigate(['/time-attendance/list-team-lead-review'])
    }


    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event

    }

    onChangeType() {

    }

    clearData() {
        this.itemData = [];
    }

    transformTime(time: string) {
        if (time) {
            const timeWithSeconds = time;
            const timeWithoutSeconds = timeWithSeconds.slice(0, 5);
            // console.log(timeWithoutSeconds)
            return timeWithoutSeconds;
        } else {
            return '-';
        }
    }

    getnerateTimeAttendance() {
        let formValue = this.form.value
        const error = this.validateYearAndMonth(formValue.year, formValue.month);

        if (error) {
            this._toast.error(error)
        } else {

            this._toast.success('Valid')
        }
    }

    validateYearAndMonth(year: any, month: any): string | null {
        const now = new Date();
        let currentYear = now.getFullYear();
        let currentMonth = now.getMonth() + 1; // getMonth() ให้ค่าตั้งแต่ 0-11 ดังนั้นต้องบวก 1
        const monthNumber = parseInt(month, 10);
        if (year > currentYear || (year === currentYear && monthNumber >= currentMonth)) {
            return "The provided year and month must be earlier than the current year and month.";
        }
        return null;
    }

    addReason() {
        const dialogRef = this._matDialog.open(DialogTeamLeadReasonComponent, {
            width: '400px',
            maxHeight: '90vh',
        });
        dialogRef.afterClosed().subscribe(item => {

            // this._changeDetectorRef.markForCheck();
        });
    }

}
