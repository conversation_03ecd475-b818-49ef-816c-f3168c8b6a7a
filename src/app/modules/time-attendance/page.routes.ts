import { ActivatedRouteSnapshot, Routes } from '@angular/router';
import { TimeAttendanceComponent } from './time-attendance.component';
import { ListHistoryComponent } from './list-history/page.component';
import { StaffReviewComponent } from './staff-review/page.component';
import { ListTeamLeadComponent } from './list-team-lead/page.component';
import { TeamLeadReviewComponent } from './team-lead-review/page.component';
import { EmployeeService } from '../hr/employee/page.service';
import { inject } from '@angular/core';
import { TimeAttendanceService } from './page.service';
import { CheckInComponent } from './check-in/page.component';

export default [
    {
        path: '',
        component: TimeAttendanceComponent,
        children: [
            {
                path: 'history',
                component: ListHistoryComponent,
            },
            {
                path: 'admin/history',
                component: ListHistoryComponent,
            },
            {
                path: 'staff-review',
                component: StaffReviewComponent,
                resolve: {
                    employee: () => inject(EmployeeService).getEmployee(),
                },
                data: {
                    type: 'staff'
                }
            },

            {
                path: 'check-in',
                component: CheckInComponent,
                resolve: {
                    employee: () => inject(EmployeeService).getEmployee(),
                },
                data: {
                    type: 'staff'
                }
            },
            {
              path: 'staff-reviewed/:year/:month/:empid/:status',
              component: StaffReviewComponent,
              resolve: {
                  employee: () => inject(EmployeeService).getEmployee(),
              },
              data: {
                  type: 'staff'
              }
          },
            {
                path: 'list-team-lead-review',
                component: ListTeamLeadComponent,
                data: {
                type: 'team-lead'
              }
            },
            {
              path: 'list-admin-review',
              component: ListTeamLeadComponent,
              resolve: {
                employee: () => inject(EmployeeService).getEmployee(),
            },
              data: {
                type: 'admin',
            }
          },
            {
                path: 'team-lead-review/:id/:year/:month/:status',
                component: StaffReviewComponent,
                resolve: {
                    employeeAttendance: (route: ActivatedRouteSnapshot) => {
                        let id = route.paramMap.get('id');
                        let year = route.paramMap.get('year');
                        let month = route.paramMap.get('month');
                        return inject(TimeAttendanceService).getEmployeeAttendance(+year, +month,+id);
                    },
                    employee: (route: ActivatedRouteSnapshot) => {
                        let id = route.paramMap.get('id');
                        return inject(EmployeeService).getUserId(+id);
                    },
                },
                data: {
                    type: 'team-lead'
                }


            },
            {
              path: 'admin-review/:id/:year/:month/:status',
              component: StaffReviewComponent,
              resolve: {
                  employeeAttendance: (route: ActivatedRouteSnapshot) => {
                      let id = route.paramMap.get('id');
                      let year = route.paramMap.get('year');
                      let month = route.paramMap.get('month');
                      return inject(TimeAttendanceService).getEmployeeAttendance(+year, +month,+id);
                  },
                  employee: (route: ActivatedRouteSnapshot) => {
                      let id = route.paramMap.get('id');
                      return inject(EmployeeService).getUserId(+id);
                  },
              },
              data: {
                  type: 'admin'
              }


          },

        ]
    },

] as Routes;
