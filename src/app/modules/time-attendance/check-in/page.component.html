<div *ngIf="isLoading" class="fixed inset-0 bg-white bg-opacity-60 flex items-center justify-center z-50">
    <div class="w-12 h-12 border-4 border-blue-700 border-b-transparent rounded-full animate-spin"></div>
  </div>
  
<div class="flex flex-col flex-auto min-w-0">
    <div class="flex-auto sm:px-10 bg-card m-4 rounded-md">
        <div class="flex flex-row justify-between pb-2 my-4 border-b-2 border-gray-400">
            <div class="px-5">
                <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
                    Check In
                </h2>
            </div>
            <div class="flex flex-col md:flex-row justify-end pb-2 my-2 gap-2" *ngIf="this.type === 'staff'">
                <button mat-flat-button [color]="'primary'" (click)="generateTimeAttendance()">
                    <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
                    <span class="ml-2"> Search</span>
                </button>
                <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
                    <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
                    <span class="ml-2"> Reset</span>
                </button>
            </div>
        </div>

        <!-- แสดงเฉพาะบน Mobile -->
        <div class="flex flex-col justify-center pb-2 my-5 md:hidden items-center gap-2">
            <div class="my-2 overflow-auto w-full">
                <div #map id="map" class="w-full h-80"></div>
            </div>
            <div class="mx-4 my-2 overflow-auto h-auto">
                <p>{{address}}</p>
            </div>
            <div *ngIf="previewImage" class="flex justify-center mt-4 w-full">
                <!-- <p>รูปภาพที่เลือก:</p> -->
                <img [src]="previewImage" class="w-60 h-auto rounded shadow" alt="preview" />
            </div>
       
            <div class="flex flex-row mt-2 justify-between w-full px-2 gap-2">
                <div class="w-2/3">
                    <input #fileInput type="file" accept="image/*" capture="environment" hidden
                        (change)="onFileSelected($event)" />
    
                    <button mat-flat-button [color]="'accent'" (click)="openCamera()" class="w-full">
                        <mat-icon svgIcon="heroicons_mini:camera"></mat-icon> 
                        &nbsp;
                        Take A Photo
                    </button>
                </div>
                <div
                    class="items-end text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate w-1/2">
                    <button class="px-6 w-full" mat-flat-button [color]="'primary'" (click)="Submit()" >
                        Save
                    </button>
                    <!-- <button class="px-6 ml-3" mat-flat-button [color]="'warn'" (click)="backTo()">
                        Cancel
                    </button> -->
                </div>
            </div>
        </div>

        <!-- แสดงเฉพาะบน Tablet และ Desktop -->
        <div class="hidden md:flex h-screen justify-center items-center">
            <p class="text-xl font-semibold text-gray-600 text-center">
                Not available on web or tablet
            </p>
        </div>
    </div>
</div>