import { CommonModule, C<PERSON><PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { ReplaySubject, Subject, take, takeUntil } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { orderBy } from 'lodash';
import { TimeAttendanceService } from '../page.service';
import { MatInputModule } from '@angular/material/input';
import { DialogReasonComponent } from '../dialog-reason/dialog-reason.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Min2HrsPipe } from 'app/min2-hrs.pipe';
import { LongdoMapService } from 'app/modules/share/longdo-map.service';
import { GeolocationService } from '@ng-web-apis/geolocation';
import { ImageUploadComponent } from 'app/core/auth/common/image-upload/image-upload.component';
import { ImageUploadService } from 'app/core/auth/common/image-upload/image-upload.service';
import { fuseAnimations } from '@fuse/animations';
import { FuseAlertComponent, FuseAlertType } from '@fuse/components/alert';
declare var longdo: any;
@Component({
  selector: 'app-time-attendance-check-in',
  standalone: true,
  providers: [
    CurrencyPipe,
    DecimalPipe,
    Min2HrsPipe
  ],
  imports: [
    CommonModule,
    DataTablesModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    MatSelectModule,
    MatInputModule,
    MatAutocompleteModule,
    ImageUploadComponent,
    FuseAlertComponent,

  ],
  animations: fuseAnimations,
  templateUrl: './page.component.html',
  styleUrl: './page.component.scss',
  changeDetection: ChangeDetectionStrategy.Default,
})
export class CheckInComponent implements OnInit, AfterViewInit {
  dtOptions: any = {};
  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
  @ViewChild('btNg') btNg: any;
  @ViewChild(DataTableDirective, { static: false })
  dtElement: DataTableDirective;
  form: FormGroup
  formStaffReview: FormGroup
  @Input() storeId: any;
  @Output() dataArrayChange = new EventEmitter<any[]>();
  previewImage: string | null = null;
  alert: { type: FuseAlertType; message: string } = {
    type: 'error',
    message: 'Cannot get location',
  };
  showAlert: boolean = false;

  formFieldHelpers: string[] = ['fuse-mat-dense'];
  user: any;
  data: any;
  type: any;
  profile: any
  status: any;
  empid: any;
  map: any;
  position: GeolocationPosition;
  address: any;
  marker: any;
  myid: any;
  driver: any;
  public lat: number;
  public lng: number;

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>

  constructor(
    private _service: TimeAttendanceService,
    private fuseConfirmationService: FuseConfirmationService,
    private toastr: ToastrService,
    public _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _toast: ToastrService,
    private currencyPipe: CurrencyPipe,
    private _fb: FormBuilder,
    private readonly geolocation$: GeolocationService,
    private readonly longdoMapService: LongdoMapService,
    private imageUploadService: ImageUploadService,


  ) {

    this.form = this._fb.group({
      employeeCode: '',
      latitude: '',
      longitude: '',
      image: '',
    })

    this.user = JSON.parse(localStorage.getItem('user'))

  }
  isLoading = false;

  async ngOnInit(): Promise<void> {
    this.showAlert = true;
    this.drawMap();
    this.isLoading = true;

    try {
      const position = await this.getCurrentPosition();
      this.position = position;

      this.map.location(
        { lon: position.coords.longitude, lat: position.coords.latitude },
        true
      );

      this.map.Overlays.clear();
      this.marker = new longdo.Marker(
        {
          lon: position.coords.longitude,
          lat: position.coords.latitude,
        },
        {
          title: 'Marker',
          icon: {
            url: 'https://asha-tech.co.th/pin.png',
          },
        }
      );

      this.map.Overlays.add(this.marker);
      this.map.zoom(18, true);

      this.longdoMapService.getAddress(position.coords.latitude, position.coords.longitude).subscribe({
        next: (resp) => {
          const fullAddress = [
            resp.aoi || "",
            resp.road || "",
            resp.subdistrict || "",
            resp.district || "",
            resp.province || "",
            resp.postcode || "",
            resp.country || ""
          ].filter(part => part !== "").join(" ");

          this.address = fullAddress;

          this.form.patchValue({
            employeeCode: this.user?.code,
            latitude: resp.road_lat,
            longitude: resp.road_lon,
          });
          this.isLoading = false;
        },
        error: (err) => {
          this.showAlert = true;
          this.isLoading = false;
          this.toastr.error('Please try again.');
        }
      });

    } catch (error) {
      this.showAlert = true;
      this.isLoading = false;
      this.toastr.error('Please try again.');
    }
  }

  getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      });
    });
  }


                   /**
     * On destroy
     */  protected _onDestroy = new Subject<void>();

  ngAfterViewInit() {

  }

  drawMap() {
    this.map = new longdo.Map({
      placeholder: document.getElementById('map'),
      input: false,
      lastView: false,
    });

    this.map.Ui.Zoombar.visible(false);
    this.map.Ui.LayerSelector.visible(false);
    this.map.Ui.Crosshair.visible(false);
  }

  ngOnDestroy(): void {
    // Do not forget to unsubscribe the event
  }
  backTo() {
    this._router.navigate(['/report/time-attendance-view-by-user-view'])
  }

  openCamera() {
    this.fileInput.nativeElement.click();
  }
  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];

    this.resizeAndConvertToBase64(file).then((base64: string) => {
      this.previewImage = base64;

      this.form.patchValue({
        image: base64
      });
    }).catch(err => {
      this.toastr.error('Error resizing and converting image');
      console.error(err);
    });
  }


  private convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  }

  Submit() {
    let formValue = this.form.value

    if (formValue.image === null || formValue.image === '' || formValue.image === undefined) {
      this.toastr.error('Please take a photo.')
      return;
    }
    const confirmation = this.fuseConfirmationService.open({
      title: "Do you want to save data?",
      icon: {
        show: true,
        name: "heroicons_outline:exclamation-triangle",
        color: "primary"
      },
      actions: {
        confirm: {
          show: true,
          label: "Save",
          color: "primary"
        },
        cancel: {
          show: true,
          label: "Cancel"
        }
      },
      dismissible: false
    })
    confirmation.afterClosed().subscribe(
      result => {
        if (result == 'confirmed') {
          this._service.checkIn(formValue).subscribe({
            error: (err) => {
              this.toastr.error(err.error.message)
            },
            complete: () => {
              this._router.navigate(['/report/time-attendance-view-by-user-view'])
              this.toastr.success('Successed')
            },
          });
        }
      }
    )
  }

  resizeAndConvertToBase64(file: File, maxWidth = 800, maxHeight = 800): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event: any) => {
        const img = new Image();
        img.src = event.target.result;

        img.onload = () => {
          const canvas = document.createElement('canvas');

          let width = img.width;
          let height = img.height;

          // ปรับขนาดให้อยู่ในกรอบ maxWidth x maxHeight
          if (width > maxWidth || height > maxHeight) {
            if (width > height) {
              height = Math.round((height * maxWidth) / width);
              width = maxWidth;
            } else {
              width = Math.round((width * maxHeight) / height);
              height = maxHeight;
            }
          }

          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          if (!ctx) return reject('Canvas context is null');

          ctx.drawImage(img, 0, 0, width, height);
          const base64 = canvas.toDataURL('image/jpeg', 0.8); // ปรับ quality ได้ (0.1 - 1)
          resolve(base64);
        };

        img.onerror = (err) => reject(err);
      };

      reader.onerror = (err) => reject(err);
      reader.readAsDataURL(file);
    });
  }

}
