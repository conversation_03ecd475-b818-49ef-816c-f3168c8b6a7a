import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';


import { toUpper } from 'lodash';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { contacts } from './mock-data';
import { removeEmpty } from 'app/helper';
import { param } from 'jquery';

@Injectable({
  providedIn: 'root'
})
export class TimeAttendanceService {

  private _data: BehaviorSubject<any[] | null> = new BehaviorSubject(null);

  constructor(private http: HttpClient) { }


  getAll(dataTablesParameters: any) {
    console.log(dataTablesParameters);

    const { columns, order, search, start, length, filter } = dataTablesParameters;
    const page = start / length + 1;
    const column = columns[order[0].column].data;
    const dir = toUpper(order[0].dir);
    const sortBy = column + ':' + dir;

    return this.http.get(`${environment.apiUrl}/api/employee/attendance-history-check-datatables`, {
      params: {
        page: page,
        limit: length,
        sortBy: sortBy,
        search: search.value,
        ...removeEmpty(filter)
      }
    }).pipe(
      map((resp: any) => {
        resp.data.forEach((e: any, i: number) => e.no = start + i + 1);
        return resp;
      })
    );
  }

  getEmployeeAttendance(year: number, month: number, employeeId: number) {
    return this.http.get(`${environment.apiUrl}/api/employee/${employeeId}/attendances`, {
      params: {
        year: year?.toString(),
        month: month?.toString()
      }
    });
  }

  updateStatus(id: number, data: object) {
    return this.http.post(`${environment.apiUrl}/api/employee/${id}/attendances`, data)
  }

  checkIn(data: any) {
    return this.http.post(`${environment.apiUrl}/api/check-in`, data)
  }

  updateReason(id: number, data: object) {
    return this.http.put(`${environment.apiUrl}/api/attandances/${id}`, data)
  }
}
