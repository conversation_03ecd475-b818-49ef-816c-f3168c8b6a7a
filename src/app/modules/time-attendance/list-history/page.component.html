<div class="flex flex-col flex-auto min-w-0">
  <div class="flex-auto sm:px-10 bg-card m-4 py-4 rounded-md">
    <div class="flex mx-2 flex-col md:flex-row justify-between pb-2 my-5 border-b-2 border-gray-400">
      <div>
        <h2 class="truncate text-3xl font-extrabold leading-7 tracking-tight sm:leading-10 md:text-4xl">
          Review Time Attendance
        </h2>
      </div>
      <div class="flex flex-row justify-end pb-2 my-2 gap-2">
        <button mat-flat-button [color]="'primary'" (click)="this.rerender()">
          <mat-icon svgIcon="heroicons_outline:magnifying-glass"></mat-icon>
          <span class="ml-2"> Search</span>
        </button>
        <button mat-flat-button [color]="'accent'" (click)="this.clearData()">
          <mat-icon svgIcon="heroicons_outline:arrow-path"></mat-icon>
          <span class="ml-2"> Reset</span>
        </button>
      </div>
    </div>
    <form [formGroup]="form">
      <div class="flex flex-row justify-between pb-0 my-2 gap-2 border-b-2 border-gray-400">
          <div class="flex flex-col mx-2 md:flex-row w-full md:w-2/3 gap-2">
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3">
                  <mat-label>Year</mat-label>
                  <mat-select [formControlName]="'year'" placeholder="Select year">
                      <mat-option *ngFor="let item of years;" [value]="item">
                          {{item}}
                      </mat-option>
                  </mat-select>
              </mat-form-field>
              <mat-form-field [ngClass]="formFieldHelpers" class="w-full md:w-1/3">
                  <mat-label>Month</mat-label>
                  <mat-select [formControlName]="'month'" placeholder="Select month">
                      <mat-option *ngFor="let item of months;" value="{{item.value}}">
                          {{item.name}}
                      </mat-option>
                  </mat-select>
              </mat-form-field>
          </div>
      </div>
  </form>
    <div class="overflow-x-auto whitespace-nowrap mt-2 max-w-full">
      <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="row-border hover w-full">
      </table>
    </div>
  </div>
</div>
<ng-template #btNg let-data="adtData">
  <button mat-icon-button [color]="'primary'" [matMenuTriggerFor]="menu">
    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:cog-8-tooth'"></mat-icon>
  </button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item (click)="navigate(data)">
      <mat-icon [svgIcon]="'heroicons_solid:pencil-square'"></mat-icon>
      <span>View</span>
    </button>
  </mat-menu>
</ng-template>
