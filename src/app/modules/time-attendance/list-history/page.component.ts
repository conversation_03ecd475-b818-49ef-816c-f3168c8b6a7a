import { CommonModule, CurrencyPipe, DecimalPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';

import { ADTSettings } from 'angular-datatables/src/models/settings';
import { Subject } from 'rxjs';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { ToastrService } from 'ngx-toastr';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { DateTimeToISO, DateTimeToSQL } from 'app/helper';
import { orderBy } from 'lodash';
import { TimeAttendanceService } from '../page.service';
@Component({
    selector: 'app-time-attendance-list-history',
    standalone: true,
    providers: [
        CurrencyPipe,
        DecimalPipe
    ],
    imports: [
        CommonModule,
        DataTablesModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatFormFieldModule,
        ReactiveFormsModule,
        FormsModule,
        MatSelectModule
    ],
    templateUrl: './page.component.html',
    styleUrl: './page.component.scss',
    changeDetection: ChangeDetectionStrategy.Default,
})
export class ListHistoryComponent implements OnInit, AfterViewInit {
    dtOptions: any = {};
    dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();
    @ViewChild('btNg') btNg: any;
    @ViewChild(DataTableDirective, { static: false })
    dtElement: DataTableDirective;
    form: FormGroup
    @Input() storeId: any;
    @Output() dataArrayChange = new EventEmitter<any[]>();
    itemType: any[] = [];
    formFieldHelpers: string[] = ['fuse-mat-dense'];

    title: any;
    level: any;
    department: any[] = [];
    leaveType: any;
    employeeType: any;
    workShift: any;
    approval: any;
    user: any;
    data: any;
    years: number[] = [];
    year: any = ''
    month: any = ''
    months = [
        { value: '01', name: 'January' },
        { value: '02', name: 'February' },
        { value: '03', name: 'March' },
        { value: '04', name: 'April' },
        { value: '05', name: 'May' },
        { value: '06', name: 'June' },
        { value: '07', name: 'July' },
        { value: '08', name: 'August' },
        { value: '09', name: 'September' },
        { value: '10', name: 'October' },
        { value: '11', name: 'November' },
        { value: '12', name: 'December' }
    ];
    constructor(
        private _service: TimeAttendanceService,
        private fuseConfirmationService: FuseConfirmationService,
        private toastr: ToastrService,
        public dialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private currencyPipe: CurrencyPipe,
        private _fb: FormBuilder,

    ) {
        this.user = JSON.parse(localStorage.getItem('user'))
        const currentYear = new Date().getFullYear();
        const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
        console.log(currentMonth)
        this.form = this._fb.group({
            year: currentYear,
            month: currentMonth,
        })
        this.years = [currentYear - 2, currentYear - 1, currentYear, currentYear + 1];
    }
    ngOnInit(): void {
        const currentYear = new Date().getFullYear();
        const currentDate = new Date();
        let previousMonth = currentDate.getMonth();

        if (previousMonth === 0) {
            previousMonth = 11; // ธันวาคม
        } else {
            previousMonth -= 1;
        }
        this.form.patchValue({
            year: currentYear - (previousMonth === 11 ? 1 : 0),
            month: String(previousMonth + 1).padStart(2, '0'),
        });
        setTimeout(() =>
            this.loadTable());
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.dtTrigger.next(this.dtOptions);
        }, 200);
    }

    ngOnDestroy(): void {
        // Do not forget to unsubscribe the event
        this.dtTrigger.unsubscribe();
    }

    loadTable(): void {
        console.log(this._router.url);
        let employeeId = ''
        // let formValue = this.form.value
        if (this._router.url === '/time-attendance/admin/history') {
            employeeId = ''
        } else if (this._router.url !== '/time-attendance/admin/history') {
            employeeId = this.user?.id
        }
        this.dtOptions = {
            pagingType: 'full_numbers',
            serverSide: true,
            scrollX: true,
            order: [[2, 'asc']],
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.filter = {
                    'filter.id': employeeId,
                    'year': this.form.value.year,
                    'month': this.form.value.month,
                }
                this._service.getAll(dataTablesParameters).subscribe({
                    next: (resp: any) => {
                        callback({
                            recordsTotal: resp.meta.totalItems,
                            recordsFiltered: resp.meta.totalItems,
                            data: resp.data
                        });
                    }, error: () => {
                        this.toastr.error('Invalid')
                    }
                })
            },
            columns: [
                {
                    title: 'No.',
                    data: null,
                    className: 'w-10 text-center',
                    render: function (data, type, row, meta) {
                        return meta.row + 1;
                    }
                },
                {
                    title: 'Employee ID',
                    data: 'code',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Firstname',
                    data: 'firstname',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Lastname',
                    data: 'lastname',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Month',
                    data: 'create_history_attendance_detail.month',
                    defaultContent: '-',
                    className: 'text-left'
                },
                {
                    title: 'Case 1',
                    data: 'create_history_attendance_detail.case1',
                    defaultContent: 0,
                    className: 'text-left'
                },
                {
                    title: 'Case 2',
                    data: 'create_history_attendance_detail.case2',
                    defaultContent: 0,
                    className: 'text-left'
                },
                {
                    title: 'Case 3',
                    data: 'create_history_attendance_detail.case3',
                    defaultContent: 0,
                    className: 'text-left'
                },
                {
                    title: 'Case 4',
                    data: 'create_history_attendance_detail.case4',
                    defaultContent: 0,
                    className: 'text-left'
                },


                {
                    title: 'Staff Review',
                    data: function (row: any) {
                        if (!row.create_history_attendance_detail?.confirmStatus) {
                            return '-';
                        }
                        switch (row.create_history_attendance_detail?.confirmStatus) {
                            case 'approve':
                                return 'Reviewed';
                            case 'pending':
                                return 'Wait Review';
                            default:
                                return '-';
                        }
                    },
                    defaultContent: '',
                    className: 'text-center'
                },
                {
                    title: 'TL Review',
                    data: function (row: any) {
                        switch (row.create_history_attendance_detail?.headConfirmStatus) {
                            case 'reviewed':
                                return 'Reviewed';
                            case 'approve':
                                return 'Reviewed';
                            case 'pending':
                                return 'Wait Review';
                            default:
                                return '-';
                        }


                    },
                    defaultContent: '',

                    className: 'text-left'
                },
                {
                    title: '',
                    data: null,
                    defaultContent: '',
                    ngTemplateRef: {
                        ref: this.btNg,
                    },
                    className: 'w-15 text-center'
                }

            ]
        }
    }



    rerender(): void {
        this.dtElement.dtInstance.then(dtInstance => {
            // Destroy the table first
            dtInstance.destroy();
            // Call the dtTrigger to rerender again
            this.dtTrigger.next(null);
        });
    }


    onChangeType() {
        this.rerender()
    }

    clearData() {
        this.form.reset();
        this.rerender();
    }
    navigate(data: any) {
        console.log(data, 'Data')
        if (data.create_history_status == false) {
            this._router.navigate([`/time-attendance/staff-reviewed/${this.form.value.year}/${this.form.value.month}/${data.id}/pending`])
        } else {
            this._router.navigate([`/time-attendance/staff-reviewed/${data.create_history_attendance_detail?.year}/${data.create_history_attendance_detail?.month}/${data.id}/${data.create_history_attendance_detail?.confirmStatus}`])
        }
    }
}
