import { Pipe, PipeTransform } from '@angular/core';
import { calculateHoursFromMinutes } from './helper';

@Pipe({
  name: 'min2Hrs',
  standalone: true
})
export class Min2HrsPipe implements PipeTransform {

  transform(value: number, ...args: unknown[]): unknown {
    const { hours, remainingMinutes } = calculateHoursFromMinutes(value)

    if (hours != 0 || remainingMinutes != 0) {
      return `${hours}.${remainingMinutes.toString().padStart(2, "0")}`;
    } else {
      return '0.00'
    }
  }
}
