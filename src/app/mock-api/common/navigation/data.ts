/* eslint-disable */
import { FuseNavigationItem } from '@fuse/components/navigation';
import { environment } from 'environments/environment';

const BASE_VERSION = environment.version;

export const defaultNavigation: FuseNavigationItem[] = [
    {
        id: 'time-attendance.check-in',
        title: 'Check-In',
        type: 'basic',
        icon: 'heroicons_outline:map-pin',
        link: '/time-attendance/check-in',
        hidden: (item) => {
            var role = localStorage.getItem('role')
            if (role == 'EMP') {
                return false;
            } else {
                return true;
            }
        },
    },
    {
        id: 'admin',
        title: 'Admin',
        type: 'collapsable',
        icon: 'heroicons_outline:building-office-2',
        hidden: (item) => {
            const permission = localStorage.getItem('permission');
            const permissions = JSON.parse(permission);

            return !permissions.includes('ADMIN_USER_READ')
                || !permissions.includes('ADMIN_SITE_READ');
        },
        children: [
            {
                id: 'admin.user',
                title: 'User',
                icon: 'heroicons_outline:user',
                type: 'basic',
                link: '/user/list',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('ADMIN_USER_READ');
                },
            },
            {
                id: 'admin.check-in-area',
                title: 'Site',
                icon: 'heroicons_outline:building-office-2',
                type: 'basic',
                link: '/check-in-area/list',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('ADMIN_SITE_READ');
                },
            },
        ],
    },
    {
        id: 'hr',
        title: 'Human Resources',
        type: 'collapsable',
        icon: 'heroicons_outline:user-group',
        // hidden: (item) => {
        //     const permission = localStorage.getItem('permission');
        //     const permissions = JSON.parse(permission);

        //     return !permissions.includes('HR_EMPLOYEE_READ');
        // },
        children: [
            {
                id: 'employee',
                title: 'Employee',
                icon: 'heroicons_outline:user',
                type: 'basic',
                link: '/employee',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('HR_EMPLOYEE_READ');
                },
            },
            {
                id: 'hr.employee.setting',
                title: 'Employee Setting',
                icon: 'heroicons_outline:cog-8-tooth',
                type: 'collapsable',
                children: [
                    {
                        id: 'hr.employee.setting.employeee-type',
                        title: 'Employee Type',
                        type: 'basic',
                        link: '/employee-type/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_TYPE_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.title',
                        title: 'Title',
                        type: 'basic',
                        link: '/title/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_TITLE_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.group',
                        title: 'Group',
                        type: 'basic',
                        link: '/group/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_GROUP_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.Department',
                        title: 'Department',
                        type: 'basic',
                        link: '/department/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_DEPARTMENT_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.level',
                        title: 'Level',
                        type: 'basic',
                        link: '/level/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_LEVEL_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.level-type',
                        title: 'Level Type',
                        type: 'basic',
                        link: '/level-type',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_LEVEL_TYPE_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.approve',
                        title: 'Approver List',
                        type: 'basic',
                        link: '/approval-emp/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_APPROVE_LIST_READ');
                        },
                    },
                    {
                        id: 'hr.employee.setting.com-for-emp',
                        title: 'Company',
                        type: 'basic',
                        link: '/com-for-emp',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_EMPLOYEE_COMPANY_READ');
                        },
                    },
                ],
            },
            {
                id: 'admin-leave',
                title: 'Leave',
                icon: 'heroicons_outline:paper-airplane',
                type: 'basic',
                link: '/admin-leave/leave',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('HR_LEAVE_READ');
                },
            },
            {
                id: 'leave.setting',
                title: 'Leave Setting',
                icon: 'heroicons_outline:cog-8-tooth',
                type: 'collapsable',
                children: [
                    {
                        id: 'leave.setting.leave-type',
                        title: 'Leave Type',
                        type: 'basic',
                        link: '/leave-type/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_LEAVE_TYPE_READ');
                        },
                    },
                    {
                        id: 'leave.setting.work-shift',
                        title: 'Work Shift',
                        type: 'basic',
                        link: '/work-shift/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_WORK_SHIFT_READ');
                        },
                    },
                    {
                        id: 'leave.setting.company-holiday',
                        title: 'Company Holiday',
                        type: 'basic',
                        link: '/company-holiday/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('HR_COMPANY_HOLIDAY_READ');
                        },
                    },
                ],
            },
        ],
    },
    {
        id: 'Overtime',
        title: 'Overtime',
        type: 'collapsable',
        icon: 'heroicons_outline:clock',
        hidden: (item) => {
            const permission = localStorage.getItem('permission');
            const permissions = JSON.parse(permission);

            return !permissions.includes('OVERTIME_READ')
                || !permissions.includes('OVERTIME_AIR_READ')
                || !permissions.includes('OVERTIME_SETTING_READ')
                || !permissions.includes('OVERTIME_SETTING_ZONE_READ')
                || !permissions.includes('OVERTIME_SETTING_FLOOR_READ')
                || !permissions.includes('OVERTIME_SETTING_EMAIL_READ');
        },
        children: [
            {
                id: 'admin-ot',
                title: 'OT',
                icon: 'heroicons_outline:briefcase',
                type: 'basic',
                link: '/admin-ot/ot',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('OVERTIME_READ');
                },
            },
            {
                id: 'admin-ot-air',
                title: 'OT Air',
                icon: 'heroicons_outline:briefcase',
                type: 'basic',
                link: '/admin-ot-air/ot-air',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('OVERTIME_AIR_READ');
                },
            },
            {
                id: 'ot.setting',
                title: 'OT Setting',
                icon: 'heroicons_outline:cog-8-tooth',
                type: 'collapsable',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('OVERTIME_SETTING_READ')
                        || !permissions.includes('OVERTIME_SETTING_ZONE_READ')
                        || !permissions.includes('OVERTIME_SETTING_FLOOR_READ')
                        || !permissions.includes('OVERTIME_SETTING_EMAIL_READ');
                },
                children: [
                    {
                        id: 'leave.setting.zone',
                        title: 'Zone',
                        type: 'basic',
                        link: '/zone/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('OVERTIME_SETTING_ZONE_READ');
                        },
                    },
                    {
                        id: 'leave.setting.floor',
                        title: 'Floor',
                        type: 'basic',
                        link: '/floor/list',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('OVERTIME_SETTING_FLOOR_READ');
                        },
                    },
                    {
                        id: 'setting-email',
                        title: 'Setting Email',
                        type: 'basic',
                        link: '/admin-ot-air/otair-setting-form',
                        hidden: (item) => {
                            const permission = localStorage.getItem('permission');
                            const permissions = JSON.parse(permission);

                            return !permissions.includes('OVERTIME_SETTING_EMAIL_READ');
                        },
                    },
                ],
            },
            {
                id: 'leave.setting.project',
                title: 'Project',
                icon: 'heroicons_outline:document-text',
                type: 'basic',
                link: '/project/list',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('PROJECT_READ');
                },
            },
        ],
    },
    {
        id: 'report',
        title: 'Report',
        type: 'collapsable',
        icon: 'heroicons_outline:document-duplicate',
        hidden: (item) => {
            const permission = localStorage.getItem('permission');
            const permissions = JSON.parse(permission);

            return !permissions.includes('REPORT_LEAVE_READ')
                || !permissions.includes('REPORT_TIME_ATTENDANCE_READ')
                || !permissions.includes('REPORT_OVERTIME_READ')
                || !permissions.includes('REPORT_OVERTIME_AIR_READ')
                || !permissions.includes('REPORT_CLIENT_LIST_READ');
        },
        children: [
            {
                id: 'report.leave',
                title: 'Leave',
                icon: 'heroicons_outline:document',
                type: 'collapsable',
                children: [
                    {
                        id: 'report.leave.document.accumulate-staff',
                        title: 'Accumulate Staff',
                        type: 'basic',
                        link: '/report/accumulate-staff',
                    },
                    {
                        id: 'report.leave.document.detail-accumulate-staff',
                        title: 'Detail Accumulate Staff',
                        type: 'basic',
                        link: '/report/accumulate-staff-detail',
                    },
                ],
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('REPORT_LEAVE_READ');
                },
            },
            {
                id: 'report.time-attendance',
                title: 'Time-Attendance',
                icon: 'heroicons_outline:document',
                type: 'collapsable',
                children: [
                    {
                        id: 'report.time-attendance.document.view-daily-raw-transactions',
                        title: 'View Daily Raw Transactions',
                        type: 'basic',
                        link: '/report/view-daily-raw-transactions',
                    },
                    {
                        id: 'report.time-attendance.document.view-daily-raw-transactions',
                        title: 'View Daily Check In',
                        type: 'basic',
                        link: '/report/view-daily-raw-transactions-check-in',
                    },
                    {
                        id: 'report.time-attendance.document.time-attendance-view-by-user',
                        title: 'Time Attendance View By User',
                        type: 'basic',
                        link: '/report/time-attendance-view-by-user',
                    },
                    {
                        id: 'report.time-attendance.document.daily-attendance-report',
                        title: 'Daily Attendance Report',
                        type: 'basic',
                        link: '/report/daily-attendance-report',
                    },
                    {
                        id: 'report.time-attendance.document.monthly-attendance-report',
                        title: 'Monthly Attendance Report',
                        type: 'basic',
                        link: '/report/monthly-attendance-report',
                    },
                    {
                        id: 'report.time-attendance.document.summary-deeuction-report',
                        title: 'Summary Deduction Report',
                        type: 'basic',
                        link: '/report/summary-deduction-report',
                    },
                ],
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('REPORT_TIME_ATTENDANCE_READ');
                },
            },
            {
                id: 'report.overtime',
                title: 'Overtime',
                icon: 'heroicons_outline:document',
                type: 'collapsable',
                children: [
                    {
                        id: 'report.overtime.request',
                        title: 'OT Request',
                        type: 'basic',
                        link: '/report/ot-request-report',
                    },
                    {
                        id: 'report.overtime.byteam',
                        title: 'OT Request Report Group By Team',
                        type: 'basic',
                        link: '/report/ot-request-byteam-report',
                    },
                    {
                        id: 'report.overtime.bystaff',
                        title: 'OT & TA comparison By Staff',
                        type: 'basic',
                        link: '/report/ot-request-bystaff-report',
                    },
                    {
                        id: 'report.overtime.byowner',
                        title: 'View Request By Project Owner',
                        type: 'basic',
                        link: '/report/ot-request-byowner-report',
                    },
                    {
                        id: 'report.overtime.view-pending-request',
                        title: 'View Pending Request',
                        type: 'basic',
                        link: '/report/view-pending-request-report',
                    },
                ],
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('REPORT_OVERTIME_READ');
                },
            },
            {
                id: 'report.overtime-air',
                title: 'Overtime Air',
                icon: 'heroicons_outline:document',
                type: 'collapsable',
                children: [
                    {
                        id: 'report.overtime-air.request',
                        title: 'OT Air Request',
                        type: 'basic',
                        link: '/report/ot-air-request-report',
                    },

                ],
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('REPORT_OVERTIME_AIR_READ');
                },
            },
            {
                id: 'report.client-list',
                title: 'Client List',
                icon: 'heroicons_outline:document',
                type: 'collapsable',
                children: [
                    {
                        id: 'report.client-list.list',
                        title: 'Customer List',
                        type: 'basic',
                        link: '/report/contact',
                    },
                ],
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('REPORT_CLIENT_LIST_READ');
                },
            },
        ],
    },
    {
        id: 'hr.leave',
        title: 'Leave',
        type: 'collapsable',
        icon: 'heroicons_outline:paper-airplane',
        children: [
            {
                id: 'leave.user',
                title: 'User',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'leave.user.remaining',
                        title: 'Leave Summary & Status',
                        type: 'basic',
                        link: '/leave/leave-remainning',
                    },
                    {
                        id: 'leave.user.Request.form',
                        title: 'Leave Request',
                        type: 'basic',
                        link: '/leave/leave-application-form',
                    },
                ],
            },
            {
                id: 'leave.user.report',
                title: 'User Report',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'leave.user.report.taken',
                        title: 'List of Leave Taken',
                        type: 'basic',
                        link: '/leave/leave-list',
                    },
                ],
            },
            {
                id: 'leave.approver',
                title: 'Approver',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'leave.approval.approved',
                        title: 'Pending Request',
                        type: 'basic',
                        link: '/leave/leave-approval',
                    },
                    {
                        id: 'leave.approval.list-of-approver',
                        title: 'Summary of member leave request',
                        type: 'basic',
                        link: '/leave/leave-list-of-approver',
                    },
                ],
                hidden: (item) => {
                    var role = localStorage.getItem('role')
                    var approver = JSON.parse(localStorage.getItem('user'))
                    if (role == 'EMP' && approver.isApprover === true) {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
        ],
        hidden: (item) => {
            var role = localStorage.getItem('role')
            if (role == 'EMP') {
                return false;
            } else {
                return true;
            }
        },
    },
    {
        id: 'hr.ot',
        title: 'OT',
        type: 'collapsable',
        icon: 'heroicons_outline:briefcase',

        children: [
            {
                id: 'ot.user',
                title: 'User',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'ot.user.list',
                        title: 'Overtime History',
                        type: 'basic',
                        link: '/ot/ot-list',
                    },
                    {
                        id: 'ot.user.Request.form',
                        title: 'OT Request',
                        type: 'basic',
                        link: '/ot/ot-application-form',
                    },
                    {
                        id: 'otair.user.list',
                        title: 'Overtime Air History',
                        type: 'basic',
                        link: '/ot-air/ot-air-list',
                    },
                    {
                        id: 'otair.user.Request.form',
                        title: 'OT Air Request',
                        type: 'basic',
                        link: '/ot-air/ot-air-application-form',
                    },
                ],
            },
            {
                id: 'ot.review',
                title: 'Review',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'ot.review.reviewed',
                        title: 'OT Review',
                        type: 'basic',
                        link: '/ot/ot-review',
                    },
                ],
                hidden: (item) => {
                    var role = localStorage.getItem('role')
                    var approver = JSON.parse(localStorage.getItem('user'))
                    if (role == 'EMP' && approver.isApprover === true) {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
            {
                id: 'ot.approver',
                title: 'Approver',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'ot.approval.approved',
                        title: 'OT Pending Request',
                        type: 'basic',
                        link: '/ot/ot-approval',
                    },
                    {
                        id: 'otair.approval.approved',
                        title: 'OT Air Pending Request',
                        type: 'basic',
                        link: '/ot-air/ot-air-approval',
                    },
                ],
                hidden: (item) => {
                    var role = localStorage.getItem('role')
                    var approver = JSON.parse(localStorage.getItem('user'))
                    if (role == 'EMP' && approver.isApprover === true) {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
        ],

        hidden: (item) => {
            var role = localStorage.getItem('role')
            if (role == 'EMP') {
                return false;
            } else {
                return true;
            }
        },
    },
    {
        id: 'hr.time-attendance',
        title: 'Time Attendance',
        type: 'collapsable',
        icon: 'heroicons_outline:clock',
        // hidden: (item) => {
        //     // var role = localStorage.getItem('role')
        //     // const roles = role.split(',');
        //     // if (role.includes('EMP')) {
        //     //     return false;
        //     // } else if (roles.includes('ADMIN') || roles.includes('ADMIN_HR') || roles.includes('HR')) {
        //     //     return false;
        //     // } else {
        //     //     return true;
        //     // }

        //     const permission = localStorage.getItem('permission');
        //     const permissions = JSON.parse(permission);

        //     return !permissions.includes('TIME_ATTENDANCE_ADJUST_DEPART_REVIEW');
        // },
        children: [
            {
                id: 'report.time-attendance.document.adjust-depart-review',
                title: 'Adjust Depart Head/Team lead Review',
                type: 'basic',
                link: '/time-attendance/list-admin-review',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('TIME_ATTENDANCE_ADJUST_DEPART_REVIEW');
                },
            },
            {
                id: 'time-attendance.user',
                title: 'User',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'time-attendance.user.history',
                        title: 'Review Time Attendance',
                        type: 'basic',
                        link: '/time-attendance/history',
                        hidden: (item) => {
                            var role = localStorage.getItem('role')
                            if (role == 'EMP') {
                                return false;
                            } else {
                                return true;
                            }
                        },
                    },
                ],
                hidden: (item) => {
                    var role = localStorage.getItem('role')
                    if (role == 'EMP') {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
            {
                id: 'time-attendance.user-report',
                title: 'Attendance Record',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'report.time-attendance.document.time-attendance-view-by-user-view',
                        title: 'History',
                        type: 'basic',
                        link: '/report/time-attendance-view-by-user-view',
                    },
                ],
                hidden: (item) => {
                    var role = localStorage.getItem('role')
                    if (role == 'EMP') {
                        return false;
                    } else {
                        return true;
                    }
                },
            },
            {
                id: 'time-attendance.approver',
                title: 'Reviewer',
                type: 'collapsable',
                // link : '/example'
                children: [
                    {
                        id: 'time-attendance.approver.staff-review',
                        title: 'Team Lead Review',
                        type: 'basic',
                        link: '/time-attendance/list-team-lead-review',
                    },
                ],
                hidden: (item) => {
                    var role = localStorage.getItem('role')
                    var approver = JSON.parse(localStorage.getItem('user'))

                    if (role == 'EMP' && approver.isApprover === true) {
                        return false;
                    } else {
                        return true;
                    }
                },
            },

        ],
    },
    {
        id: 'client-list',
        title: 'Client List',
        type: 'collapsable',
        hidden: (item) => {
            const permission = localStorage.getItem('permission');
            const permissions = JSON.parse(permission);

            return !permissions.includes('CLIENT_LIST_COMPANY_READ')
                || !permissions.includes('CLIENT_LIST_CONTACT_READ')
                || !permissions.includes('CLIENT_LIST_SETTING_READ');
        },
        icon: 'heroicons_outline:user-group',
        children: [
            {
                id: 'client-list.company',
                title: 'Company',
                type: 'basic',
                link: '/client-list/company',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('CLIENT_LIST_COMPANY_READ');
                },
            },
            {
                id: 'client-list.contact',
                title: 'Customer',
                type: 'basic',
                link: '/client-list/contact',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('CLIENT_LIST_CONTACT_READ');
                },
            },
            {
                id: 'client-list.setting',
                title: 'Setting',
                type: 'collapsable',
                hidden: (item) => {
                    const permission = localStorage.getItem('permission');
                    const permissions = JSON.parse(permission);

                    return !permissions.includes('CLIENT_LIST_SETTING_READ');
                },
                children: [
                    {
                        id: 'client-list.setting.category',
                        title: 'Category',
                        type: 'basic',
                        link: '/client-list/category',
                    },
                    {
                        id: 'client-list.setting.activity',
                        title: 'Activity',
                        type: 'basic',
                        link: '/client-list/activity',
                    },
                ],
            },
        ],
    },
    {
        id: 'recruitment',
        title: 'Recruitment',
        type: 'collapsable',
        hidden: (item) => {
            var role = localStorage.getItem('role')
            // if(!BASE_VERSION) {
            //     return true
            // }
            if (role == 'EMP') {
                return false;
            } else {
                return true;
            }
        },
        icon: 'heroicons_outline:user-group',
        children: [
            {
                id: 'personnel-request',
                title: 'Personnel Request',
                type: 'basic',
                link: '/personnel-request-emp'
            },
        ],
    },
    {
        id: 'recruitment-1',
        title: 'Recruitment',
        type: 'collapsable',
        hidden: (item) => {
            const permission = localStorage.getItem('permission');
            const permissions = JSON.parse(permission);

            return !permissions.includes('RECRUITMENT_READ');
        },
        icon: 'heroicons_outline:user-group',
        children: [
            {
                id: 'recruitment.job',
                title: 'Job',
                type: 'basic',
                link: '/recruitment/job'
            },
            {
                id: 'applicant',
                title: 'Applicant',
                type: 'basic',
                link: '/applicant'
            },
            {
                id: 'personnel-request',
                title: 'Personnel Request',
                type: 'basic',
                link: '/personnel-request'
            },
            {
                id: 'job-applicants',
                title: 'Job Applicants',
                type: 'basic',
                link: '/job-applicants'
            }
        ],
    }
];
export const compactNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
    },
];
export const futuristicNavigation: FuseNavigationItem[] = [
    {
        id: 'example',
        title: 'Example',
        type: 'basic',
        icon: 'heroicons_outline:chart-pie',
    },
];
export const horizontalNavigation: FuseNavigationItem[] = [];
