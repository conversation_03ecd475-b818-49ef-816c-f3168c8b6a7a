fuse-alert {
    display: block;

    /* Common */
    .fuse-alert-container {
        position: relative;
        display: flex;
        padding: 16px;
        font-size: 14px;
        line-height: 1;

        /* All icons */
        .mat-icon {
            color: currentColor !important;
        }

        /* Icon */
        .fuse-alert-icon {
            display: flex;
            align-items: flex-start;

            .fuse-alert-custom-icon,
            .fuse-alert-default-icon {
                display: none;
                align-items: center;
                justify-content: center;
                border-radius: 50%;

                &:not(:empty) {
                    display: flex;
                    margin-right: 12px;
                }
            }

            .fuse-alert-default-icon {
                .mat-icon {
                    @apply icon-size-5;
                }
            }

            .fuse-alert-custom-icon {
                display: none;

                &:not(:empty) {
                    display: flex;

                    + .fuse-alert-default-icon {
                        display: none;
                    }
                }
            }
        }

        /* Content */
        .fuse-alert-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            line-height: 1;

            /* Title */
            .fuse-alert-title {
                display: none;
                font-weight: 600;
                line-height: 20px;

                &:not(:empty) {
                    display: block;

                    /* <PERSON>ert that comes after the title */
                    + .fuse-alert-message {
                        &:not(:empty) {
                            margin-top: 4px;
                        }
                    }
                }
            }

            /* Alert */
            .fuse-alert-message {
                display: none;
                line-height: 20px;

                &:not(:empty) {
                    display: block;
                }
            }
        }

        /* Dismiss button */
        .fuse-alert-dismiss-button {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 32px !important;
            min-width: 32px !important;
            height: 32px !important;
            min-height: 32px !important;
            line-height: 32px !important;

            .mat-icon {
                @apply icon-size-4;
            }
        }
    }

    /* Dismissible */
    &.fuse-alert-dismissible {
        .fuse-alert-container {
            .fuse-alert-content {
                margin-right: 32px;
            }
        }
    }

    &:not(.fuse-alert-dismissible) {
        .fuse-alert-container {
            .fuse-alert-dismiss-button {
                display: none !important;
            }
        }
    }

    /* Border */
    &.fuse-alert-appearance-border {
        .fuse-alert-container {
            position: relative;
            overflow: hidden;
            border-radius: 6px;
            @apply bg-card shadow-md;

            .fuse-alert-border {
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 4px;
            }

            .fuse-alert-message {
                @apply text-gray-600;
            }
        }

        /* Primary */
        &.fuse-alert-type-primary {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-primary;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-primary;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-primary-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-primary-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Accent */
        &.fuse-alert-type-accent {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-accent;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-accent;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-accent-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-accent-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Warn */
        &.fuse-alert-type-warn {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-warn;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-warn;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-warn-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-warn-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Basic */
        &.fuse-alert-type-basic {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-gray-600;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-gray-600;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-gray-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-gray-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Info */
        &.fuse-alert-type-info {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-blue-600;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-blue-700;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-blue-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-blue-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Success */
        &.fuse-alert-type-success {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-green-500;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-green-500;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-green-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-green-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Warning */
        &.fuse-alert-type-warning {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-amber-500;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-amber-500;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-amber-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-amber-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }

        /* Error */
        &.fuse-alert-type-error {
            .fuse-alert-container {
                .fuse-alert-border {
                    @apply bg-red-600;
                }

                .fuse-alert-title,
                .fuse-alert-icon {
                    @apply text-red-700;
                }

                .dark & {
                    @apply bg-gray-700;

                    .fuse-alert-border {
                        @apply bg-red-400;
                    }

                    .fuse-alert-title,
                    .fuse-alert-icon {
                        @apply text-red-400;
                    }

                    .fuse-alert-message {
                        @apply text-gray-300;
                    }

                    code {
                        @apply bg-gray-400 text-gray-800;
                    }
                }
            }
        }
    }

    /* Fill */
    &.fuse-alert-appearance-fill {
        .fuse-alert-container {
            border-radius: 6px;

            .fuse-alert-dismiss-button {
                @apply text-white;
            }
        }

        /* Primary */
        &.fuse-alert-type-primary {
            .fuse-alert-container {
                @apply bg-primary-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-primary-100;
                }

                code {
                    @apply bg-primary-200 text-primary-800;
                }
            }
        }

        /* Accent */
        &.fuse-alert-type-accent {
            .fuse-alert-container {
                @apply bg-accent-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-accent-100;
                }

                code {
                    @apply bg-accent-200 text-accent-800;
                }
            }
        }

        /* Warn */
        &.fuse-alert-type-warn {
            .fuse-alert-container {
                @apply bg-warn-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-warn-100;
                }

                code {
                    @apply bg-warn-200 text-warn-800;
                }
            }
        }

        /* Basic */
        &.fuse-alert-type-basic {
            .fuse-alert-container {
                @apply bg-gray-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-gray-100;
                }

                code {
                    @apply bg-gray-200 text-gray-800;
                }
            }
        }

        /* Info */
        &.fuse-alert-type-info {
            .fuse-alert-container {
                @apply bg-blue-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-blue-100;
                }

                code {
                    @apply bg-blue-200 text-blue-800;
                }
            }
        }

        /* Success */
        &.fuse-alert-type-success {
            .fuse-alert-container {
                @apply bg-green-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-green-100;
                }

                code {
                    @apply bg-green-200 text-gray-800;
                }
            }
        }

        /* Warning */
        &.fuse-alert-type-warning {
            .fuse-alert-container {
                @apply bg-amber-500;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-amber-100;
                }

                code {
                    @apply bg-amber-200 text-amber-800;
                }
            }
        }

        /* Error */
        &.fuse-alert-type-error {
            .fuse-alert-container {
                @apply bg-red-600;

                .fuse-alert-icon {
                    @apply text-white;
                }

                .fuse-alert-title {
                    @apply text-white;
                }

                .fuse-alert-message {
                    @apply text-red-100;
                }

                code {
                    @apply bg-red-200 text-red-800;
                }
            }
        }
    }

    /* Outline */
    &.fuse-alert-appearance-outline {
        .fuse-alert-container {
            border-radius: 6px;
        }

        /* Primary */
        &.fuse-alert-type-primary {
            .fuse-alert-container {
                @apply bg-primary-50 ring-1 ring-inset ring-primary-400;

                .fuse-alert-icon {
                    @apply text-primary-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-primary-900;
                }

                .fuse-alert-message {
                    @apply text-primary-700;
                }

                code {
                    @apply bg-primary-200 text-primary-800;
                }

                .dark & {
                    @apply bg-primary-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-primary-200;
                    }
                }
            }
        }

        /* Accent */
        &.fuse-alert-type-accent {
            .fuse-alert-container {
                @apply bg-accent-100 ring-1 ring-inset ring-accent-400;

                .fuse-alert-icon {
                    @apply text-accent-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-accent-900;
                }

                .fuse-alert-message {
                    @apply text-accent-700;
                }

                code {
                    @apply bg-accent-200 text-accent-800;
                }

                .dark & {
                    @apply bg-accent-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-accent-200;
                    }
                }
            }
        }

        /* Warn */
        &.fuse-alert-type-warn {
            .fuse-alert-container {
                @apply bg-warn-50 ring-1 ring-inset ring-warn-400;

                .fuse-alert-icon {
                    @apply text-warn-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-warn-900;
                }

                .fuse-alert-message {
                    @apply text-warn-700;
                }

                code {
                    @apply bg-warn-200 text-warn-800;
                }

                .dark & {
                    @apply bg-warn-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-warn-200;
                    }
                }
            }
        }

        /* Basic */
        &.fuse-alert-type-basic {
            .fuse-alert-container {
                @apply bg-gray-100 ring-1 ring-inset ring-gray-400;

                .fuse-alert-icon {
                    @apply text-gray-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-gray-900;
                }

                .fuse-alert-message {
                    @apply text-gray-700;
                }

                code {
                    @apply bg-gray-200 text-gray-800;
                }

                .dark & {
                    @apply bg-gray-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-gray-200;
                    }
                }
            }
        }

        /* Info */
        &.fuse-alert-type-info {
            .fuse-alert-container {
                @apply bg-blue-50 ring-1 ring-inset ring-blue-400;

                .fuse-alert-icon {
                    @apply text-blue-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-blue-900;
                }

                .fuse-alert-message {
                    @apply text-blue-700;
                }

                code {
                    @apply bg-blue-200 text-blue-800;
                }

                .dark & {
                    @apply bg-blue-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-blue-200;
                    }
                }
            }
        }

        /* Success */
        &.fuse-alert-type-success {
            .fuse-alert-container {
                @apply bg-green-50 ring-1 ring-inset ring-green-400;

                .fuse-alert-icon {
                    @apply text-green-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-green-900;
                }

                .fuse-alert-message {
                    @apply text-green-700;
                }

                code {
                    @apply bg-green-200 text-green-800;
                }

                .dark & {
                    @apply bg-green-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-green-200;
                    }
                }
            }
        }

        /* Warning */
        &.fuse-alert-type-warning {
            .fuse-alert-container {
                @apply bg-amber-50 ring-1 ring-inset ring-amber-400;

                .fuse-alert-icon {
                    @apply text-amber-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-amber-900;
                }

                .fuse-alert-message {
                    @apply text-amber-700;
                }

                code {
                    @apply bg-amber-200 text-amber-800;
                }

                .dark & {
                    @apply bg-amber-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-amber-200;
                    }
                }
            }
        }

        /* Error */
        &.fuse-alert-type-error {
            .fuse-alert-container {
                @apply bg-red-50 ring-1 ring-inset ring-red-400;

                .fuse-alert-icon {
                    @apply text-red-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-red-900;
                }

                .fuse-alert-message {
                    @apply text-red-700;
                }

                code {
                    @apply bg-red-200 text-red-800;
                }

                .dark & {
                    @apply bg-red-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-red-200;
                    }
                }
            }
        }
    }

    /* Soft */
    &.fuse-alert-appearance-soft {
        .fuse-alert-container {
            border-radius: 6px;
        }

        /* Primary */
        &.fuse-alert-type-primary {
            .fuse-alert-container {
                @apply bg-primary-50;

                .fuse-alert-icon {
                    @apply text-primary-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-primary-900;
                }

                .fuse-alert-message {
                    @apply text-primary-700;
                }

                code {
                    @apply bg-primary-200 text-primary-800;
                }

                .dark & {
                    @apply bg-primary-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-primary-200;
                    }
                }
            }
        }

        /* Accent */
        &.fuse-alert-type-accent {
            .fuse-alert-container {
                @apply bg-accent-100;

                .fuse-alert-icon {
                    @apply text-accent-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-accent-900;
                }

                .fuse-alert-message {
                    @apply text-accent-700;
                }

                code {
                    @apply bg-accent-200 text-accent-800;
                }

                .dark & {
                    @apply bg-accent-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-accent-200;
                    }
                }
            }
        }

        /* Warn */
        &.fuse-alert-type-warn {
            .fuse-alert-container {
                @apply bg-warn-50;

                .fuse-alert-icon {
                    @apply text-warn-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-warn-900;
                }

                .fuse-alert-message {
                    @apply text-warn-700;
                }

                code {
                    @apply bg-warn-200 text-warn-800;
                }

                .dark & {
                    @apply bg-warn-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-warn-200;
                    }
                }
            }
        }

        /* Basic */
        &.fuse-alert-type-basic {
            .fuse-alert-container {
                @apply bg-gray-100;

                .fuse-alert-icon {
                    @apply text-gray-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-gray-900;
                }

                .fuse-alert-message {
                    @apply text-gray-700;
                }

                code {
                    @apply bg-gray-200 text-gray-800;
                }

                .dark & {
                    @apply bg-gray-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-gray-200;
                    }
                }
            }
        }

        /* Info */
        &.fuse-alert-type-info {
            .fuse-alert-container {
                @apply bg-blue-50;

                .fuse-alert-icon {
                    @apply text-blue-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-blue-900;
                }

                .fuse-alert-message {
                    @apply text-blue-700;
                }

                code {
                    @apply bg-blue-200 text-blue-800;
                }

                .dark & {
                    @apply bg-blue-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-blue-200;
                    }
                }
            }
        }

        /* Success */
        &.fuse-alert-type-success {
            .fuse-alert-container {
                @apply bg-green-50;

                .fuse-alert-icon {
                    @apply text-green-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-green-900;
                }

                .fuse-alert-message {
                    @apply text-green-700;
                }

                code {
                    @apply bg-green-200 text-green-800;
                }

                .dark & {
                    @apply bg-green-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-green-200;
                    }
                }
            }
        }

        /* Warning */
        &.fuse-alert-type-warning {
            .fuse-alert-container {
                @apply bg-amber-50;

                .fuse-alert-icon {
                    @apply text-amber-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-amber-900;
                }

                .fuse-alert-message {
                    @apply text-amber-700;
                }

                code {
                    @apply bg-amber-200 text-amber-800;
                }

                .dark & {
                    @apply bg-amber-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-amber-200;
                    }
                }
            }
        }

        /* Error */
        &.fuse-alert-type-error {
            .fuse-alert-container {
                @apply bg-red-50;

                .fuse-alert-icon {
                    @apply text-red-600;
                }

                .fuse-alert-title,
                .fuse-alert-dismiss-button {
                    @apply text-red-900;
                }

                .fuse-alert-message {
                    @apply text-red-700;
                }

                code {
                    @apply bg-red-200 text-red-800;
                }

                .dark & {
                    @apply bg-red-600;

                    .fuse-alert-icon {
                        @apply text-white;
                    }

                    .fuse-alert-title,
                    .fuse-alert-dismiss-button {
                        @apply text-white;
                    }

                    .fuse-alert-message {
                        @apply text-red-200;
                    }
                }
            }
        }
    }
}
